<div class="container mt-4">
  <div class="card">
    <div class="card-header">
      過去考題搜尋結果
    </div>
    <div class="card-body">
      <div class="alert alert-info">
        <strong>搜尋條件：</strong> 
        學校: {{ searchParams.school || '全部' }} | 
        年度: {{ searchParams.year || '全部' }} | 
        科目: {{ searchParams.subject || '全部' }}
      </div>
      
      <!-- 顯示沒有搜尋條件時的提示 -->
      <p *ngIf="!searchParams.school && !searchParams.year && !searchParams.subject">
        請從選擇頁面設定搜尋條件
      </p>
      
      <!-- 顯示沒有資料的提示 -->
      <div *ngIf="examData.length === 0 && (searchParams.school || searchParams.year || searchParams.subject)" class="alert alert-warning">
        沒有找到符合條件的考題
      </div>
      
      <!-- 顯示考題列表 -->
      <div *ngIf="examData.length > 0" class="mt-3">
        <h5>找到 {{ examData.length }} 筆考題資料</h5>
        
        <div *ngFor="let exam of examData; let i = index" class="card mb-3">
          <div class="card-header d-flex justify-content-between align-items-center">
            <span>
              <strong>{{ exam.school }}</strong> | {{ exam.year }}年 | {{ exam.predicted_category }}
            </span>
            <span class="badge bg-primary">{{ exam.type === 'single-choice' ? '單選題' : exam.type }}</span>
          </div>
          <div class="card-body">
            <h5 class="card-title">第 {{ exam.question_number }} 題</h5>
            <p class="card-text">{{ exam.question_text }}</p>
            
            <!-- 顯示選項 -->
            <div class="mt-3" *ngIf="exam.options && exam.options.length > 0">
              <p class="fw-bold mb-2">選項：</p>
              <div class="ms-2">
                <div *ngFor="let option of exam.options" class="mb-1">
                  {{ option }}
                </div>
              </div>
            </div>
            
            <div class="mt-3 d-flex justify-content-between">
              <div class="text-muted small">科系：{{ exam.department }}</div>
              <div>
                <span class="badge"
                  [ngClass]="{
                    'bg-primary': exam.type === 'single-choice',
                    'bg-success': exam.type === 'multiple-choice',
                    'bg-warning': exam.type === 'true-false',
                    'bg-info': exam.type === 'short-answer'
                  }">
                  {{ 
                    exam.type === 'single-choice' ? '單選題' : 
                    exam.type === 'multiple-choice' ? '多選題' : 
                    exam.type === 'true-false' ? '是非題' : 
                    exam.type === 'short-answer' ? '簡答題' : exam.type 
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
