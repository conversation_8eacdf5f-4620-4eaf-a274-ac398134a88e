#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版智能教師 - 統一prompt，減少複雜性
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import requests
import json
from rag_ai_responder import AIResponder
import config

class CleanTutor:
    """簡化版智能教師"""
    
    def __init__(self):
        """初始化教師"""
        # 關閉所有日誌
        import logging
        logging.getLogger('rag_ai_responder').setLevel(logging.CRITICAL)
        logging.getLogger('rag_processor').setLevel(logging.CRITICAL)
        logging.getLogger('sentence_transformers').setLevel(logging.CRITICAL)
        logging.getLogger('chromadb').setLevel(logging.CRITICAL)
        logging.getLogger('transformers').setLevel(logging.CRITICAL)
        
        self.ai_responder = AIResponder(language='chinese')
        self.ai_base_url = config.AI_CONFIG['base_url']
        self.ai_model = config.AI_CONFIG['model']
        
        # 核心變數
        self.original_question = ""  # 主問題
        self.context = ""  # 對話上下文
        self.topic_knowledge = ""  # 主題知識
        
        # 統一的教學風格提示詞
        self.TEACHER_STYLE = """
你是一位經驗豐富的資管系教授，正在一對一輔導學生，需要採用引導方式教學。

**教學風格**：
- 語氣親切自然，像真正的老師在身邊
- 補充學生可能遺漏的關鍵概念
- 用生活化例子幫助理解抽象概念
- 循序漸進，不急於給出完整答案

**回應結構**：
1. 針對目前題目做延伸深入，提出一個引導性問題，幫助學生更理解觀念

**重要原則**：
- 不要用格式化標題（如**評價學生回答**）
- 語氣要自然，像在聊天
- 始終圍繞原始問題進行教學
- 每次回應都要推進學生的理解
"""
        
        print("🎓 簡化版智能教師已啟動")
    
    def get_topic_knowledge(self, question: str) -> str:
        """獲取主題相關知識"""
        try:
            # 翻譯成英文搜索
            english_question = self._translate_to_english(question)
            search_results = self.ai_responder.search_knowledge(english_question)
            
            if search_results:
                # 提取前3個結果的內容
                knowledge = "\n".join([
                    result.get('content', '')[:400] 
                    for result in search_results[:3]
                ])
                return knowledge
        except:
            pass
        return ""
    
    def _translate_to_english(self, text: str) -> str:
        """翻譯成英文"""
        try:
            prompt = f"Translate to English: {text}"
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.1, "num_predict": 50}
                },
                timeout=10
            )
            if response.status_code == 200:
                return response.json().get('response', '').strip()
        except:
            pass
        return text
    
    def ask_ai(self, student_input: str, is_new_question: bool = False) -> str:
        """統一的AI回應函式"""
        if is_new_question:
            # 新問題
            prompt = f"""{self.TEACHER_STYLE}

原始問題：{student_input}

教材知識：
{self.topic_knowledge}

請開始教學，先根據教材簡單解釋概念，然後提出引導性問題。
"""
        else:
            # 學生回答
            prompt = f"""{self.TEACHER_STYLE}

原始問題：{self.original_question}
學生回答：{student_input}

對話歷史：
{self.context}

請針對學生的回答進行引導教學。
"""
        
        return self._call_ai(prompt)
    
    def _call_ai(self, prompt: str) -> str:
        """調用AI"""
        try:
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.4,
                        "num_predict": 200,
                        "top_p": 0.8,
                        "repeat_penalty": 1.2
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                ai_response = response.json().get('response', '').strip()
                if ai_response and len(ai_response) > 10:
                    return ai_response
                else:
                    return "讓我們繼續探討這個概念。"
            else:
                return "抱歉，讓我重新組織一下思路。"
        except Exception as e:
            return "請稍等，讓我重新思考一下。"
    
    def start_new_question(self, question: str) -> str:
        """開始新問題"""
        # 重置狀態
        self.original_question = question
        self.context = ""
        
        # 獲取主題知識
        self.topic_knowledge = self.get_topic_knowledge(question)
        
        # 生成首次回應
        response = self.ask_ai(question, is_new_question=True)
        
        # 更新上下文
        self.context = f"學生問：{question}\n老師：{response}"
        
        return response
    
    def continue_conversation(self, student_answer: str) -> str:
        """繼續對話"""
        if not self.original_question:
            return "請先提出一個問題開始學習。"
        
        # 生成回應
        response = self.ask_ai(student_answer, is_new_question=False)
        
        # 更新上下文
        self.context += f"\n\n學生：{student_answer}\n老師：{response}"
        
        # 保持上下文在合理長度
        if len(self.context) > 1500:
            # 保留最近3輪完整對話
            parts = self.context.split('\n\n')
            if len(parts) > 6:  # 3輪對話 = 6個部分
                self.context = '\n\n'.join(parts[-6:])
        
        return response
    
    def get_status(self) -> str:
        """獲取當前狀態"""
        return f"""
📊 學習狀態
原始問題: {self.original_question or '無'}
對話輪數: {self.context.count('學生：')}
上下文長度: {len(self.context)} 字符
"""
    
    def reset(self):
        """重置對話"""
        self.original_question = ""
        self.context = ""
        self.topic_knowledge = ""
