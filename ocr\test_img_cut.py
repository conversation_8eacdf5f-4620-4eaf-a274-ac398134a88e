from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os

# 字型設定放在最前
plt.rcParams['font.family'] = 'Microsoft JhengHei'  # 或 'Noto Sans CJK TC'

def show_image_with_regions(image_path, regions):
    image = Image.open(image_path)

    fig, ax = plt.subplots(1)
    ax.imshow(image)

    for region in regions:
        rect = patches.Rectangle(
            (region["x"], region["y"]),
            region["width"],
            region["height"],
            linewidth=2,
            edgecolor='red',
            facecolor='none'
        )
        ax.add_patch(rect)

    plt.title("題目區塊標記")
    plt.show()

# 正確格式：一張圖片配一組 image_regions
data = {
    "image_file": [
        "113-資訊管理概論-私立輔仁大學-資訊管理學系碩士班_page_2.png",
        "112-計算機概論-國立政治大學-資訊管理學系碩士班科技組_page_2.png"
    ],
    "image_regions": [
        [  # 第一張圖片的區塊
            {
                "x": 100,#180
                "y": 250,#310
                "width": 200,#230
                "height": 250#140
            },
            {
                "x": 300,#320
                "y": 250,#310
                "width": 250,#180
                "height": 250#140
            }
        ],
        [  # 第二張圖片的區塊
            {"x": 75, "y": 580, "width": 100, "height": 100},
            {"x": 270, "y": 580, "width": 100, "height": 100}
        ]
    ]
}

image_folder = "exam_img"

# 顯示所有圖片與其對應區塊
for idx, image_file in enumerate(data["image_file"]):
    image_path = os.path.join(image_folder, image_file)
    print(f"🖼️ 顯示圖片：{image_file}")
    show_image_with_regions(image_path, data["image_regions"][idx])
