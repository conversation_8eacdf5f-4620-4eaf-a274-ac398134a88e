#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的教學系統
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from smart_tutor import SmartTutor

def test_conversation():
    """測試對話流程"""
    print("🧪 測試修復後的對話流程")
    print("="*50)
    
    tutor = SmartTutor()
    
    # 模擬問題和回答
    print("🤔 學生問題: 什麼是銀行家演算法？")
    response1 = tutor.generate_smart_response("什麼是銀行家演算法？", is_new_question=True)
    print(f"🎓 老師回應: {response1}")
    tutor.record_conversation("什麼是銀行家演算法？", response1)
    
    print("\n💭 學生回答: 死鎖指系統陷入僵局")
    response2 = tutor.generate_smart_response("死鎖指系統陷入僵局", is_new_question=False)
    print(f"🎓 老師回應: {response2}")
    tutor.record_conversation("死鎖指系統陷入僵局", response2)
    
    print("\n💭 學生回答: 那什麼是死鎖")
    response3 = tutor.generate_smart_response("那什麼是死鎖", is_new_question=False)
    print(f"🎓 老師回應: {response3}")
    tutor.record_conversation("那什麼是死鎖", response3)
    
    print("\n📊 對話記錄:")
    for i, conv in enumerate(tutor.conversation_context, 1):
        print(f"  {i}. 學生: {conv['student'][:30]}...")
        print(f"     老師: {conv['teacher'][:50]}...")

def main():
    """主測試"""
    try:
        test_conversation()
        print("\n✅ 測試完成！")
        print("💡 如果沒有重複或奇怪的回應，修復成功")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
