import os
import re
import sys

# School name to abbreviation mapping provided by the user
SCHOOL_ABBREVIATIONS = {
    "國立中央大學": "NCU",
    "國立中央警察大學": "NCPA",
    "國立中山大學": "NSYSU",
    "國立中正大學": "CCU",
    "國立交通大學": "NCTU", # Note: Merged into NYCU
    "國立嘉義大學": "NCYU",
    "國立宜蘭大學": "NIU",
    "國立屏東大學": "NPPTU",
    "國立彰化師範大學": "NCUE",
    "國立成功大學": "NCKU",
    "國立政治大學": "NCCU",
    "國立暨南國際大學": "NCNU",
    "國立東華大學": "NDHU",
    "國立清華大學": "NTHU",
    "國立聯合大學": "NUU",
    "國立臺中科技大學": "NUTC",
    "國立臺北商業大學": "NTUB",
    # Multiple schools map to NTU, ensure this is intended
    "國立臺北大學": "NTU",
    "國立臺北教育大學": "NTUE",
    "國立臺北科技大學": "NTUT",
    "國立臺南大學": "NUTN",
    "國立臺東大學": "NTU",
    "國立臺灣大學": "NTU",
    "國立臺灣師範大學": "NTNU",
    "國立臺灣科技大學": "NTUST",
    "國立金門大學": "NQU",
    "國立雲林科技大學": "YUNTECH",
    "國立高雄大學": "NKU",
    "國立高雄應用科技大學": "KUAS", # Note: Merged into NKUST
    "國立高雄科技大學（建工校區）": "NKUST_Jian", # Using _Jian to distinguish
    "國立高雄科技大學（第一校區）": "NKUST_DY", # Using _DY to distinguish
    "國立高雄第一科技大學": "NKFUST", # Note: Merged into NKUST
    "私立中原大學": "CYCU",
    # Multiple schools map to CCU, ensure this is intended
    "私立中國文化大學": "CCU",
    "私立健行科技大學": "UCH",
    "私立元培醫事科技大學": "YPU",
    "私立元智大學": "YZU",
    "私立南臺科技大學": "STUST",
    "私立大同大學": "TTU",
    "私立慈濟大學": "TCU",
    "私立東吳大學": "SCU",
    "私立東海大學": "THU",
    "私立淡江大學": "TKU",
    "私立義守大學": "ESU",
    "私立臺北醫學大學": "TMU",
    "私立輔仁大學": "FJU",
    "私立逢甲大學": "FCU",
    "私立銘傳大學": "MCU",
    "私立長庚大學": "CGU",
    "私立靜宜大學": "PU"
}

# --- Configuration ---
INPUT_FOLDER = "data" # The root folder containing 111, 112, etc.
# Regex to parse the subject folder name: YEAR-SUBJECT-SCHOOL-DEPARTMENT
FOLDER_PATTERN = re.compile(r"^(\d+)-([^-]+)-([^-]+)-([^-]+)$")
# Regex to parse the original filename: ANY_SUBJECT_YEAR_第NUMBER題SUFFIX.EXT
# We capture SubjectCode, QuestionNumber, Suffix (including .ext)
# IMPORTANT: Adjust this regex if your filenames differ significantly
FILENAME_PATTERN = re.compile(r"^(?:UNKNOWN|\w+)_([A-Z]+)_\d+_第(\d+)題(.*)$", re.IGNORECASE)
# Set to True to actually rename files, False to just print what would be renamed
DRY_RUN = False
# --- End Configuration ---

def rename_files_in_folder(root_folder):
    """Walks through the folder structure and renames files."""
    print(f"Starting file renaming process in '{root_folder}'...")
    if DRY_RUN:
        print("--- DRY RUN MODE: No files will actually be renamed. ---")

    renamed_count = 0
    skipped_count = 0
    error_count = 0

    # Walk through the directory structure
    # root will be like 'data/111/111-科目-學校-系所' or 'data/111/111-科目-學校-系所/original_pages'
    for current_dir, subdirs, files in os.walk(root_folder):
        # Get the name of the directory containing the potential image files
        parent_dir_name = os.path.basename(current_dir)

        # Check if the *grandparent* directory name matches the subject folder pattern
        # This assumes images are one level down (e.g., in original_pages) or directly inside
        subject_folder_path = os.path.dirname(current_dir)
        subject_folder_name = os.path.basename(subject_folder_path)

        folder_match = FOLDER_PATTERN.match(subject_folder_name)

        # If the immediate parent folder name matches, use that
        if not folder_match:
             folder_match = FOLDER_PATTERN.match(parent_dir_name)
             if folder_match:
                 subject_folder_path = current_dir # Images are directly in the subject folder

        if not folder_match:
            # This directory doesn't match the expected pattern for containing subject info
            # print(f"Skipping directory (pattern mismatch): {current_dir}")
            continue

        dir_year, dir_subject, dir_school_chinese, dir_dept = folder_match.groups()

        # Look up school abbreviation
        school_abbr = SCHOOL_ABBREVIATIONS.get(dir_school_chinese)
        if not school_abbr:
            print(f"[Warning] School abbreviation not found for '{dir_school_chinese}' in folder '{subject_folder_name}'. Skipping files within.")
            # Optionally skip files in this folder if mapping is missing
            # continue
            school_abbr = "UNKNOWN_SCHOOL" # Use a placeholder if needed


        # Process files in the current directory
        for filename in files:
            file_match = FILENAME_PATTERN.match(filename)
            if file_match:
                file_subject_code, file_question_num, file_suffix = file_match.groups()

                # Construct the new filename
                # Format: Abbreviation_SubjectCode_Year_QuestionNumSuffix.ext
                # Example: YPU_CS_101_4-0point_cont.png
                new_filename = f"{school_abbr}_{file_subject_code}_{dir_year}_{file_question_num}{file_suffix}"

                old_path = os.path.join(current_dir, filename)
                new_path = os.path.join(current_dir, new_filename)

                if old_path == new_path:
                    # print(f"Skipping (already named correctly): {old_path}")
                    skipped_count +=1
                    continue

                print(f"Plan Rename: '{os.path.relpath(old_path, root_folder)}' -> '{new_filename}'")

                if not DRY_RUN:
                    try:
                        os.rename(old_path, new_path)
                        renamed_count += 1
                    except OSError as e:
                        print(f"[Error] Failed to rename '{old_path}' to '{new_path}': {e}")
                        error_count += 1
                else:
                    # In dry run, just count as if renamed for summary purposes
                     renamed_count += 1

            # else:
                # Optional: Print files that don't match the expected pattern
                # if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                #    print(f"Skipping non-matching file: {os.path.join(current_dir, filename)}")


    print("\n--- Renaming Summary ---")
    if DRY_RUN:
        print("--- (DRY RUN) ---")
    print(f"Files processed (planned for rename): {renamed_count}")
    print(f"Files skipped (already correct name): {skipped_count}")
    print(f"Errors during renaming: {error_count}")
    print("------------------------")
    if DRY_RUN:
        print("To actually rename files, set DRY_RUN = False in the script.")

if __name__ == "__main__":
    if not os.path.isdir(INPUT_FOLDER):
        print(f"[Error] Input folder '{INPUT_FOLDER}' not found.")
        sys.exit(1)
    rename_files_in_folder(INPUT_FOLDER) 