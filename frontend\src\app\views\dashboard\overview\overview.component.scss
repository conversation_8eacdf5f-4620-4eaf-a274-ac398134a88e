.dashboard-container {
    min-height: 100vh;
    background-color: #f8f9fa;
  }
  
  // 標題樣式
  .welcome-title {
    font-size: 1.8rem;
    margin-bottom: 0.25rem;
  }
  
  // 統計卡片樣式
  c-card {
    transition: transform 0.3s ease;
    border-radius: 0.5rem;
    overflow: hidden;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
  }
  
  // 導航項目樣式
  c-nav-item a {
    transition: all 0.2s ease;
    border-radius: 0.25rem;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    &.active {
      font-weight: bold;
    }
  }
  
  // 進度條樣式
  .progress-group {
    margin-bottom: 1.5rem;
  }
  
  // 自定義邊框顏色
  .border-start-primary {
    border-left-color: var(--cui-primary) !important;
  }
  
  .border-start-4 {
    border-left-width: 4px !important;
  }
  
  .border-bottom-3 {
    border-bottom-width: 3px !important;
  }
  
  // 卡片陰影效果
  .shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }
  
  // 圖標顏色
  .text-primary-subtle {
    color: rgba(var(--cui-primary-rgb), 0.5) !important;
  }
  
  .text-info-subtle {
    color: rgba(var(--cui-info-rgb), 0.5) !important;
  }
  
  .text-warning-subtle {
    color: rgba(var(--cui-warning-rgb), 0.5) !important;
  }
  
  .text-danger-subtle {
    color: rgba(var(--cui-danger-rgb), 0.5) !important;
  }
  
  // 快速操作按鈕
  // Be cautious applying this globally to button if not intended
  // button {
  //   transition: all 0.2s;
  //   &:hover {
  //     transform: translateY(-2px);
  //   }
  // }
  
  /* Whiteboard styles removed - moved to whiteboard.component.scss */
  
  /* Add styles for charts or other dashboard elements here */
  