import os
from pathlib import Path
import re
from typing import List, Dict, Tuple, Optional

class ProblemReader:
    """讀取題目文件並提供相關操作的類"""
    
    def __init__(self, data_dir: str = None):
        """初始化題目讀取器
        
        Args:
            data_dir: 題目數據目錄的路徑，如果為None則使用預設路徑
        """
        if data_dir:
            self.data_dir = Path(data_dir)
        else:
            # 預設使用相對路徑
            self.data_dir = Path(__file__).parent.parent / "data"
            
        # 確保目錄存在
        if not self.data_dir.exists():
            raise FileNotFoundError(f"找不到數據目錄: {self.data_dir}")
            
        print(f"使用數據目錄: {self.data_dir}")
        
        # 初始化題目列表
        self.problems = {}
        
        # 如果目錄存在，則開始掃描
        if self.data_dir.exists():
            self._scan_problems()
    
    def _scan_problems(self):
        """掃描問題文件夾並識別所有題目"""
        # 查找所有PNG文件
        png_files = list(self.data_dir.glob('*.png'))
        print(f"找到的PNG文件數量: {len(png_files)}")
        
        # 題目編號匹配模式
        pattern = r"第(\d+)題"
        
        # 處理每個文件
        for file_path in png_files:
            file_name = file_path.name
            print(f"處理文件: {file_name}")
            
            # 從文件名中提取題目編號
            match = re.search(pattern, file_name)
            if match:
                problem_number = int(match.group(1))
                print(f"  匹配成功: 題目編號={problem_number}")
                
                # 將題目添加到字典中
                self.problems[problem_number] = file_path
        
        # 打印找到的題目數量
        print(f"總共找到 {len(png_files)} 個題目文件")
    
    def get_all_problems(self) -> List[Tuple[int, Path]]:
        """獲取所有題目文件
        
        Returns:
            題目編號和文件路徑的列表，按編號排序
        """
        try:
            # 獲取所有PNG文件
            problem_files = list(self.data_dir.glob("*.png"))
            
            if not problem_files:
                print(f"警告: 在 {self.data_dir} 中沒有找到PNG文件")
                return []
            
            # 解析文件名中的題目編號
            problems = []
            for file_path in problem_files:
                try:
                    # 從文件名中提取數字
                    match = re.search(r'(\d+)', file_path.stem)
                    if match:
                        number = int(match.group(1))
                        problems.append((number, file_path))
                except ValueError:
                    print(f"警告: 無法從文件名解析題目編號: {file_path.name}")
                    continue
            
            # 按題目編號排序
            problems.sort(key=lambda x: x[0])
            
            print(f"總共找到 {len(problems)} 個題目文件")
            return problems
            
        except Exception as e:
            print(f"讀取題目文件時出錯: {str(e)}")
            return []
    
    def get_problem_file(self, problem_number: int) -> Optional[Path]:
        """獲取指定編號的題目文件路徑
        
        Args:
            problem_number: 題目編號
            
        Returns:
            題目文件的路徑，如果找不到則返回None
        """
        try:
            # 搜索匹配的文件
            pattern = f"*{problem_number}*.png"
            matches = list(self.data_dir.glob(pattern))
            
            if not matches:
                print(f"找不到題目 {problem_number} 的文件")
                return None
            
            if len(matches) > 1:
                print(f"警告: 找到多個題目 {problem_number} 的文件，使用第一個")
            
            return matches[0]
            
        except Exception as e:
            print(f"獲取題目 {problem_number} 文件時出錯: {str(e)}")
            return None
    
    def get_problem(self, problem_number: int) -> Optional[Path]:
        """獲取指定題目編號的文件路徑（兼容舊代碼）
        
        Args:
            problem_number: 題目編號
        
        Returns:
            題目文件的路徑，如果找不到則返回None
        """
        return self.get_problem_file(problem_number)
    
    def print_problem_info(self, problem_number: int = None):
        """打印題目信息
        
        Args:
            problem_number: 可選的題目編號，如果提供則只打印該題目的信息
                           否則打印所有題目的信息
        """
        if problem_number is not None:
            # 打印指定題目的信息
            problem_file = self.get_problem_file(problem_number)
            if problem_file:
                print(f"題目 {problem_number}: {problem_file.name}")
            else:
                print(f"找不到題目 {problem_number}")
        else:
            # 打印所有題目的信息
            problems = self.get_all_problems()
            if problems:
                for number, file_path in problems:
                    print(f"題目 {number}: {file_path.name}")
            else:
                print("未找到任何題目")

# 測試代碼
if __name__ == "__main__":
    # 創建題目讀取器
    reader = ProblemReader()
    
    # 打印所有題目信息
    reader.print_problem_info()
    
    # 測試獲取所有題目
    print("\n測試獲取所有題目:")
    problems = reader.get_all_problems()
    for number, path in problems:
        print(f"題目 {number}: {path.name}")
    
    # 測試獲取特定題目
    if problems:
        test_number = problems[0][0]
        print(f"\n測試獲取題目 {test_number}:")
        problem_file = reader.get_problem_file(test_number)
        if problem_file:
            print(f"找到文件: {problem_file.name}")
        else:
            print("找不到文件")

    # 測試特定文件名
    print("\n測試特定文件名:")
    test_files = [
        "題目1_測試.png",
        "國立臺灣師範大學-第1題-5point.png"
    ]
    
    for test_file in test_files:
        reader.test_specific_file(test_file) 