# 🎓 RAG智能教學系統 - 完整專案說明

## 📋 專案概述

本專案是一個基於RAG（Retrieval-Augmented Generation）技術的智能教學系統，結合了向量資料庫、AI問答和智能對話教學功能。

### 🎯 核心功能
- **PDF教材處理** - 自動提取和結構化教材內容
- **向量資料庫** - 高效的語義搜索和知識檢索
- **智能問答** - 基於教材內容的AI問答系統
- **對話教學** - 引導式智能教學和理解程度評估

## 🗂️ 專案結構

```
rag_system/
├── 📁 核心系統
│   ├── config.py              # 系統配置
│   ├── rag_main.py            # 主程式入口
│   ├── rag_processor.py       # RAG處理器
│   └── rag_ai_responder.py    # AI回答系統
├── 🎓 智能教學
│   ├── intelligent_tutor.py   # 智能教師
│   └── interactive_learning.py # 互動學習系統
├── 🔧 工具
│   ├── system_check.py        # 系統健康檢查
│   └── reset_database.py      # 資料庫重置
├── 📋 配置
│   ├── requirements.txt       # 依賴套件
│   └── RAG專案完整說明.md     # 本文檔
└── 📁 data/                   # 資料目錄
    ├── pdfs/                  # PDF教材
    ├── knowledge_db/          # 向量資料庫
    └── outputs/               # 處理結果
```

## 📄 核心檔案詳細說明

### 1. config.py - 系統配置

**功能**: 集中管理所有系統配置參數

#### 主要配置區塊:
- **路徑配置**: 定義資料目錄、PDF目錄、資料庫路徑
- **向量化配置**: 模型選擇、GPU設定、批量處理參數
- **AI配置**: Ollama模型設定、API端點
- **搜索配置**: 相似度閾值、返回結果數量
- **語言配置**: 支援的語言和顯示設定

#### 重要變數:
```python
# 路徑設定
DATA_DIR = Path("data")
PDF_DIR = DATA_DIR / "pdfs"
KNOWLEDGE_DB_DIR = DATA_DIR / "knowledge_db"

# 向量化模型
EMBEDDING_MODEL = "paraphrase-multilingual-MiniLM-L12-v2"

# AI模型設定
AI_CONFIG = {
    "model": "llama3.1:latest",
    "base_url": "http://localhost:11434"
}
```

### 2. rag_processor.py - RAG處理器

**功能**: 核心的RAG處理邏輯，包含PDF處理、向量化、資料庫建立

#### 主要類別: `RAGProcessor`

#### 核心方法說明:

##### `__init__(self, verbose=True)`
- **功能**: 初始化RAG處理器
- **參數**: verbose - 是否顯示詳細日誌
- **作用**: 設定GPU、載入向量化模型、初始化ChromaDB

##### `extract_pdf_content(self, pdf_path: str) -> List[Dict]`
- **功能**: 提取PDF內容並結構化
- **參數**: pdf_path - PDF檔案路徑
- **返回**: 結構化的內容列表
- **作用**: 使用多種方法提取PDF文字、圖片、表格

##### `process_single_pdf(self, pdf_path: str) -> Dict`
- **功能**: 處理單個PDF檔案
- **參數**: pdf_path - PDF檔案路徑
- **返回**: 處理結果字典
- **作用**: 完整處理流程，從提取到結構化

##### `batch_process_pdfs(self, pdf_dir: str) -> Dict`
- **功能**: 批量處理PDF檔案
- **參數**: pdf_dir - PDF目錄路徑
- **返回**: 批量處理結果
- **作用**: 並行處理多個PDF，顯示進度條

##### `create_knowledge_points(self, structured_content: List[Dict]) -> List[Dict]`
- **功能**: 從結構化內容創建知識點
- **參數**: structured_content - 結構化內容列表
- **返回**: 知識點列表
- **作用**: 智能分割和組織知識點

##### `build_vector_database(self, knowledge_points: List[Dict]) -> bool`
- **功能**: 建立向量資料庫
- **參數**: knowledge_points - 知識點列表
- **返回**: 是否成功
- **作用**: 生成向量嵌入，建立ChromaDB索引

##### `_generate_embeddings_gpu(self, texts: List[str]) -> np.ndarray`
- **功能**: GPU加速向量生成
- **參數**: texts - 文字列表
- **返回**: 向量陣列
- **作用**: 使用GPU批量生成向量嵌入

### 3. rag_ai_responder.py - AI回答系統

**功能**: 處理問答邏輯、搜索知識、生成AI回答

#### 主要類別: `AIResponder`

#### 核心方法說明:

##### `__init__(self, language='chinese')`
- **功能**: 初始化AI回答系統
- **參數**: language - 使用語言
- **作用**: 載入RAG處理器、設定語言、初始化AI連接

##### `answer_question(self, question: str, use_ai: bool = True) -> Dict`
- **功能**: 回答問題的主要方法
- **參數**: question - 問題, use_ai - 是否使用AI
- **返回**: 完整的回答字典
- **作用**: 整合搜索、分析、生成回答的完整流程

##### `search_knowledge(self, question: str, top_k: int = None) -> List[Dict]`
- **功能**: 搜索相關知識
- **參數**: question - 問題, top_k - 返回結果數
- **返回**: 搜索結果列表
- **作用**: 向量搜索、相似度過濾、結果排序

##### `analyze_question(self, question: str) -> Dict`
- **功能**: 使用AI分析問題
- **參數**: question - 問題
- **返回**: 問題分析結果
- **作用**: AI驅動的問題類型、複雜度、概念分析

##### `extract_structured_info(self, search_results: List[Dict]) -> Dict`
- **功能**: 從搜索結果提取結構化資訊
- **參數**: search_results - 搜索結果
- **返回**: 結構化資訊字典
- **作用**: 提取章節、知識點、頁碼等資訊

##### `generate_ai_response(self, question: str, context: str, question_analysis: Dict) -> str`
- **功能**: 生成AI回答
- **參數**: question - 問題, context - 上下文, question_analysis - 問題分析
- **返回**: AI生成的回答
- **作用**: 根據分析結果選擇模板，生成個性化回答

### 4. intelligent_tutor.py - 智能教師

**功能**: 實現智能對話教學、理解程度評估、引導式學習

#### 主要類別: `IntelligentTutor`

#### 核心方法說明:

##### `__init__(self)`
- **功能**: 初始化智能教師
- **作用**: 設定AI連接、初始化對話狀態、載入RAG系統

##### `translate_to_english(self, chinese_text: str) -> str`
- **功能**: 中文問題翻譯成英文
- **參數**: chinese_text - 中文文字
- **返回**: 英文翻譯
- **作用**: 支援中文問答、英文資料庫搜索

##### `analyze_question_intelligence(self, question: str) -> Dict`
- **功能**: AI智能問題分析
- **參數**: question - 問題
- **返回**: 分析結果字典
- **作用**: 判斷教學策略、複雜度、需求引導程度

##### `search_knowledge_bilingual(self, chinese_question: str) -> List[Dict]`
- **功能**: 雙語知識搜索
- **參數**: chinese_question - 中文問題
- **返回**: 搜索結果
- **作用**: 中文問題→英文搜索→中文回答

##### `generate_direct_answer(self, question: str, search_results: List[Dict]) -> str`
- **功能**: 生成直接回答
- **參數**: question - 問題, search_results - 搜索結果
- **返回**: 直接回答
- **作用**: 簡潔明確的定義型回答

##### `generate_guided_response(self, question: str, search_results: List[Dict]) -> str`
- **功能**: 生成引導式回答
- **參數**: question - 問題, search_results - 搜索結果
- **返回**: 引導式回答
- **作用**: 循序漸進的引導式教學

##### `evaluate_understanding(self, student_response: str) -> Dict`
- **功能**: 評估學生理解程度
- **參數**: student_response - 學生回答
- **返回**: 評估結果字典
- **作用**: AI評估理解程度、正確性、提供反饋

##### `handle_question(self, question: str) -> str`
- **功能**: 處理學生問題的主要方法
- **參數**: question - 問題
- **返回**: 回答
- **作用**: 整合分析、搜索、生成回答的完整流程

##### `continue_guidance(self, student_response: str) -> str`
- **功能**: 在引導模式中繼續對話
- **參數**: student_response - 學生回應
- **返回**: 後續引導
- **作用**: 評估回答、提供反饋、決定下一步引導

### 5. interactive_learning.py - 互動學習系統

**功能**: 提供用戶互動介面、管理學習流程

#### 主要類別: `InteractiveLearningSystem`

#### 核心方法說明:

##### `__init__(self)`
- **功能**: 初始化互動學習系統
- **作用**: 創建智能教師實例、設定對話狀態

##### `show_welcome(self)`
- **功能**: 顯示歡迎訊息
- **作用**: 介紹系統功能、使用說明

##### `handle_special_commands(self, user_input: str) -> bool`
- **功能**: 處理特殊命令
- **參數**: user_input - 用戶輸入
- **返回**: 是否退出
- **作用**: 處理quit、next、help等命令

##### `show_help(self)`
- **功能**: 顯示幫助訊息
- **作用**: 說明系統命令和使用技巧

##### `show_status(self)`
- **功能**: 顯示當前狀態
- **作用**: 顯示學習進度、理解程度等

##### `run(self)`
- **功能**: 運行互動式學習系統
- **作用**: 主要的對話循環、處理用戶輸入

### 6. rag_main.py - 主程式入口

**功能**: 系統主入口、選單管理、功能整合

#### 主要類別: `RAGSystem`

#### 核心方法說明:

##### `__init__(self)`
- **功能**: 初始化RAG系統
- **作用**: 設定語言、初始化組件

##### `run(self)`
- **功能**: 運行主系統
- **作用**: 顯示選單、處理用戶選擇

##### `_show_main_menu(self)`
- **功能**: 顯示主選單
- **作用**: 展示所有可用功能選項

##### `_handle_pdf_processing(self)`
- **功能**: 處理PDF教材處理
- **作用**: 管理PDF處理流程

##### `_handle_qa_system(self)`
- **功能**: 處理智能問答系統
- **作用**: 啟動傳統問答模式

##### `_handle_intelligent_tutoring(self)`
- **功能**: 處理智能對話教學
- **作用**: 啟動智能教學系統

##### `_handle_system_settings(self)`
- **功能**: 處理系統設定
- **作用**: 語言切換、參數調整

##### `_handle_system_status(self)`
- **功能**: 處理系統狀態
- **作用**: 顯示系統健康狀況

## 🔧 工具檔案說明

### system_check.py - 系統健康檢查
- **check_gpu()**: 檢查GPU狀態
- **check_ollama()**: 檢查Ollama連接
- **check_database()**: 檢查向量資料庫
- **check_files()**: 檢查核心檔案
- **check_directories()**: 檢查目錄結構

### check_database.py - 詳細資料庫檢查
- **detailed_database_check()**: 詳細檢查ChromaDB
- **quick_fix_suggestions()**: 提供修復建議

### reset_database.py - 資料庫重置
- **reset_database()**: 清除向量資料庫和相關檔案

## 🚀 使用流程

1. **系統檢查**: `python system_check.py`
2. **啟動系統**: `python rag_main.py`
3. **建立知識庫**: 選項1 - PDF教材處理
4. **智能問答**: 選項2 - 傳統問答模式
5. **對話教學**: 選項3 - 智能對話教學

## 📊 詳細方法列表

### rag_processor.py 完整方法列表

```python
class RAGProcessor:
    def __init__(self, verbose=True)                    # 初始化處理器
    def _setup_gpu(self)                                # 設定GPU環境
    def _load_embedding_model(self)                     # 載入向量化模型
    def _initialize_chroma_db(self)                     # 初始化ChromaDB
    def extract_pdf_content(self, pdf_path)             # 提取PDF內容
    def _extract_with_unstructured(self, pdf_path)      # 使用unstructured提取
    def _extract_with_pdfplumber(self, pdf_path)        # 使用pdfplumber提取
    def _extract_with_pypdf2(self, pdf_path)            # 使用PyPDF2提取
    def _clean_text(self, text)                         # 清理文字
    def _extract_metadata(self, pdf_path)               # 提取元數據
    def process_single_pdf(self, pdf_path)              # 處理單個PDF
    def batch_process_pdfs(self, pdf_dir)               # 批量處理PDF
    def create_knowledge_points(self, structured_content) # 創建知識點
    def _split_content_intelligently(self, content)     # 智能分割內容
    def _generate_keywords(self, content)               # 生成關鍵詞
    def _generate_summary(self, content)                # 生成摘要
    def build_vector_database(self, knowledge_points)   # 建立向量資料庫
    def _generate_embeddings_gpu(self, texts)           # GPU向量生成
    def _batch_process_embeddings(self, texts)          # 批量處理向量
    def search_similar_content(self, query, top_k)      # 搜索相似內容
    def get_database_stats(self)                        # 獲取資料庫統計
    def save_results(self, results, filename)           # 保存結果
    def load_existing_database(self)                    # 載入現有資料庫
```

### rag_ai_responder.py 完整方法列表

```python
class AIResponder:
    def __init__(self, language='chinese')              # 初始化AI回答系統
    def load_rag_processor(self)                        # 載入RAG處理器
    def update_subject_info(self, subject_info)         # 更新科目資訊
    def analyze_question(self, question)                # AI問題分析
    def _create_analysis_prompt(self, question)         # 創建分析prompt
    def _parse_ai_analysis(self, ai_response, question) # 解析AI分析結果
    def _fallback_analysis(self, question)              # 備用分析方法
    def search_knowledge(self, question, top_k)         # 搜索知識
    def _calculate_similarity(self, query_embedding, doc_embedding) # 計算相似度
    def _rerank_results(self, results, question)        # 重新排序結果
    def extract_structured_info(self, search_results)   # 提取結構化資訊
    def _extract_chapters(self, search_results)         # 提取章節資訊
    def _extract_knowledge_points(self, search_results) # 提取知識點
    def _extract_page_references(self, search_results)  # 提取頁碼參考
    def answer_question(self, question, use_ai)         # 回答問題主方法
    def generate_ai_response(self, question, context, question_analysis) # 生成AI回答
    def _select_teaching_template(self, question_analysis) # 選擇教學模板
    def _create_response_prompt(self, template_type, question, context, question_analysis) # 創建回答prompt
    def _get_template_prompt(self, template_type, language) # 獲取模板prompt
    def generate_fallback_response(self, question, search_results) # 生成備用回答
    def _generate_related_concepts(self, search_results, question_analysis) # 生成相關概念
    def _generate_study_suggestions(self, question_analysis) # 生成學習建議
    def _create_suggestion_prompt(self, question_analysis) # 創建建議prompt
    def _fallback_suggestions(self, question_analysis)  # 備用學習建議
    def format_response_for_display(self, response)     # 格式化回答顯示
    def get_system_status(self)                         # 獲取系統狀態
```

### intelligent_tutor.py 完整方法列表

```python
class IntelligentTutor:
    def __init__(self)                                  # 初始化智能教師
    def translate_to_english(self, chinese_text)        # 中英文翻譯
    def analyze_question_intelligence(self, question)   # AI智能問題分析
    def search_knowledge_bilingual(self, chinese_question) # 雙語知識搜索
    def generate_direct_answer(self, question, search_results) # 生成直接回答
    def generate_guided_response(self, question, search_results) # 生成引導式回答
    def evaluate_understanding(self, student_response)  # 評估學生理解程度
    def handle_question(self, question)                 # 處理學生問題主方法
    def continue_guidance(self, student_response)       # 在引導模式中繼續對話
    def _generate_follow_up_guidance(self, level)       # 生成後續引導
```

### interactive_learning.py 完整方法列表

```python
class InteractiveLearningSystem:
    def __init__(self)                                  # 初始化互動學習系統
    def show_welcome(self)                              # 顯示歡迎訊息
    def handle_special_commands(self, user_input)       # 處理特殊命令
    def show_help(self)                                 # 顯示幫助訊息
    def show_status(self)                               # 顯示當前狀態
    def run(self)                                       # 運行互動式學習系統
```

### rag_main.py 完整方法列表

```python
class RAGSystem:
    def __init__(self)                                  # 初始化RAG系統
    def _show_welcome(self)                             # 顯示歡迎資訊
    def run(self)                                       # 運行主系統
    def _show_main_menu(self)                           # 顯示主選單
    def _handle_pdf_processing(self)                    # 處理PDF教材處理
    def _handle_ai_qa(self)                             # 處理智能問答系統
    def _handle_intelligent_tutoring(self)              # 處理智能對話教學
    def _handle_exit(self)                              # 處理退出
    def _show_qa_help(self)                             # 顯示問答幫助
    def _handle_system_settings(self)                   # 處理系統設定
    def _change_language(self)                          # 切換語言
    def _update_subject_info(self)                      # 更新科目資訊
    def _configure_ai_model(self)                       # 配置AI模型
    def _configure_search_params(self)                  # 配置搜索參數
    def _show_current_config(self)                      # 顯示當前配置
    def _handle_system_status(self)                     # 處理系統狀態
    def _show_gpu_info(self)                            # 顯示GPU資訊
    def _show_database_info(self)                       # 顯示資料庫資訊
    def _show_ai_model_info(self)                       # 顯示AI模型資訊
```

## 🎯 技術特色

- **GPU加速**: 向量生成和搜索使用GPU加速
- **雙語支援**: 中文問答、英文搜索
- **AI驅動**: 問題分析、理解評估完全由AI處理
- **引導式教學**: 根據學生理解程度動態調整
- **模組化設計**: 清晰的職責分離和介面設計

## 🔄 資料流程

1. **PDF處理流程**:
   ```
   PDF檔案 → 內容提取 → 結構化處理 → 知識點創建 → 向量生成 → 資料庫建立
   ```

2. **問答流程**:
   ```
   用戶問題 → 問題分析 → 向量搜索 → 結果過濾 → AI生成回答 → 格式化輸出
   ```

3. **智能教學流程**:
   ```
   問題輸入 → AI分析 → 教學方式選擇 → 引導式回答 → 理解評估 → 後續引導
   ```

## 🛠️ 開發和維護

### 添加新功能
1. 在對應的類別中添加新方法
2. 更新config.py中的相關配置
3. 在main.py中添加選單選項
4. 更新本文檔

### 調試技巧
- 使用`verbose=True`開啟詳細日誌
- 檢查`system_check.py`確認系統狀態
- 使用`check_database.py`詳細檢查資料庫
- 查看`data/outputs/`目錄中的處理日誌

### 效能優化
- 調整`GPU_CONFIG`中的批量大小
- 修改`SEARCH_CONFIG`中的相似度閾值
- 使用更大的向量化模型提高準確性
- 啟用混合精度加速GPU計算
