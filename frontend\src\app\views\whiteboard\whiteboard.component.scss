.whiteboard-container {
  width: 100%;
  // Removed background-color, border-radius, padding as it might be redundant if placed inside a card
}

.whiteboard-controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.whiteboard-canvas-container {
  width: 100%;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  overflow: hidden;
  background-color: #ffffff; // Keep white background for the canvas itself
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.whiteboard-canvas {
  display: block;
  cursor: crosshair;
  width: 100%;
  // height is now set dynamically in TS (resizeCanvas)
  touch-action: none; // Important for touch events
}

// Keep form-range styling if it's specific to whiteboard size slider
.form-range {
  width: 100px;
}

// Keep responsive adjustments
@media (max-width: 768px) {
  .whiteboard-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .whiteboard-controls > div,
  .whiteboard-controls > c-button-group {
    margin-bottom: 10px;
  }
}
