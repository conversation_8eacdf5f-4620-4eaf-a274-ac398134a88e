# 🎓 RAG智能教學系統

一個基於RAG (Retrieval-Augmented Generation) 技術的智能教學系統，專門用於處理教育材料並提供結構化的知識問答服務。

## 🎯 系統特色

- 📚 **PDF教材批量處理**: 支援一次處理多個PDF教材檔案
- 🧠 **智能知識結構化**: 自動提取章節、知識點並建立向量資料庫
- 🌐 **中英文雙語支援**: AI可選擇中文或英文回答，支援中英文混合教材
- 🔍 **高效語義檢索**: 基於ChromaDB的向量化檢索系統
- 🤖 **本地AI模型**: 使用本地llama3.1模型，保護資料隱私
- 📊 **結構化輸出**: 提供包含科目、章節、知識點的詳細回答

## 🚀 快速開始

### 1. 環境準備
```bash
# 確保Python 3.8+已安裝
python --version

# 安裝依賴套件
pip install -r requirements.txt

# 安裝Ollama並下載llama3.1模型
ollama pull llama3.1
```

### 2. 使用方法

#### 方法一：使用主程式 (推薦)
```bash
# 運行主程式，包含完整的互動式介面
python rag_main.py
```

#### 方法二：程式化調用
```python
from rag_processor import RAGProcessor
from rag_ai_responder import AIResponder

# 1. 處理PDF教材並建立知識庫
processor = RAGProcessor()
processor.process_multiple_pdfs(['教材1.pdf', '教材2.pdf'])
processor.build_knowledge_database()

# 2. 初始化AI回答系統
ai = AIResponder(language='chinese')  # 或 'english'

# 3. 提問並獲得回答
response = ai.answer_question("什麼是作業系統？")
print(response)
```

## 📁 檔案結構

```
rag_system/
├── README.md                    # 📖 專案說明文檔
├── rag_main.py                  # 🚀 主要調用程式 (互動式介面)
├── rag_processor.py             # 🔧 RAG處理器 (PDF→向量資料庫)
├── rag_ai_responder.py          # 🤖 AI回答系統 (支援中英文)
├── config.py                    # ⚙️ 系統配置檔案
├── requirements.txt             # 📦 依賴套件清單
└── data/                        # 📁 資料目錄
    ├── pdfs/                    # 存放PDF教材
    ├── knowledge_db/            # 向量資料庫存儲
    └── outputs/                 # 輸出檔案 (JSON等)
```

## 🛠️ 核心功能

### 1. PDF教材處理 (`rag_processor.py`)
- **批量處理**: 一次處理多個PDF檔案
- **智能解析**: 自動識別章節結構和知識點
- **向量化**: 使用多語言模型進行文本嵌入
- **資料庫建立**: 建立ChromaDB向量資料庫

### 2. AI回答系統 (`rag_ai_responder.py`)
- **語言選擇**: 支援中文/英文回答模式
- **結構化回答**: 包含科目、章節、知識點等資訊
- **上下文檢索**: 基於向量相似度的智能檢索
- **本地模型**: 使用llama3.1本地模型

### 3. 主程式 (`rag_main.py`)
- **互動式介面**: 友好的命令列介面
- **批量處理**: 支援多檔案批量處理
- **語言切換**: 即時切換回答語言
- **會話管理**: 支援連續對話

## 💡 使用範例

### 問答示例
```
🤖 RAG智能教學系統
請選擇回答語言 (1: 中文, 2: English): 1

📚 請輸入您的問題: 什麼是死鎖？

📚 科目: 作業系統概論
📖 章節: 第7章 - 同步化
🎯 知識點: 死鎖 (Deadlock)
📍 頁碼: 第245-250頁

💡 詳細回答:
死鎖是指兩個或多個進程在執行過程中，因爭奪資源而造成的一種互相等待的現象...

🔗 相關概念: 進程同步 | 資源分配 | 互斥 | 信號量

📝 學習建議: 建議結合實際例子理解死鎖的四個必要條件...
```

## ⚙️ 配置選項

在 `config.py` 中可以調整：
- AI模型設定 (預設: llama3.1)
- 向量模型設定 (預設: paraphrase-multilingual-MiniLM-L12-v2)
- 資料庫路徑
- 批量處理設定
- 語言偏好設定

## 🔧 系統要求

- **Python**: 3.8 或更高版本
- **記憶體**: 建議8GB以上 (用於向量化處理)
- **儲存空間**: 至少2GB可用空間
- **網路**: 首次運行需要下載模型

## 🚨 注意事項

1. **模型依賴**: 確保Ollama已安裝並運行llama3.1模型
2. **處理時間**: 大型PDF文檔的初次處理可能需要10-30分鐘
3. **記憶體使用**: 向量化過程會消耗較多記憶體
4. **中文支援**: 系統已優化中文處理，支援繁體和簡體中文

## 📄 授權

本專案採用MIT授權條款。

---

## 🚀 快速安裝

### 自動安裝 (推薦)
```bash
# 1. 進入專案目錄
cd rag_system

# 2. 運行自動安裝腳本
python setup.py

# 3. 按照提示完成安裝
```

### 手動安裝
```bash
# 1. 安裝依賴套件
pip install -r requirements.txt

# 2. 安裝並啟動Ollama
# 下載: https://ollama.ai/
ollama pull llama3.1

# 3. 測試系統
python test_system.py

# 4. 啟動主程式
python rag_main.py
```

## 📋 檔案說明

| 檔案名稱 | 功能說明 | 重要程度 |
|---------|---------|---------|
| `rag_main.py` | 🚀 **主程式** - 互動式介面 | ⭐⭐⭐ |
| `rag_processor.py` | 🔧 PDF處理和向量資料庫建立 | ⭐⭐⭐ |
| `rag_ai_responder.py` | 🤖 AI回答系統 | ⭐⭐⭐ |
| `config.py` | ⚙️ 系統配置檔案 | ⭐⭐ |
| `setup.py` | 📦 自動安裝腳本 | ⭐⭐ |
| `test_system.py` | 🧪 系統測試腳本 | ⭐⭐ |
| `example_usage.py` | 📖 使用示例 | ⭐ |
| `requirements.txt` | 📋 依賴套件清單 | ⭐⭐ |

## 🎯 優化特色

### ✨ 相比原版的改進
1. **📁 統一資料夾管理** - 所有檔案整理在 `rag_system` 目錄
2. **🎯 簡潔檔案命名** - 主程式包含 `main`，功能一目了然
3. **🌐 完整中英文支援** - 可即時切換回答語言
4. **📚 批量處理優化** - 一次處理多個PDF，效率更高
5. **🔧 詳細註解** - 每個函數都有完整的中文註解
6. **⚙️ 靈活配置** - 統一的配置檔案，易於調整
7. **🧪 完整測試** - 自動測試系統各個組件
8. **📖 豐富文檔** - 詳細的使用說明和示例

### 🚀 效能優化
- **批量向量化** - 提升處理速度
- **智能分塊** - 優化記憶體使用
- **快取機制** - 減少重複計算
- **並行處理** - 支援多線程處理

### 🛡️ 穩定性提升
- **錯誤處理** - 完善的異常處理機制
- **日誌系統** - 詳細的運行日誌
- **配置驗證** - 自動檢查配置有效性
- **備用方案** - AI模型不可用時的備用回答

## 🔧 進階配置

### 自定義AI模型
```python
# 在 config.py 中修改
LOCAL_AI_MODEL = "your-model-name"
LOCAL_AI_BASE_URL = "http://your-server:port"
```

### 調整搜索參數
```python
# 在 config.py 中修改
SEARCH_CONFIG = {
    "default_top_k": 10,        # 增加搜索結果數量
    "similarity_threshold": 0.6  # 降低相似度閾值
}
```

### 自定義科目資訊
```python
# 在 config.py 中修改
DEFAULT_SUBJECT_INFO = {
    "科目名稱": "您的科目名稱",
    "英文名稱": "Your Subject Name",
    "教材作者": "作者姓名",
    "版本": "版本資訊"
}
```

## 🐛 常見問題

### Q: PDF處理失敗怎麼辦？
A:
1. 檢查PDF檔案是否損壞
2. 確保檔案大小不超過100MB
3. 檢查檔案名稱是否包含特殊字符

### Q: AI模型無法連接？
A:
1. 確保Ollama服務正在運行
2. 檢查模型是否已下載: `ollama list`
3. 嘗試重新下載模型: `ollama pull llama3.1`

### Q: 向量資料庫建立失敗？
A:
1. 檢查磁碟空間是否充足
2. 確保有寫入權限
3. 嘗試刪除舊的資料庫檔案重新建立

### Q: 中文顯示亂碼？
A:
1. 確保終端支援UTF-8編碼
2. 在Windows上使用PowerShell或Windows Terminal
3. 檢查Python環境的編碼設定

## 📞 技術支援

如果遇到問題，請：
1. 先運行 `python test_system.py` 檢查系統狀態
2. 查看 `data/outputs/system.log` 日誌檔案
3. 檢查是否按照安裝步驟正確設定

**🎓 讓學習更智能，讓知識更易得！**
