# RAG Intelligent Teaching System - Dependencies
# Core packages for PDF processing, vector database, and AI integration

# Core frameworks
numpy>=1.21.0
pandas>=1.3.0

# PDF processing
PyPDF2>=3.0.0
pdfplumber>=0.7.0
unstructured>=0.10.0
unstructured[pdf]>=0.10.0

# Text processing and NLP
nltk>=3.8
jieba>=0.42.1
sentence-transformers>=2.2.0
transformers>=4.21.0

# Vector databases
chromadb>=0.4.0
faiss-cpu>=1.7.4

# HTTP clients and APIs
requests>=2.28.0
httpx>=0.24.0

# Machine learning and deep learning
torch>=1.12.0
torchvision>=0.13.0
scikit-learn>=1.1.0

# Data processing utilities
tqdm>=4.64.0
python-dotenv>=0.19.0

# Logging and configuration
coloredlogs>=15.0
pyyaml>=6.0

# Development and testing tools
pytest>=7.0.0
black>=22.0.0
flake8>=5.0.0

# GPU support packages (install based on your CUDA version)
# For CUDA 11.8: torch>=2.0.0+cu118 torchvision>=0.15.0+cu118
# For CUDA 12.1: torch>=2.0.0+cu121 torchvision>=0.15.0+cu121
# faiss-gpu>=1.7.4  # GPU-accelerated FAISS

# Optional: Advanced NLP features (uncomment if needed)
# spacy>=3.4.0

# Installation notes:
# 1. Ensure Python version >= 3.8
# 2. Recommended to use virtual environment
# 3. If installation issues occur, try: pip install --upgrade pip
# 4. Some packages may require additional system dependencies
