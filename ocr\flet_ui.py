import flet as ft
from src.read import ProblemReader
from pathlib import Path
from src.gemini import GeminiAnalyzer
import re
import os

def extract_code_blocks(text):
    """從文本中提取代碼塊
    
    Args:
        text: 包含可能代碼塊的文本
        
    Returns:
        包含描述和代碼的字典
    """
    if not text:
        return {"description": "", "code": ""}
        
    # 首先檢查是否有標準格式的代碼塊
    code_pattern = r"```(?:c|cpp|C|c\+\+)?\n?(.*?)```"
    code_blocks = re.findall(code_pattern, text, re.DOTALL)
    
    # 如果找到標準格式的代碼塊
    if code_blocks:
        # 合併所有代碼塊
        all_code = "\n\n".join([f"```c\n{block.strip()}\n```" for block in code_blocks])
        
        # 從原文中移除代碼塊，留下描述部分
        clean_text = re.sub(r"```(?:c|cpp|C|c\+\+)?\n?.*?```", "", text, flags=re.DOTALL).strip()
        
        # 檢查是否有題目描述在代碼塊外，但應該屬於題目的一部分
        if "Please write down the output" in clean_text and "Please write down the output" not in all_code:
            desc_pattern = r"(.*?Please write down the output of the following code\.*)"
            desc_match = re.search(desc_pattern, clean_text, re.DOTALL | re.IGNORECASE)
            if desc_match:
                problem_desc = desc_match.group(1).strip()
                clean_text = clean_text.replace(problem_desc, "").strip()
                
                # 加入題目描述到描述部分
                if clean_text:
                    clean_text = problem_desc + "\n\n" + clean_text
                else:
                    clean_text = problem_desc
        
        return {
            "description": clean_text,
            "code": all_code
        }
    
    # 如果沒有標準格式的代碼塊，使用關鍵字識別 C 程式碼特性
    c_keywords = [
        "#include", "void", "int main", "printf", "return", 
        "stdio.h", "int ", "float ", "char ", "for(", "while(", 
        "scanf", "if(", "else", "struct", "switch"
    ]
    
    # 檢測行號格式如 "1 #include" 或 "1  #include"
    line_number_pattern = r"^\s*\d+[\s\.]+(.+)"
    
    lines = text.split("\n")
    code_lines = []
    desc_lines = []
    in_code_block = False
    seen_code = False
    in_problem_statement = False
    
    # 首先檢查是否有「Please write down the output」這樣的題目說明
    for i, line in enumerate(lines):
        if "Please write down the output" in line or "(points)" in line:
            in_problem_statement = True
            desc_lines.append(line)
            
            # 檢查下一行是否開始是程式碼
            if i+1 < len(lines) and (
                re.match(line_number_pattern, lines[i+1]) or 
                any(keyword in lines[i+1] for keyword in c_keywords)
            ):
                in_problem_statement = False
            continue
        
        if in_problem_statement:
            if any(keyword in line for keyword in c_keywords) or re.match(line_number_pattern, line):
                in_problem_statement = False
            else:
                desc_lines.append(line)
                continue
                
        # 檢查行是否包含C關鍵字或有行號
        contains_keyword = any(keyword in line for keyword in c_keywords)
        line_number_match = re.match(line_number_pattern, line)
        
        # 如果行有行號或包含C關鍵字，則考慮為程式碼
        if line_number_match or contains_keyword or ("{" in line and "}" in line) or ";" in line:
            if not in_code_block and desc_lines and "output" not in "".join(desc_lines[-3:]):
                # 如果前面沒有提到output，可能是從頭開始的程式碼
                # 檢查是否有題目描述
                intro_text = " ".join(desc_lines)
                if "Please write down the output" in intro_text or "(points)" in intro_text:
                    # 已經有題目描述，繼續處理代碼
                    pass
                else:
                    # 清空描述，因為可能不是題目描述
                    desc_lines = []
            
            in_code_block = True
            code_lines.append(line)
            seen_code = True
        else:
            # 如果在程式碼區塊中但遇到空行，暫時保留在程式碼中
            if in_code_block and not line.strip():
                code_lines.append(line)
            else:
                # 結束程式碼區塊
                in_code_block = False
                # 將非程式碼行添加到描述
                desc_lines.append(line)
    
    # 如果檢測到程式碼
    if seen_code and code_lines:
        code_text = "\n".join(code_lines)
        desc_text = "\n".join(desc_lines)
        
        # 從程式碼中移除可能的題目描述
        if "points" in code_text and "output" in code_text:
            intro_pattern = r".*?points.*?output.*?\.\s*"
            code_text = re.sub(intro_pattern, "", code_text, count=1, flags=re.DOTALL | re.IGNORECASE)
        
        return {
            "description": desc_text.strip(),
            "code": f"```c\n{code_text.strip()}\n```"
        }
    
    # 如果沒有檢測到程式碼
    return {
        "description": text,
        "code": ""
    }

def main(page: ft.Page):
    """
    Flet應用的主函數
    
    Args:
        page: Flet頁面對象
    """
    # 設置頁面主題和屬性
    page.title = "題目瀏覽器"
    page.padding = 20
    page.theme_mode = ft.ThemeMode.LIGHT
    page.scroll = ft.ScrollMode.AUTO
    page.bgcolor = "#FFFFFF"  # 使用白色背景
    
    # 定義顏色常量
    PRIMARY_COLOR = "#1976D2"      # 藍色
    SECONDARY_COLOR = "#757575"    # 灰色
    ERROR_COLOR = "#D32F2F"        # 紅色
    SUCCESS_COLOR = "#2E7D32"      # 綠色
    SURFACE_COLOR = "#F5F5F5"      # 淺灰色背景
    
    # 創建標題區域
    title_area = ft.Container(
        content=ft.Column([
            ft.Text(
                "題目瀏覽器", 
                size=36, 
                weight=ft.FontWeight.BOLD,
                color=PRIMARY_COLOR
            ),
            ft.Text(
                "選擇題目以查看內容和AI分析", 
                size=16, 
                color=SECONDARY_COLOR
            )
        ]),
        padding=ft.padding.only(bottom=20)
    )
    
    # 創建狀態文本
    status_text = ft.Text(
        "正在載入題目...", 
        color=PRIMARY_COLOR,
        size=14,
        weight=ft.FontWeight.BOLD
    )
    
    # 創建題目圖片區塊
    problem_image_view = ft.Column(
        spacing=10,
        scroll=ft.ScrollMode.AUTO,
        visible=True
    )
    
    # 創建題目內容區塊
    problem_content = ft.Column(
        spacing=10,
        scroll=ft.ScrollMode.AUTO,
        visible=True
    )
    
    # 創建題目列表
    problem_buttons = ft.Column(
        spacing=8,
        scroll=ft.ScrollMode.AUTO,
        height=600
    )
    
    # 定義顯示題目的函數
    def show_problem(problem_num):
        """顯示選擇的題目並自動進行AI分析"""
        try:
            if not problem_num:
                return
            
            status_text.value = f"正在載入題目 {problem_num}..."
            status_text.color = PRIMARY_COLOR
            page.update()
                
            # 清除之前的內容
            problem_image_view.controls.clear()
            problem_content.controls.clear()
            
            # 獲取題目文件路徑
            problem_file = problem_reader.get_problem_file(int(problem_num))
            if not problem_file:
                status_text.value = f"錯誤：找不到題目 {problem_num}"
                status_text.color = ERROR_COLOR
                page.update()
                return
                
            # 記錄當前題目和路徑
            global current_problem, current_problem_file
            current_problem = int(problem_num)
            current_problem_file = problem_file
                
            # 顯示題目圖片
            problem_image_view.controls.append(
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            f"題目 {problem_num}", 
                            size=24, 
                            weight=ft.FontWeight.BOLD,
                            color=PRIMARY_COLOR
                        ),
                        ft.Image(
                            src=str(problem_file),
                            width=800,
                            fit=ft.ImageFit.CONTAIN,
                            border_radius=ft.border_radius.all(8)
                        )
                    ]),
                    padding=10
                )
            )
            
            # 更新狀態文本
            status_text.value = f"正在分析題目 {problem_num}..."
            page.update()
            
            # 自動進行AI分析
            analyze_problem()
            
        except Exception as e:
            status_text.value = f"顯示題目時出錯：{str(e)}"
            status_text.color = ERROR_COLOR
            page.update()
    
    # 定義分析題目的函數
    def analyze_problem():
        """使用AI分析當前題目"""
        try:
            # 確保有選中的題目
            if not current_problem_file:
                status_text.value = "請先選擇一個題目"
                status_text.color = ERROR_COLOR
                page.update()
                return
            
            # 使用Gemini分析題目
            result = analyzer.get_problem_analysis(current_problem_file)
            
            if result.get("success", False):
                # 獲取分析結果
                problem_text = result.get("chinese", {}).get("problem_question", "")
                
                # 清除之前的內容
                problem_content.controls.clear()
                
                # 顯示分析結果
                if problem_text:
                    problem_content.controls.append(
                        ft.Container(
                            content=ft.Column([
                                ft.Text(
                                    "AI 分析結果", 
                                    size=20, 
                                    weight=ft.FontWeight.BOLD,
                                    color=PRIMARY_COLOR
                                ),
                                ft.Container(
                                    content=ft.Markdown(
                                        problem_text,
                                        selectable=True,
                                        extension_set=ft.MarkdownExtensionSet.GITHUB_WEB,
                                        code_theme="satom-one-dark",
                                        code_style_sheet=ft.TextStyle(
                                            font_family="Consolas",
                                            size=14,
                                            color=SECONDARY_COLOR,
                                        )
                                    ),
                                    bgcolor=SURFACE_COLOR,
                                    padding=20,
                                    border_radius=8,
                                    width=800
                                )
                            ]),
                            padding=10
                        )
                    )
                
                # 更新狀態
                status_text.value = f"題目 {current_problem} 分析完成"
                status_text.color = SUCCESS_COLOR
            else:
                # 分析失敗
                error_msg = result.get("error", "未知錯誤")
                status_text.value = f"分析失敗: {error_msg}"
                status_text.color = ERROR_COLOR
                
                problem_content.controls.clear()
                problem_content.controls.append(
                    ft.Container(
                        content=ft.Text(
                            f"分析失敗: {error_msg}",
                            color=ERROR_COLOR,
                            size=16,
                            weight=ft.FontWeight.BOLD
                        ),
                        padding=20
                    )
                )
            
            # 更新頁面
            page.update()
            
        except Exception as e:
            status_text.value = f"分析過程中出錯: {str(e)}"
            status_text.color = ERROR_COLOR
            page.update()
    
    # 創建題目點擊處理函數
    def on_problem_button_click(e):
        # 更新所有按鈕的狀態
        for button in problem_buttons.controls:
            if isinstance(button, ft.ElevatedButton):
                if button.data == e.control.data:
                    button.bgcolor = PRIMARY_COLOR
                    button.color = "#FFFFFF"  # 白色文字
                else:
                    button.bgcolor = None
                    button.color = None
        
        # 顯示選中的題目
        show_problem(e.control.data)
    
    # 創建側邊欄
    sidebar = ft.Container(
        content=ft.Column([
            ft.Text(
                "選擇題目",
                size=20,
                weight=ft.FontWeight.BOLD,
                color=PRIMARY_COLOR
            ),
            ft.Container(
                content=problem_buttons,
                padding=ft.padding.only(top=10)
            )
        ]),
        padding=20,
        bgcolor=SURFACE_COLOR,
        border_radius=12,
        width=250
    )
    
    # 創建主要內容區域
    content_area = ft.Container(
        content=ft.Column([
            problem_image_view,
            problem_content
        ]),
        padding=20,
        expand=True
    )
    
    # 創建主佈局
    main_layout = ft.Row([
        sidebar,
        ft.VerticalDivider(width=1),
        content_area
    ], expand=True)
    
    # 將所有元素添加到頁面
    page.add(
        ft.Column([
            title_area,
            status_text,
            ft.Divider(height=1),
            main_layout
        ])
    )
    
    # 載入題目列表
    try:
        problems = problem_reader.get_all_problems()
        
        # 將題目添加到按鈕列表
        for number, path in problems:
            # 添加按鈕
            problem_buttons.controls.append(
                ft.ElevatedButton(
                    text=f"題目 {number}",
                    data=str(number),
                    width=200,
                    on_click=on_problem_button_click,
                    style=ft.ButtonStyle(
                        padding=15,
                        animation_duration=200
                    )
                )
            )
        
        # 更新狀態並顯示
        status_text.value = f"成功載入 {len(problems)} 題"
        status_text.color = SUCCESS_COLOR
        page.update()
        
        # 如果有題目，預設選擇第一題
        if problems:
            first_button = problem_buttons.controls[0]
            if isinstance(first_button, ft.ElevatedButton):
                first_button.bgcolor = PRIMARY_COLOR
                first_button.color = "#FFFFFF"  # 白色文字
            show_problem(str(problems[0][0]))
            
    except Exception as e:
        status_text.value = f"載入題目列表時出錯: {str(e)}"
        status_text.color = ERROR_COLOR
        # 添加更詳細的錯誤信息
        problem_buttons.controls.append(
            ft.Text(
                f"錯誤信息: {str(e)}",
                color=ERROR_COLOR,
                size=14,
                selectable=True
            )
        )
        page.update()

# 全局變量
problem_reader = ProblemReader()
analyzer = GeminiAnalyzer()
current_problem = None
current_problem_file = None

# 啟動應用
if __name__ == "__main__":
    ft.app(target=main) 