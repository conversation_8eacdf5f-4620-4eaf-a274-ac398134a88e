import sys
from pathlib import Path

# 將 src 目錄添加到系統路徑
sys.path.append(str(Path(__file__).parent / "src"))

from src.gemini import GeminiAnalyzer
from src.read import ProblemReader
import PIL.Image

def test_gemini_api():
    """測試 Gemini API 的基本功能"""
    print("=== 測試 Gemini API 功能 ===")
    
    # 創建問題讀取器
    reader = ProblemReader()
    
    # 獲取所有題目
    problems = reader.get_all_problems()
    if not problems:
        print("錯誤：沒有找到任何題目文件")
        return
    
    # 選擇第一題進行測試
    problem_number, problem_file = problems[0]
    print(f"選擇測試題目 {problem_number}: {problem_file.name}")
    
    # 創建 Gemini 分析器
    analyzer = GeminiAnalyzer()
    
    # 測試基本功能
    try:
        # 1. 測試讀取圖片
        print("\n1. 測試圖片讀取...")
        try:
            image = PIL.Image.open(problem_file)
            print(f"   讀取成功，圖片大小: {image.size}")
        except Exception as e:
            print(f"   圖片讀取失敗: {str(e)}")
            return
        
        # 2. 測試直接 API 調用
        print("\n2. 測試 API 直接調用...")
        api_result = analyzer.analyze_image(problem_file)
        if api_result.get("success", False):
            print("   API 調用成功!")
            
            # 顯示部分回應
            analysis = api_result.get("analysis", "")
            preview = analysis[:200] + "..." if len(analysis) > 200 else analysis
            print(f"   回應預覽: {preview}")
        else:
            print(f"   API 調用失敗: {api_result.get('error', '未知錯誤')}")
            return
        
        # 3. 測試完整分析
        print("\n3. 測試完整題目分析流程...")
        full_result = analyzer.get_problem_analysis(problem_file)
        if full_result.get("success", False):
            print("   完整分析成功!")
            
            # 顯示解析後的題目和答案
            ch_problem = full_result.get("chinese", {}).get("problem_question", "")
            ch_answer = full_result.get("chinese", {}).get("answer", "")
            
            print("\n   中文題目：")
            print("   " + ch_problem[:150] + "..." if len(ch_problem) > 150 else ch_problem)
            
            print("\n   中文答案：")
            print("   " + ch_answer[:150] + "..." if len(ch_answer) > 150 else ch_answer)
        else:
            print(f"   完整分析失敗: {full_result.get('error', '未知錯誤')}")
        
    except Exception as e:
        print(f"測試過程中發生錯誤: {str(e)}")
    
    print("\n測試完成！")

if __name__ == "__main__":
    test_gemini_api() 