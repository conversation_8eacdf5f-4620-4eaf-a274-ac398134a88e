html, body, :host {
  height: 100%;
}
:host {
  display: block;
  width: 100%;
  height: calc(100vh - 70px);
  overflow: hidden;
  background-color: #f7f7f8;
  position: relative;
  margin-top: 10px;
}

.chatgpt-root {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 0 20px 15px 20px;
}

.chatgpt-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 95%;
  background: white;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border-radius: 12px;
}

.chatgpt-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(90deg, #1976d2, #2196f3);
  color: white;
  font-size: 20px;
  font-weight: 600;
  height: 60px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
  border-radius: 12px 12px 0 0;
}

.chatgpt-body {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
}

.user, .ai {
  display: flex;
  margin-bottom: 24px;
  width: 100%;
}

.user {
  justify-content: flex-end;
}

.ai {
  justify-content: flex-start;
}

.chat-bubble {
  padding: 16px 22px;
  border-radius: 18px;
  max-width: 85%;
  font-size: 24px;
  line-height: 1.35;
  position: relative;
  margin: 10px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  word-break: break-word;
}

.chat-bubble.user {
  background-color: #1976d2;
  color: white;
  border-bottom-right-radius: 4px;
  padding-right: 18px;
}

.chat-bubble.ai {
  background-color: #f1f1f1;
  color: #333;
  border-bottom-left-radius: 4px;
  padding-left: 18px;
}

.chatgpt-footer {
  padding: 20px;
  background-color: white;
  border-top: 1px solid #eaeaea;
  position: relative;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 12px 12px;
}

.chatgpt-input-row {
  display: flex;
  width: 100%;
  height: 60px;
}

.chatgpt-input-row input {
  flex: 1;
  padding: 16px 24px;
  border: 1px solid #e0e0e0;
  border-radius: 8px 0 0 8px;
  font-size: 18px;
  background-color: #f9f9f9;
  transition: all 0.2s;
}

.chatgpt-input-row input:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
  background-color: white;
}

.chatgpt-input-row button {
  padding: 0 30px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 0 8px 8px 0;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chatgpt-input-row button:hover {
  background-color: #1565c0;
}

/* 滾動條樣式 */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 平板和電腦的邊距 */
@media (min-width: 768px) {
  .chatgpt-root {
    padding: 20px 30px;
  }
  
  .chatgpt-card {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
    border-radius: 12px;
  }
}
