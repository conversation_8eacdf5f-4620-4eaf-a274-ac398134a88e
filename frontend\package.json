{"name": "mis_teach", "version": "5.2.16", "copyright": "Copyright 2024 creativeL<PERSON><PERSON><PERSON>", "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people) and contributors", "homepage": "https://coreui.io/angular", "config": {"theme": "default", "coreui_library_short_version": "5.2", "coreui_library_docs_url": "https://coreui.io/angular/docs/"}, "scripts": {"ng": "ng", "start": "ng serve -o", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.13", "@angular/cdk": "^19.2.13", "@angular/common": "^19.2.13", "@angular/compiler": "^19.2.13", "@angular/core": "^19.2.13", "@angular/forms": "^19.2.13", "@angular/language-service": "^19.2.13", "@angular/platform-browser": "^19.2.13", "@angular/platform-browser-dynamic": "^19.2.13", "@angular/router": "^19.2.13", "@coreui/angular": "~5.4.13", "@coreui/angular-chartjs": "~5.4.13", "@coreui/chartjs": "~4.0.0", "@coreui/coreui": "^5.4.0", "@coreui/icons": "^3.0.1", "@coreui/icons-angular": "~5.4.13", "@coreui/utils": "^2.0.2", "@popperjs/core": "~2.11.8", "bootstrap": "^5.3.3", "chart.js": "^4.4.9", "joint-plus": "^4.0.1", "jointjs": "^3.7.7", "jquery": "^3.7.1", "lodash-es": "^4.17.21", "marked": "^15.0.11", "mathjax": "^3.2.2", "ng2-charts": "^7.0.0", "ngx-scrollbar": "^13.0.3", "rxjs": "~7.8.1", "tslib": "^2.7.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.13", "@angular/cli": "^19.2.13", "@angular/compiler-cli": "^19.2.13", "@angular/localize": "^19.2.13", "@types/jasmine": "^5.1.4", "@types/jquery": "^3.5.32", "@types/lodash-es": "^4.17.12", "@types/node": "^20.16.1", "install": "^0.13.0", "jasmine-core": "^5.2.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "sass": "^1.69.5", "sass-loader": "^13.3.2", "typescript": "~5.8.3"}, "engines": {"node": "^18.19.0 || ^20.9.0", "npm": ">= 9"}}