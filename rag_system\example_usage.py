#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG智能教學系統 - 使用示例
展示如何程式化使用RAG系統的各個功能
"""

import os
import sys
from pathlib import Path

# 添加當前目錄到Python路徑
sys.path.append(str(Path(__file__).parent))

from rag_processor import RAGProcessor
from rag_ai_responder import AIResponder
import config

def example_pdf_processing():
    """
    示例1: PDF處理和知識庫建立
    """
    print("🔄 示例1: PDF處理和知識庫建立")
    print("="*50)
    
    # 1. 初始化RAG處理器
    processor = RAGProcessor(verbose=True)
    
    # 2. 檢查PDF檔案
    pdf_files = list(config.PDF_DIR.glob("*.pdf"))
    if not pdf_files:
        print(f"❌ 在 {config.PDF_DIR} 目錄中沒有找到PDF檔案")
        print("💡 請將PDF教材檔案放入該目錄後再運行此示例")
        return None
    
    print(f"📁 找到 {len(pdf_files)} 個PDF檔案")
    
    # 3. 處理PDF檔案
    pdf_paths = [str(f) for f in pdf_files[:2]]  # 只處理前2個檔案作為示例
    success = processor.process_multiple_pdfs(pdf_paths)
    
    if not success:
        print("❌ PDF處理失敗")
        return None
    
    # 4. 創建知識點
    if not processor.create_knowledge_points():
        print("❌ 知識點創建失敗")
        return None
    
    # 5. 建立向量資料庫
    if not processor.build_knowledge_database():
        print("❌ 向量資料庫建立失敗")
        return None
    
    # 6. 保存結果
    processor.save_knowledge_points()
    
    # 7. 顯示摘要
    summary = processor.get_processing_summary()
    print(f"\n✅ 處理完成！")
    print(f"📊 摘要: {summary['structured_data_count']} 個內容塊, {summary['knowledge_points_count']} 個知識點")
    
    return processor

def example_ai_qa(processor=None):
    """
    示例2: AI問答系統
    """
    print("\n🤖 示例2: AI問答系統")
    print("="*50)
    
    # 1. 初始化AI回答系統
    ai_responder = AIResponder(
        language='chinese',  # 設定為中文
        rag_processor=processor
    )
    
    # 2. 檢查系統狀態
    status = ai_responder.get_system_status()
    print(f"🔍 系統狀態: {status}")
    
    if not status['database_ready']:
        print("❌ 知識庫未準備就緒，請先運行PDF處理示例")
        return
    
    # 3. 示例問題列表
    example_questions = [
        "什麼是作業系統？",
        "進程和線程的區別是什麼？",
        "virtual memory的作用",
        "file system如何工作？",
        "什麼是死鎖？"
    ]
    
    print(f"📝 將回答 {len(example_questions)} 個示例問題:")
    
    # 4. 逐一回答問題
    for i, question in enumerate(example_questions, 1):
        print(f"\n{'='*60}")
        print(f"問題 {i}: {question}")
        print('='*60)
        
        try:
            # 生成回答
            response = ai_responder.answer_question(question)
            
            # 格式化並顯示回答
            formatted_response = ai_responder.format_response_for_display(response)
            print(formatted_response)
            
        except Exception as e:
            print(f"❌ 回答問題時發生錯誤: {e}")
    
    return ai_responder

def example_language_switching():
    """
    示例3: 語言切換功能
    """
    print("\n🌐 示例3: 語言切換功能")
    print("="*50)
    
    # 初始化AI回答系統
    ai_responder = AIResponder(language='chinese')
    
    question = "什麼是作業系統？"
    
    # 中文回答
    print("📝 中文回答:")
    print("-" * 30)
    ai_responder.set_language('chinese')
    response_zh = ai_responder.answer_question(question, use_ai=False)  # 使用備用回答避免AI模型依賴
    print(ai_responder.format_response_for_display(response_zh))
    
    # 英文回答
    print("\n📝 English Response:")
    print("-" * 30)
    ai_responder.set_language('english')
    response_en = ai_responder.answer_question("What is an operating system?", use_ai=False)
    print(ai_responder.format_response_for_display(response_en))

def example_batch_processing():
    """
    示例4: 批量處理和搜索
    """
    print("\n📚 示例4: 批量處理和搜索")
    print("="*50)
    
    # 初始化系統
    processor = RAGProcessor(verbose=False)  # 關閉詳細輸出
    ai_responder = AIResponder(rag_processor=processor)
    
    # 批量問題
    batch_questions = [
        "memory management",
        "process scheduling", 
        "file system",
        "network protocols",
        "database indexing"
    ]
    
    print(f"🔍 批量搜索 {len(batch_questions)} 個概念:")
    
    for question in batch_questions:
        print(f"\n🔎 搜索: {question}")
        
        # 搜索相關知識點
        search_results = ai_responder.search_knowledge(question, top_k=3)
        
        if search_results:
            print(f"  ✅ 找到 {len(search_results)} 個相關結果")
            for i, result in enumerate(search_results, 1):
                metadata = result.get('metadata', {})
                similarity = result.get('similarity', 0)
                print(f"    {i}. 章節: {metadata.get('chapter', 'N/A')} | 相似度: {similarity:.2f}")
        else:
            print("  ❌ 未找到相關結果")

def example_configuration():
    """
    示例5: 配置管理
    """
    print("\n⚙️ 示例5: 配置管理")
    print("="*50)
    
    # 顯示當前配置
    print("📋 當前配置摘要:")
    print(config.get_config_summary())
    
    # 驗證配置
    print(f"\n🔍 配置驗證: {'通過' if config.validate_config() else '失敗'}")
    
    # 顯示支援的語言
    print(f"\n🌐 支援的語言:")
    for lang_code, lang_info in config.SUPPORTED_LANGUAGES.items():
        print(f"  • {lang_code}: {lang_info['name']}")
    
    # 顯示資料目錄狀態
    print(f"\n📁 資料目錄狀態:")
    directories = [
        ("PDF目錄", config.PDF_DIR),
        ("知識庫目錄", config.KNOWLEDGE_DB_DIR),
        ("輸出目錄", config.OUTPUT_DIR)
    ]
    
    for name, path in directories:
        exists = path.exists()
        file_count = len(list(path.glob("*"))) if exists else 0
        print(f"  • {name}: {path} ({'存在' if exists else '不存在'}, {file_count} 個檔案)")

def main():
    """
    主函數 - 運行所有示例
    """
    print("🎓 RAG智能教學系統 - 使用示例")
    print("="*80)
    
    try:
        # 示例1: PDF處理
        processor = example_pdf_processing()
        
        # 示例2: AI問答 (需要先完成PDF處理)
        if processor:
            example_ai_qa(processor)
        
        # 示例3: 語言切換
        example_language_switching()
        
        # 示例4: 批量處理
        example_batch_processing()
        
        # 示例5: 配置管理
        example_configuration()
        
        print("\n✅ 所有示例運行完成！")
        print("💡 您現在可以運行 'python rag_main.py' 來使用完整的互動式系統")
        
    except Exception as e:
        print(f"❌ 示例運行失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
