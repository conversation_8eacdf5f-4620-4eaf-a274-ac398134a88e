import os
import re
import json
import time
import google.generativeai as genai
from typing import Dict, List, Any, Tuple, Optional
from PIL import Image
import cv2

# 配置 Gemini API
GOOGLE_API_KEY = ""
genai.configure(api_key=GOOGLE_API_KEY)
model = genai.GenerativeModel("gemini-1.5-flash")

# --- 輔助函數 ---
def read_image(image_path: str) -> bytes:
    """讀取圖片檔案並返回二進位數據"""
    try:
        with open(image_path, "rb") as img_file:
            return img_file.read()
    except Exception as e:
        print(f"❌ 圖片讀取錯誤 {image_path}: {e}")
        return b""

def parse_filename(filename: str) -> Tuple[str, str, str, Optional[str], Optional[str]]:
    """從檔名解析年份、學校、科系、可能的科目和頁碼資訊"""
    base = os.path.basename(filename).replace(".png", "").replace(".jpg", "").replace(".jpeg", "")
    match = re.match(r"(\d+)-(.+?)-(.+?)-(.+?)_page_(\d+)", base)
    if match:
        year = match.group(1)
        subject = match.group(2)
        school = match.group(3)
        department = match.group(4)
        page_number = match.group(5)
        return year, school, department, subject, page_number
    else:
        parts = base.split('-')
        year = parts[0] if parts and parts[0].isdigit() else ""
        subject = parts[1] if len(parts) > 1 else ""
        school = parts[-2] if len(parts) >= 3 else ""
        department_parts = parts[-1].split('_')
        department = department_parts[0] if department_parts else ""
        page_number_match = re.search(r'_page_(\d+)', base)
        page_number = page_number_match.group(1) if page_number_match else None
        return year, school, department, subject, page_number

def extract_json_from_response(response_text: str) -> List[Dict[str, Any]]:
    """從模型回應中提取 JSON 資料"""
    json_pattern = r'\[\s*{.*}\s*\]'
    json_match = re.search(json_pattern, response_text, re.DOTALL)

    if json_match:
        try:
            return json.loads(json_match.group(0))
        except json.JSONDecodeError:
            pass

    code_block_pattern = r'```(?:json)?(.*?)```'
    code_blocks = re.findall(code_block_pattern, response_text, re.DOTALL)

    for block in code_blocks:
        try:
            return json.loads(block.strip())
        except json.JSONDecodeError:
            continue

    try:
        return json.loads(response_text.strip())
    except json.JSONDecodeError:
        print("❌ 所有 JSON 解析嘗試均失敗")
        return []

def clean_json_result(result: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """清理和標準化 JSON 結果"""
    for question in result:
        for field, default in [
            ("image_file", []),
            ("options", []),
            ("school", ""),
            ("department", ""),
            ("year", ""),
            ("question_number", ""),
            ("question_text", ""),
            ("type", "other"),
        ]:
            if field not in question:
                question[field] = default
            elif field in ["image_file", "options"] and not isinstance(question[field], list):
                question[field] = [question[field]] if question[field] else []
    return result
def clean_image_filenames_in_json_file(json_file_path: str) -> None:
    """處理 JSON 檔案中的 image_file 欄位，將「圖X」改為「_X」"""
    try:
        # 讀取 JSON 檔案
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 處理每個項目
        for item in data:
            if "image_file" in item and isinstance(item["image_file"], list):
                for i, img_file in enumerate(item["image_file"]):
                    # 使用正則表達式匹配「圖X」模式並替換為「_X」
                    new_img_file = re.sub(r'圖(\d+)', r'\1', img_file)
                    if new_img_file != img_file:
                        print(f"修改檔名: {img_file} -> {new_img_file}")
                        item["image_file"][i] = new_img_file
        
        # 寫回 JSON 檔案
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        print(f"✅ 成功處理 JSON 檔案: {json_file_path}")
    except Exception as e:
        print(f"❌ 處理 JSON 檔案時發生錯誤: {e}")

# --- 主要分析函數 (僅使用 Gemini) ---
def analyze_image(image_path: str, retry_count: int = 2) -> List[Dict[str, Any]]:
    """分析圖片並提取結構化題目數據，支援重試機制"""
    year, school, department, subject, page_number = parse_filename(image_path)
    image_data = read_image(image_path)
    if not image_data:
        return []
    json_example = '''[
  {
    "school": "國立中正大學",
    "department": "資訊工程研究所",
    "year": "101",
    "question_number": "1-a",
    "question_text": "請計算 $\\int_0^1 x^2 \\,dx$ 的值。",
    "options": ["a. $\\frac{1}{3}$", "b. $\\frac{1}{2}$", "c. $\\frac{2}{3}$", "d. $1$"],
    "type": "single-choice",
    "image_file": [],
  }
]'''
    prompt = f"""
    這是一張考卷圖片，請依據圖片內容擷取出所有題目的結構化資料。請為每一題產生一個 JSON 物件，並包含以下欄位：
- "school": "{school}"（學校名稱）
- "department": "{department}"（系所名稱）
- "year": "{year}"（考試年份）
- "question_number": 題號，例如：
  - 第一題為 "1"
  - 若第一題包含 a、b、c 小題，請分別標示為 "1-a"、"1-b"、"1-c"
  - 若第二題有 a、b 小題，則為 "2-a"、"2-b"
- "question_text": 題目的完整文字內容，**不可自行省略題目文字，須完整返回題目敘述**
  - 若包含數學符號、公式、運算式，請以 LaTeX 語法表示，如：
    `"請計算 $\\int_0^1 x^2 \\,dx$ 的值"`
- "options": 如果是選擇題，請完整列出所有選項內容，每個選項需包含前綴的英文字母 (a.、b.、c.、d.) 並保留與題目一致的文字描述。
  例如題目包含選項 (A)*********** (B) *********** (C) *************** (D) 140.123.256.2，
  請轉換為：["a. ***********", "b. ***********", "c. ***************", "d. 140.123.256.2"]若選項為中文，也請以相同格式回傳，如 ["a. 資料庫", "b. 網路", "c. 作業系統", "d. 程式語言"]
  若無選項請設為 []
- "type": 題型的判斷方式應優先依據考卷上的文字描述（如「選擇題」、「是非題」、「問答題」等），若無明確描述，則根據題目的結構與內容進行判斷，並從以下類別中擇一：
  - "single-choice"：單選題
  - "multiple-choice"：多選題
  - "fill-in-the-blank"：填空題
  - "true-false"：是非題
  - "short-answer"：簡答題/問答題
  - "long-answer"：申論題／長答題
  - "choice-answer"：選填題
  - "draw-answer"：畫圖題
  - "coding-answer"：程式撰寫題
  若題目類型無法明確歸類至上述任一類別，請設為 "other"
  "type": "other",
- "image_file": 如果題目包含附圖（如表格、圖形、程式碼等），請**描述**其檔案名格式：
  `"{year}-{subject}-{school}-{department}_page_{page_number}_1、2、3...(代表題號代表圖1圖2...用_123表示).png"`
  若無附圖請設為 []
補充說明：
- 每個小題（如 "1-a", "1-b"）都需為獨立 JSON 物件
- 附圖包括：程式碼、圖表、表格、手寫圖、圖例等皆應截取記錄
- 若文字中有數學運算或符號，請盡量以 LaTeX 標記
- 最終請回傳一個 JSON 陣列，不要包含任何說明文字或解釋

    請以正確的JSON陣列格式回傳所有題目的資料。不要包含任何解釋文字，只回傳JSON資料。格式範例：
    {json_example}
    """
    for attempt in range(retry_count + 1):
        try:
            response = model.generate_content([
                {"text": prompt},
                {"inline_data": {"mime_type": "image/png", "data": image_data}}
            ])
            response_text = response.text if hasattr(response, 'text') else str(response)
            if attempt == 0:
                print(f"📨 Gemini 第一次回傳內容:\n{response_text[:1000]}...\n")
            result = extract_json_from_response(response_text)
            if result:
                result = clean_json_result(result)
                return result
            if attempt < retry_count:
                print(f"⚠️ 重試 {attempt+1}/{retry_count} - JSON 解析失敗")
                time.sleep(2)
        except Exception as e:
            if attempt < retry_count:
                print(f"⚠️ 重試 {attempt+1}/{retry_count} - 錯誤: {e}")
                time.sleep(2)
            else:
                print(f"❌ 處理 {image_path} 的所有嘗試均失敗: {e}")
        return []

def process_folder(
    image_folder: str = "exam_img",
    output_file: str = "./output_json/new_exam_output.json",
    failed_log: str = "./output_json/failed_files.txt"
):
    """處理資料夾中的所有圖片，使用 Gemini 分析並將結果寫入 JSON"""
    files_processed = 0
    files_successful = 0
    failed_files = []
    all_results = []

    if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
        with open(output_file, "r", encoding="utf-8") as f:
            try:
                all_results = json.load(f)
            except json.JSONDecodeError:
                print("⚠️ 無法解析 output.json，初始化為空陣列")
                all_results = []
    else:
        all_results = []

    for filename in os.listdir(image_folder):
        if filename.endswith((".png", ".jpg", ".jpeg")):
            image_path = os.path.join(image_folder, filename)
            print(f"🔍 處理圖片：{filename}")
            files_processed += 1

            analysis_result = analyze_image(image_path) # 使用 Gemini 分析
            if analysis_result:
                all_results.extend(analysis_result)
                files_successful += 1
                print(f"✅ 已分析：{filename}, 找到 {len(analysis_result)} 個題目")
                with open(output_file, "w", encoding="utf-8") as f:
                    json.dump(all_results, f, ensure_ascii=False, indent=4)
            else:
                print(f"⚠️ Gemini 分析失敗：{filename}")
                failed_files.append(filename)

    if failed_files:
        with open(failed_log, "w", encoding="utf-8") as f:
            for file in failed_files:
                f.write(file + "\n")
        print(f"📝 失敗檔名已記錄在：{failed_log}")

def retry_failed_files(
    failed_file: str = "./output_json/failed_files.txt",
    image_folder: str = "exam_img",
    output_file: str = "./output_json/new_exam_output.json"):
    if not os.path.exists(failed_file):
        print("❌ 找不到 failed_files.txt")
        return
    with open(failed_file, "r", encoding="utf-8") as f:
        failed_filenames = [line.strip() for line in f if line.strip()]
    remaining_files = []
    all_results = []
    if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
        with open(output_file, "r", encoding="utf-8") as f:
            try:
                all_results = json.load(f)
            except json.JSONDecodeError:
                print("⚠️ 無法解析 output.json，初始化為空陣列")
                all_results = []

    for filename in failed_filenames:
        image_path = os.path.join(image_folder, filename)
        print(f"\n🔄 重新處理失敗檔案：{filename}")
        analysis_result = analyze_image(image_path)
        if analysis_result:
            all_results.extend(analysis_result)
            print(f"✅ 重新分析成功：{filename}, 找到 {len(analysis_result)} 個題目")
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(all_results, f, ensure_ascii=False, indent=4)
        else:
            print(f"⚠️ 重新分析失敗：{filename}")
            remaining_files.append(filename)

    if remaining_files:
        with open(failed_file, "w", encoding="utf-8") as f:
            for name in remaining_files:
                f.write(name + "\n")
        print(f"📝 剩餘失敗檔名已更新在：{failed_file}")
    else:
        print("🎉 所有檔案已成功分析！")

if __name__ == "__main__":
    #process_folder()  # 處理資料夾中的所有圖片並使用 Gemini 分析
    #retry_failed_files() # 重新處理分析失敗的檔案
    clean_image_filenames_in_json_file(r'./output_json/new_exam_output.json')
