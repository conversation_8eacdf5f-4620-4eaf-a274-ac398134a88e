import torch
from PIL import Image
import os
import json
import re
from ultralytics import YOLO

model = YOLO(r'D:\GitHub\MIS_TeachAI\yolov8\runs\test_dataset6\weights\best.pt')
output_dir = "切割後考卷"

if not os.path.exists(output_dir):
    os.makedirs(output_dir)

def process_exam_image(image_path, output_dir, file_info=None):
    img = Image.open(image_path).convert('RGB')
    results = model(img)

    question_counter = 1
    for result in results:
        for box in result.boxes:
            xmin, ymin, xmax, ymax = box.xyxy[0].tolist()
            confidence = box.conf[0].item()
            if confidence > 0.5:
                cropped_img = img.crop((xmin, ymin, xmax, ymax))

                if file_info:
                    school = file_info.get("school", "未知學校")
                    department = file_info.get("department", "未知系所")
                    year = file_info.get("year", "未知年份")
                    question_number = file_info.get("question_number", f"題目{question_counter}")
                else:
                    # 從檔名中提取資訊，假設是「國立雲林科技大學_資訊工程研究所_108.png」
                    base_name = os.path.splitext(os.path.basename(image_path))[0]
                    parts = base_name.split("_")
                    if len(parts) >= 3:
                        school = parts[0]
                        department = parts[1]
                        year = parts[2]
                    else:
                        school, department, year = "未知學校", "未知系所", "未知年份"
                    question_number = f"{question_counter}"

                filename = f"{school}_{department}_{year}_{question_number}.png"
                save_path = os.path.join(output_dir, filename)
                cropped_img.save(save_path)
                print(f"✅ 已儲存: {save_path}")
                question_counter += 1

def process_folder(folder_path, output_dir):
    for filename in os.listdir(folder_path):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_path = os.path.join(folder_path, filename)
            process_exam_image(image_path, output_dir)

def process_json(json_path, output_dir, exam_img_path):
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
        for item in data:
            if item.get("image_file"):
                for img_file in item["image_file"]:
                    image_path = os.path.join(exam_img_path, img_file)
                    if os.path.exists(image_path):
                        process_exam_image(image_path, output_dir, item)
                    else:
                        print(f"⚠️ 圖片檔案不存在: {image_path}")

def update_json_with_output_filenames(output_dir, json_path):
    """
    使用切割後的圖片檔名更新 JSON 檔案中的 image_file 欄位（包括原本為空的列表）。
    """
    output_filenames = [f for f in os.listdir(output_dir) if f.endswith(".png") and "_question_" in f]

    with open(json_path, 'r', encoding='utf-8') as f:
        exam_data = json.load(f)

    updated_count = 0
    not_found_count = 0
    empty_list_updated_count = 0
    renamed_count = 0

    for item in exam_data:
        if "image_file" in item:
            if isinstance(item["image_file"], list):
                # 處理 image_file 中的每個檔名，將「圖X」改為「_X」
                for i, img_file in enumerate(item["image_file"]):
                    if "圖" in img_file:
                        new_img_file = re.sub(r'圖(\d+)', r'_\1', img_file)
                        item["image_file"][i] = new_img_file
                        renamed_count += 1
                
                if item["image_file"]:
                    original_image_file = item["image_file"][0]
                    found_match = False
                    for output_filename in output_filenames:
                        if original_image_file in output_filename:
                            item["image_file"] = [os.path.basename(output_filename)]
                            updated_count += 1
                            found_match = True
                            break
                    if not found_match:
                        not_found_count += 1
                        print(f"⚠️ 警告：在輸出檔名中找不到原始圖片 '{original_image_file}' 的匹配項。")
                else:
                    # image_file 為空列表，嘗試根據其他資訊匹配
                    # ... (原有代碼)
    
    print(f"✅ 成功重命名 {renamed_count} 個包含「圖X」的檔名為「_X」格式。")
    # ... (其他原有的打印信息)

if __name__ == "__main__":
    exam_img_folder = "../ocr_and_testcode/exam_img"
    json_file_path = "ocr_and_testcode/output_json/exam_output.json.json"  # 請替換成實際 JSON 路徑

    print("選擇處理方式：")
    print("1. 處理 exam_img 資料夾中的所有圖片")
    print("2. 處理 .json 檔案中 image_file 不為空的項目")

    choice = input("請輸入您的選擇 (1 或 2): ")

    if choice == '1':
        process_folder(exam_img_folder, output_dir)
    elif choice == '2是錯的asfasfafssssfafasfasf':
        if os.path.exists(json_file_path) and os.path.exists(exam_img_folder):
            process_json(json_file_path, output_dir, exam_img_folder)
        else:
            print("❌ 錯誤：.json 檔案或 exam_img 資料夾不存在。")
    else:
        print("❌ 無效的選擇。")
