<c-card>
  <c-card-header>
    <strong>繪圖板</strong>
  </c-card-header>
  <c-card-body>
<div class="whiteboard-container">
  <div class="whiteboard-controls mb-3">
    <c-button-group role="group" aria-label="顏色選擇">
      <button cButton color="dark" [active]="currentColor === '#000'" (click)="setColor('#000')">黑色</button>
      <button cButton color="primary" [active]="currentColor === '#0d6efd'" (click)="setColor('#0d6efd')">藍色</button>
      <button cButton color="danger" [active]="currentColor === '#dc3545'" (click)="setColor('#dc3545')">紅色</button>
      <button cButton color="success" [active]="currentColor === '#198754'" (click)="setColor('#198754')">綠色</button>
    </c-button-group>
    <div class="ms-3 d-inline-block">
      <label for="brushSize" class="form-label me-2">畫筆大小:</label>
      <input type="range" class="form-range d-inline-block w-auto" min="1" max="20" step="1" id="brushSize" [(ngModel)]="brushSize" (ngModelChange)="updateCtxBrushSize($event)">
      <span class="ms-2">{{brushSize}}px</span> 
    </div>
    <c-button-group class="ms-3" role="group" aria-label="工具">
      <button cButton color="light" (click)="undo()" [disabled]="undoStack.length <= 1">
        <svg cIcon name="cilActionUndo" size="sm" class="me-1"></svg> 復原
      </button>
      <button cButton color="light" (click)="redo()" [disabled]="redoStack.length === 0">
        <svg cIcon name="cilActionRedo" size="sm" class="me-1"></svg> 重作
      </button>
      <button cButton color="secondary" (click)="setMode('erase')" [active]="currentMode === 'erase'">
        <svg cIcon name="cilPaintBucket" size="sm" class="me-1"></svg> 橡皮擦
      </button>
      <button cButton color="light" (click)="clearCanvas()">
        <svg cIcon name="cilTrash" size="sm" class="me-1"></svg>清除
      </button>
      <button cButton color="info" (click)="saveImage()">
        <svg cIcon name="cilSave" size="sm" class="me-1"></svg>儲存
      </button>
      <button cButton color="warning" (click)="bgInput.click()">
        <svg cIcon name="cilImagePlus" size="sm" class="me-1"></svg> 載入背景
      </button>
      <button cButton color="danger" (click)="removeBackgroundImage()" [disabled]="!backgroundImage">
        <svg cIcon name="cilBan" size="sm" class="me-1"></svg> 移除背景
      </button>
      <input type="file" accept="image/*" #bgInput style="display: none" (change)="handleBackgroundImageLoad($event)">
    </c-button-group>
  </div>
  <div class="whiteboard-canvas-container">
    <canvas #whiteboardCanvas 
            class="whiteboard-canvas" 
            (mousedown)="startDrawing($event)" 
            (mousemove)="draw($event)" 
            (mouseup)="stopDrawing()" 
            (mouseleave)="stopDrawing()"
            (touchstart)="startDrawingTouch($event)"
            (touchmove)="drawTouch($event)"
            (touchend)="stopDrawing()">
    </canvas>
  </div>
</div>
  </c-card-body>
</c-card>
