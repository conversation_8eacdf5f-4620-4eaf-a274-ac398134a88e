import base64
import os
import google.generativeai as genai
from pathlib import Path
from typing import Dict, Any, Optional
from PIL import Image
import io
import re

# 配置API密鑰
API_KEY = "AIzaSyCq5BfnH86INcmZjgD_tKIlauJc5Ey764c"

class GeminiAnalyzer:
    """使用Google Gemini API分析題目圖片"""
    
    def __init__(self):
        """初始化Gemini分析器"""
        try:
            genai.configure(api_key=API_KEY)
            # 使用 gemini-2.0-flash 模型
            self.model = genai.GenerativeModel('gemini-2.0-flash')
            self.initialized = True
        except Exception as e:
            print(f"初始化Gemini分析器時出錯: {str(e)}")
            self.initialized = False
    
    def get_problem_analysis(self, problem_file: Path) -> Dict[str, Any]:
        """分析題目圖片
        
        Args:
            problem_file: 題目圖片的路徑
            
        Returns:
            包含分析結果的字典
        """
        # 檢查初始化狀態
        if not self.initialized:
            return {
                "success": False,
                "error": "Gemini分析器未正確初始化"
            }
            
        try:
            # 檢查文件是否存在
            if not problem_file.exists():
                return {
                    "success": False,
                    "error": f"圖片不存在: {problem_file}"
                }
            
            # 讀取圖片
            try:
                image = Image.open(problem_file)
            except Exception as e:
                return {
                    "success": False,
                    "error": f"圖片讀取失敗: {str(e)}"
                }
            
            # 準備提示詞
            prompt = """
            請分析這張考試題目圖片，並提取題目內容然後題目要中英文對照。
            
            如果圖片包含樹狀結構（如二元樹、AVL樹等），請使用以下格式輸出：
            1. 首先輸出題目文字
            2. 然後使用文字版的圖表示
          
            
            請注意：
            1. 保持樹的層級結構清晰
            2. 如果有程式碼，請完整保留其格式和行號
            3. 如果有數學符號，請使用 LaTeX 格式表示
            4. 不要添加任何額外的說明文字
            """
            
            # 使用API分析圖片
            try:
                response = self.model.generate_content([prompt, image])
                
                if not response or not response.text:
                    return {
                        "success": False,
                        "error": "無法獲取分析結果"
                    }
                
                # 返回分析結果
                return {
                    "success": True,
                    "chinese": {
                        "problem_question": response.text.strip()
                    }
                }
                
            except Exception as e:
                error_message = str(e)
                if "quota" in error_message.lower():
                    return {
                        "success": False,
                        "error": "API配額已用完，請稍後再試"
                    }
                return {
                    "success": False,
                    "error": f"API調用失敗: {error_message}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"分析過程中出錯: {str(e)}"
            }

# 測試代碼
if __name__ == "__main__":
    from read import ProblemReader
    
    # 測試分析器
    analyzer = GeminiAnalyzer()
    reader = ProblemReader()
    
    # 測試第一題
    problems = reader.get_all_problems()
    if problems:
        problem_number, problem_file = problems[0]
        print(f"分析題目 {problem_number}:")
        
        result = analyzer.get_problem_analysis(problem_file)
        if result.get("success", False):
            print("\n題目內容:")
            print(result["chinese"]["problem_question"])
        else:
            print(f"錯誤: {result.get('error', '未知錯誤')}") 