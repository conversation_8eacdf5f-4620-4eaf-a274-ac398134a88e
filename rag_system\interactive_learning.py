#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互動式學習系統主程式
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from intelligent_tutor import IntelligentTutor

class InteractiveLearningSystem:
    """互動式學習系統"""
    
    def __init__(self):
        """初始化系統"""
        self.tutor = IntelligentTutor()
        self.in_guidance_mode = False
        
    def show_welcome(self):
        """顯示歡迎訊息"""
        print("="*70)
        print("🎓 智能對話教學系統")
        print("="*70)
        print()
        print("🌟 功能特色：")
        print("  • 🧠 智能判斷問題類型，自動選擇教學方式")
        print("  • 🎯 引導式教學，逐步建立理解")
        print("  • 🔄 動態評估理解程度")
        print("  • 🌐 中文問答，英文資料庫搜索")
        print()
        print("💡 使用說明：")
        print("  • 直接輸入您的問題開始學習")
        print("  • 在引導過程中回答我的問題")
        print("  • 輸入 'next' 或 '下一題' 學習新概念")
        print("  • 輸入 'quit' 或 'exit' 退出系統")
        print()
        print("🚀 讓我們開始學習吧！")
        print("="*70)
    
    def handle_special_commands(self, user_input: str) -> bool:
        """處理特殊命令"""
        user_input = user_input.lower().strip()
        
        if user_input in ['quit', 'exit', '退出', '結束']:
            print("\n👋 感謝使用智能教學系統！")
            print("🎓 希望您學習愉快！")
            return True
        
        elif user_input in ['next', '下一題', '下一個', 'new']:
            print("\n🎯 請問您想學習什麼新概念？")
            self.in_guidance_mode = False
            self.tutor.current_topic = None
            return False
        
        elif user_input in ['deeper', '深入', '更多']:
            if self.tutor.current_topic:
                print(f"\n🔍 讓我們深入了解 '{self.tutor.current_topic}' 的相關概念...")
                # 可以在這裡添加深入學習的邏輯
            else:
                print("\n💡 請先問一個問題，然後我們可以深入探討。")
            return False
        
        elif user_input in ['help', '幫助', '說明']:
            self.show_help()
            return False
        
        elif user_input in ['status', '狀態']:
            self.show_status()
            return False
        
        return False
    
    def show_help(self):
        """顯示幫助訊息"""
        print("\n📚 系統說明")
        print("-" * 40)
        print("🎯 學習命令：")
        print("  • 直接問問題 - 開始新的學習主題")
        print("  • next/下一題 - 學習新概念")
        print("  • deeper/深入 - 深入當前主題")
        print()
        print("🔧 系統命令：")
        print("  • help/幫助 - 顯示此說明")
        print("  • status/狀態 - 顯示學習狀態")
        print("  • quit/退出 - 退出系統")
        print()
        print("💡 學習技巧：")
        print("  • 系統會自動判斷是否需要引導式教學")
        print("  • 在引導過程中積極回答問題")
        print("  • 理解程度達到80%後可進入下一題")
    
    def show_status(self):
        """顯示當前狀態"""
        print("\n📊 學習狀態")
        print("-" * 40)
        print(f"當前主題: {self.tutor.current_topic or '無'}")
        print(f"教學模式: {self.tutor.teaching_mode or '無'}")
        print(f"理解程度: {self.tutor.student_understanding}%")
        print(f"對話次數: {len(self.tutor.conversation_history)}")
        print(f"引導模式: {'是' if self.in_guidance_mode else '否'}")
    
    def run(self):
        """運行互動式學習系統"""
        self.show_welcome()
        
        while True:
            try:
                # 根據當前狀態顯示不同的提示
                if self.in_guidance_mode:
                    user_input = input("\n💭 您的想法或回答: ").strip()
                else:
                    user_input = input("\n🤔 請問您想學習什麼？ ").strip()
                
                if not user_input:
                    print("💡 請輸入您的問題或命令。")
                    continue
                
                # 處理特殊命令
                if self.handle_special_commands(user_input):
                    break
                
                # 處理學習內容
                if self.in_guidance_mode and self.tutor.teaching_mode == 'guided':
                    # 在引導模式中繼續對話
                    response = self.tutor.continue_guidance(user_input)
                    print(f"\n🎓 {response}")
                    
                    # 檢查是否完成學習
                    if self.tutor.student_understanding >= 80:
                        self.in_guidance_mode = False
                        print("\n🎉 恭喜！您已經掌握了這個概念！")
                
                else:
                    # 處理新問題
                    response = self.tutor.handle_question(user_input)
                    print(response)
                    
                    # 如果是引導模式，進入引導狀態
                    if self.tutor.teaching_mode == 'guided':
                        self.in_guidance_mode = True
                        print("\n💡 請回答上面的問題，或分享您的想法...")
                    else:
                        print("\n✅ 回答完成！您可以問下一個問題。")
                
            except KeyboardInterrupt:
                print("\n\n👋 感謝使用智能教學系統！")
                break
            except Exception as e:
                print(f"\n❌ 系統錯誤: {e}")
                print("💡 請重新輸入您的問題。")

def main():
    """主函數"""
    try:
        learning_system = InteractiveLearningSystem()
        learning_system.run()
    except Exception as e:
        print(f"❌ 系統啟動失敗: {e}")
        print("💡 請檢查:")
        print("  1. Ollama是否運行: ollama serve")
        print("  2. 模型是否可用: ollama list")
        print("  3. 向量資料庫是否建立")

if __name__ == "__main__":
    main()
