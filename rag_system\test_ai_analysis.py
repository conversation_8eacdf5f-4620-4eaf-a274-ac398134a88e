#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試AI驅動的問題分析功能
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from rag_ai_responder import AIResponder
import json

def test_ai_question_analysis():
    """測試AI問題分析功能"""
    print("🧪 測試AI驅動的問題分析")
    print("="*60)
    
    ai_responder = AIResponder(language='chinese')
    
    test_questions = [
        "什麼是銀行家演算法？",
        "為什麼需要虛擬記憶體？",
        "作業系統和資料庫管理系統有什麼區別？",
        "如何解決死鎖問題？",
        "如何實作優先權排程演算法？",
        "評估FIFO和SJF排程演算法的效能差異",
        "What is operating system?",
        "How does virtual memory work?",
        "Compare different scheduling algorithms"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 測試 {i}: {question}")
        print("-" * 50)
        
        try:
            # 測試AI分析
            analysis = ai_responder.analyze_question(question)
            
            print("📊 AI分析結果:")
            print(f"  問題類型: {analysis['question_type']}")
            print(f"  複雜度: {analysis['complexity']}")
            print(f"  學習層次: {analysis['learning_level']}")
            print(f"  概念類別: {analysis['concept_categories']}")
            print(f"  關鍵概念: {analysis['key_concepts']}")
            print(f"  需要引導: {analysis['needs_guidance']}")
            print(f"  教學方式: {analysis['teaching_approach']}")
            print(f"  問題語言: {analysis['question_language']}")
            
            # 測試模板選擇
            template_type = ai_responder._select_teaching_template(analysis)
            print(f"  選擇模板: {template_type}")
            
            # 測試學習建議生成
            suggestions = ai_responder._generate_study_suggestions(analysis)
            print(f"  學習建議: {suggestions}")
            
        except Exception as e:
            print(f"❌ 分析失敗: {e}")
            import traceback
            traceback.print_exc()

def test_ai_vs_rule_based():
    """比較AI分析和規則分析的差異"""
    print("\n🆚 AI分析 vs 規則分析比較")
    print("="*60)
    
    ai_responder = AIResponder(language='chinese')
    
    test_questions = [
        "銀行家演算法如何避免死鎖？",
        "什麼是進程？",
        "比較FIFO和LRU頁面置換演算法"
    ]
    
    for question in test_questions:
        print(f"\n🔍 問題: {question}")
        print("-" * 40)
        
        try:
            # AI分析
            ai_analysis = ai_responder.analyze_question(question)
            print("🤖 AI分析:")
            print(f"  類型: {ai_analysis['question_type']}")
            print(f"  複雜度: {ai_analysis['complexity']}")
            print(f"  概念: {ai_analysis['key_concepts']}")
            
            # 備用分析
            fallback_analysis = ai_responder._fallback_analysis(question)
            print("📋 備用分析:")
            print(f"  類型: {fallback_analysis['question_type']}")
            print(f"  複雜度: {fallback_analysis['complexity']}")
            print(f"  概念: {fallback_analysis['key_concepts']}")
            
        except Exception as e:
            print(f"❌ 比較失敗: {e}")

def test_full_ai_workflow():
    """測試完整的AI驅動工作流程"""
    print("\n🔄 測試完整AI驅動工作流程")
    print("="*60)
    
    ai_responder = AIResponder(language='chinese')
    
    test_question = "什麼是銀行家演算法？"
    print(f"🔍 測試問題: {test_question}")
    
    try:
        # 1. AI問題分析
        print("\n1️⃣ AI問題分析...")
        analysis = ai_responder.analyze_question(test_question)
        print(f"✅ 分析完成: {analysis['question_type']} | {analysis['complexity']}")
        
        # 2. 搜索知識
        print("\n2️⃣ 搜索相關知識...")
        search_results = ai_responder.search_knowledge(test_question)
        print(f"✅ 找到 {len(search_results)} 個結果")
        
        # 3. 提取結構化資訊
        print("\n3️⃣ 提取結構化資訊...")
        structured_info = ai_responder.extract_structured_info(search_results)
        print(f"✅ 提取完成: {len(structured_info.get('chapters', []))} 章節")
        
        # 4. 選擇教學模板
        print("\n4️⃣ 選擇教學模板...")
        template_type = ai_responder._select_teaching_template(analysis)
        print(f"✅ 選擇模板: {template_type}")
        
        # 5. 生成相關概念
        print("\n5️⃣ 生成相關概念...")
        concepts = ai_responder._generate_related_concepts(search_results, analysis)
        print(f"✅ 相關概念: {concepts}")
        
        # 6. 生成學習建議
        print("\n6️⃣ 生成學習建議...")
        suggestions = ai_responder._generate_study_suggestions(analysis)
        print(f"✅ 學習建議: {suggestions}")
        
        print("\n🎉 完整工作流程測試成功！")
        
    except Exception as e:
        print(f"❌ 工作流程測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_ai_connectivity():
    """測試AI連接狀態"""
    print("\n🔗 測試AI連接狀態")
    print("="*40)
    
    ai_responder = AIResponder(language='chinese')
    
    try:
        import requests
        
        # 測試Ollama連接
        response = requests.get(f"{ai_responder.ai_base_url}/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama連接正常，可用模型: {len(models)} 個")
            
            # 檢查目標模型
            model_names = [model['name'] for model in models]
            if ai_responder.ai_model in model_names:
                print(f"✅ 目標模型 {ai_responder.ai_model} 可用")
            else:
                print(f"⚠️ 目標模型 {ai_responder.ai_model} 不可用")
                print(f"可用模型: {model_names}")
        else:
            print(f"❌ Ollama連接失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ AI連接測試失敗: {e}")
        print("💡 請確認:")
        print("  1. Ollama是否運行: ollama serve")
        print("  2. 模型是否安裝: ollama pull llama3.1")
        print("  3. 端口是否正確: http://localhost:11434")

def main():
    """主函數"""
    print("🎯 AI驅動的智能分析系統測試")
    print("="*70)
    
    # 測試AI連接
    test_ai_connectivity()
    
    # 測試AI問題分析
    test_ai_question_analysis()
    
    # 測試AI vs 規則比較
    test_ai_vs_rule_based()
    
    # 測試完整工作流程
    test_full_ai_workflow()
    
    print("\n🎉 所有測試完成！")
    print("\n💡 AI驅動系統優勢:")
    print("1. 🧠 智能問題分析 - AI自動識別問題類型和複雜度")
    print("2. 🎯 動態概念提取 - AI識別關鍵概念和類別")
    print("3. 📚 個性化建議 - AI生成針對性學習建議")
    print("4. 🔄 自適應教學 - 根據AI分析選擇最佳教學方式")

if __name__ == "__main__":
    main()
