[{"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "1", "question_text": "請詳細說明 QUIC (Quick UDP Internet Connection)協定的發展背景與特色。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "2", "question_text": "在今日網路環境,無論對個人或企業,VPN(Virtual Private Network)都是重要的網路安全工具。請先說明VPN的一般功能,然後比較 IPSec VPN, SSL VPN以及MPLS VPN三者之間的區別。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "3-甲", "question_text": "此系統的TLB觸及範圍(TLB Reach)為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "3-乙", "question_text": "此系統的實體位址空間大小為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "3-丙", "question_text": "若分頁表採取了三階層式(three-level)的實作方式,其記憶體有效存取時間(effective access time)為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "4-甲", "question_text": "請問會產生多少個獨立的行程(process)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "4-乙", "question_text": "請問會產生多少個獨立的執行緒(thread)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "5", "question_text": "資料庫正規化中的1NF、2NF與3NF是以哪一重要觀念發展出來的?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "企業管理學系碩士班己組", "year": "113", "question_number": "6", "question_text": "已知存放a變數的記憶體位置為0X0012FF74;b變數的記憶體位置為0X0012FF78。請問,執行完程式後,a和b的值各為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "7-甲", "question_text": "(5%) Which normal form(s) does Table X satisfy (1st NF, 2nd NF, 3rd NF, or others)? Why?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "7-乙", "question_text": "(5%) Does further normalization needed for Table X to avoid anomaly? How and what normal form can we get?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "7-丙", "question_text": "(5%) If there are 10000 rows in Table X, how much storage space can your answer in (b) save in maximum in the extreme case? Why? Please illustrate your answer for the extreme case.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "8-甲", "question_text": "(5%) What are the difference when applying either shared lock or exclusive lock to a data item?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "8-乙", "question_text": "(5%) The size of the locked data item can be small, such as a tuple or a record in a table; or big, such as a table or a database. Please provide the pros and cons for applying locking to small and/or large sized data item.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "9", "question_text": "(5%) In Java programing language, write a program to swap two numbers without using a third variable in your program.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "10", "question_text": "(10%) Write a Java program to check if a given number is a prime number. Don't use Java's library to directly check for prime number, design your own solution. Your program needs to take an input number, and print out your answer with the input number. Explain why your program is the most efficient one as efficiency and correctness are both graded for the question.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "1", "question_text": "請詳細說明 QUIC (Quick UDP Internet Connection)協定的發展背景與特色。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "2", "question_text": "在今日網路環境,無論對個人或企業,VPN(Virtual Private Network)都是重要的網路安全工具。請先說明VPN的一般功能,然後比較 IPSec VPN, SSL VPN以及MPLS VPN三者之間的區別。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "3-甲", "question_text": "此系統的TLB觸及範圍(TLB Reach)為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "3-乙", "question_text": "此系統的實體位址空間大小為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "3-丙", "question_text": "若分頁表採取了三階層式(three-level)的實作方式,其記憶體有效存取時間(effective access time)為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "4-甲", "question_text": "請問會產生多少個獨立的行程(process)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "4-乙", "question_text": "請問會產生多少個獨立的執行緒(thread)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "5", "question_text": "資料庫正規化中的1NF、2NF與3NF是以哪一重要觀念發展出來的?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "企業管理學系碩士班戊組", "year": "113", "question_number": "6", "question_text": "已知存放a變數的記憶體位置為0X0012FF74;b變數的記憶體位置為0X0012FF78。請問,執行完程式後,a和b的值各為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "7-甲", "question_text": "(5%) Which normal form(s) does Table X satisfy (1st NF, 2nd NF, 3rd NF, or others)? Why?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "7-乙", "question_text": "(5%) Does further normalization needed for Table X to avoid anomaly? How and what normal form can we get?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "7-丙", "question_text": "(5%) If there are 10000 rows in Table X, how much storage space can your answer in (b) save in maximum in the extreme case? Why? Please illustrate your answer for the extreme case.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "8-甲", "question_text": "(5%) What are the difference when applying either shared lock or exclusive lock to a data item?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "8-乙", "question_text": "(5%) The size of the locked data item can be small, such as a tuple or a record in a table; or big, such as a table or a database. Please provide the pros and cons for applying locking to small and/or large sized data item.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "9", "question_text": "(5%) In Java programing language, write a program to swap two numbers without using a third variable in your program.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理暨大數據分析類", "year": "113", "question_number": "10", "question_text": "(10%) Write a Java program to check if a given number is a prime number. Don't use Java's library to directly check for prime number, design your own solution. Your program needs to take an input number, and print out your answer with the input number. Explain why your program is the most efficient one as efficiency and correctness are both graded for the question.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "1", "question_text": "請詳細說明 QUIC (Quick UDP Internet Connection)協定的發展背景與特色。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "2", "question_text": "在今日網路環境,無論對個人或企業,VPN(Virtual Private Network)都是重要的網路安全工具。請先說明VPN的一般功能,然後比較 IPSec VPN, SSL VPN以及MPLS VPN三者之間的區別。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "3-甲", "question_text": "此系統的TLB觸及範圍(TLB Reach)為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "3-乙", "question_text": "此系統的實體位址空間大小為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "3-丙", "question_text": "若分頁表採取了三階層式(three-level)的實作方式,其記憶體有效存取時間(effective access time)為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "4-甲", "question_text": "請問會產生多少個獨立的行程(process)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "4-乙", "question_text": "請問會產生多少個獨立的執行緒(thread)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "5", "question_text": "資料庫正規化中的1NF、2NF與3NF是以哪一重要觀念發展出來的?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "6", "question_text": "已知存放a變數的記憶體位置為0X0012FF74;b變數的記憶體位置為0X0012FF78。請問,執行完程式後,a和b的值各為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "7-甲", "question_text": "(5%) Which normal form(s) does Table X satisfy (1<sup>st</sup> NF, 2<sup>nd</sup> NF, 3<sup>rd</sup> NF, or others)? Why?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "7-乙", "question_text": "(5%) Does further normalization needed for Table X to avoid anomaly? How and what normal form can we get?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "7-丙", "question_text": "(5%) If there are 10000 rows in Table X, how much storage space can your answer in (b) save in maximum in the extreme case? Why? Please illustrate your answer for the extreme case.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "8-甲", "question_text": "(5%) What are the difference when applying either shared lock or exclusive lock to a data item?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "8-乙", "question_text": "(5%) The size of the locked data item can be small, such as a tuple or a record in a table; or big, such as a table or a database. Please provide the pros and cons for applying locking to small and/or large sized data item.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "9", "question_text": "(5%) In Java programing language, write a program to swap two numbers without using a third variable in your program.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "工業管理研究所碩士班大數據組", "year": "113", "question_number": "10", "question_text": "(10%) Write a Java program to check if a given number is a prime number. Don't use Java's library to directly check for prime number, design your own solution. Your program needs to take an input number, and print out your answer with the input number. Explain why your program is the most efficient one as efficiency and correctness are both graded for the question.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "1", "question_text": "TCP/IP 協議套件的開發,最初是為了支援什麼樣的網路?", "options": ["a. ARPANET", "b. Internet", "c. <PERSON><PERSON><PERSON>", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "10", "question_text": "一個八進位數字的每一位最多可以表示多少個不同的值?", "options": ["a. 2", "b. 4", "c. 8", "d. 16"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "12", "question_text": "問題:十進位數字55可以用二進位表示為多少?", "options": ["a. 1001001", "b. 101011", "c. 110110", "d. 110111"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "15", "question_text": "在二進位制中,什麼是位元為算Bitwise \"AND\"的運算?", "options": ["a. 兩者都為1時結果為1,否則為0", "b. 兩者都為0時結果為1,否則為0", "c. 兩者只要有一個為1就結果為1,否則為0", "d. 兩者只要有一個為0就結果為1,否則為0"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "16", "question_text": "下列哪種不是作業系統的種類？", "options": ["a. <PERSON>", "b. Microsoft Office", "c. Linux", "d. mac<PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "17", "question_text": "作業系統的一個主要功能是什麼？", "options": ["a. 程式開發", "b. 資源管理", "c. 防火牆保護", "d. 印表機控制"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "18", "question_text": "作業系統中的死結(deadlock)是指什麼情況？", "options": ["a. 硬體故障", "b. 軟體錯誤", "c. 兩個以上的運算單元相互等待導致無法繼續執行", "d. 網路連接中斷"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "19", "question_text": "以下哪個不是作業系統的排程策略？", "options": ["a. 先到先做(First Come First Serve)", "b. 最短作業優先(Shortest Job First)", "c. 隨機選擇排序(Random Priority Scheduling)", "d. 優先權排程(Priority Scheduling)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "20", "question_text": "作業系統的開機過程中，哪個階段主要負責初始化硬體？", "options": ["<PERSON><PERSON>", "b<PERSON>", "c. Shell", "d. BIOS/UEFI"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "21", "question_text": "什麼是堆疊(Stack)？", "options": ["a. 資料結構，遵循先進後出(FILO)原則", "b. 排序演算法", "c. 程式的執行結果", "d. 無序的資料集合"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "22", "question_text": "什麼是雜湊表(Hash Table)？", "options": ["a. 排序演算法", "b. 一種資料結構，使用雙向連結串列", "c. 一種資料結構，使用雜湊函數進行索引", "d. 一種遞迴方法"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "23", "question_text": "什麼是物件導向程式設計(Object-Oriented Programming, OOP)？", "options": ["a. 一種排序演算法", "b. 一種程式語言", "c. 一種程式設計風格，以物件為基礎", "d. 一種編譯器"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "24", "question_text": "下列何者是對於封裝(Encapsulation)的正確敘述?", "options": ["a. 繼承而產生的不同的類別,其物件對同一訊息會做出不同的回應", "b. 是一種簡化複雜的現實問題的做法", "c. 子類別被修改,父類別依賴著父類別", "d. 是一種防止外界去存取物件內部實作細節"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "25", "question_text": "問題:程式碼版本控制(Version Control)的主要目的是什麼?", "options": ["a. 加密程式碼", "b. 防止程式碼被竄改", "c. 管理和追蹤程式碼的變更", "d. 壓縮程式碼"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "27", "question_text": "在計算機概論應用在教育科技中,「Adaptive Learning」的主要特點是什麼?", "options": ["a. 適應不同學科", "b. 適應不同年齡層次", "c. 適應不同地區文化", "d. 根據學習者的進度和需求調整教學內容"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "29", "question_text": "在教育領域,何種應用最有可能使用區塊鏈(Blockchain)技術?", "options": ["a. 在線測試", "b. 虛擬班級", "c. 學歷認證", "d. 視訊會議"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "30", "question_text": "DNS的作用是什麼?", "options": ["a. 控制網路流量", "b. 保護網路安全", "c. 轉換域名為IP位址", "d. 加密網路通訊"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "31", "question_text": "什麼是LAN的全名?", "options": ["a. Local Access Network", "b. Long Area Network", "c. Large Array Network", "d. Local Area Network"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "32", "question_text": "什麼是VPN的全名?", "options": ["a. Virtual Personal Network", "b. Visual Private Network", "c. Verified Public Network", "d. Virtual Private Number"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "34", "question_text": "問題:什麼是SMTP協定?", "options": ["a. Simple Mail Transfer Protocol", "b. Secure Mail Transmission Protocol", "c. System Message Transfer Protocol", "d. Simple Messaging and Texting Protocol"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "35", "question_text": "「資訊自主權」(Information Autonomy)的概念是指什麼?", "options": ["a. 個體擁有自己的資訊系統", "b. 個體擁有控制自己資訊的權利", "c. 資訊系統的獨立性", "d. 自由使用所有的資訊"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "36", "question_text": "「演算法偏差」(Algorithmic Bias)的問題主要來自於什麼?", "options": ["a. 演算法的複雜性", "b. 演算法的不穩定性", "c. 數據訓練集的偏差", "d. 演算法的加密性"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "37", "question_text": "非監督式學習(Unsupervised Learning)的目標是什麼?", "options": ["a. 預測輸出結果", "b. 分析數據結構和模式", "c. 降低模型複雜度", "d. 最小化學習錯誤"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "38", "question_text": "「過擬合」(Overfitting)是機器學習中的什麼現象?", "options": ["a. 模型無法擬合訓練數據", "b. 模型過度擬合訓練數據,失去泛化能力", "c. 模型未充分擬合訓練數據", "d. 模型對訓練數據具有適中擬合能力"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "39", "question_text": "在類神經網絡中,「權重」(Weight)是指什麼?", "options": ["a. 輸入特徵的值", "b. 模型的預測結果", "c. 激活函數的輸出", "d. 學習算法的參數"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "40", "question_text": "「梯度消失」(Gradient Vanishing)是類神經網絡中的什麼問題?", "options": ["a. 梯度更新速度過快", "b. 梯度下降算法失效", "c. 梯度在反向傳播過程中趨近於零", "d. 梯度方向不一致"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "41", "question_text": "在強化學習中,「探索與利用」(Explore and Exploit)的平衡是指什麼?", "options": ["a. 在訓練過程中保持探索和利用的比例", "b. 僅進行探索而忽略利用", "c. 僅進行利用而忽略探索", "d. 在不同階段動態調整探索和利用的比例"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "42", "question_text": "「生成對抗網絡」(Generative Adversarial Network,GAN)的結構包含哪兩個主要部分?", "options": ["a. 監督和評估", "b. 生成和判別", "c. 推斷和生成", "d. 壓縮和解壓縮"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "43", "question_text": "在程式設計中,變數的主要作用是什麼?", "options": ["a. 儲存資料", "b. 控制流程", "c. 定義函式", "d. 執行迴圈"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "45", "question_text": "函式的主要目的是什麼?", "options": ["a. 儲存資料", "b. 控制流程", "c. 定義變數", "d. 封裝可重複使用的程式碼片段"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "46", "question_text": "在關聯式資料庫中,什麼是主鍵(Primary Key)的作用?", "options": ["a. 唯一標識資料表的紀律", "b. 儲存表格的索引", "c. 定義外部關聯", "d. 控制資料的存取權限"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "47", "question_text": "SQL中,用於篩選條件的關鍵字是?", "options": ["a. WH<PERSON>", "b. SELECT", "c. FROM", "d. ORDER BY"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "48", "question_text": "資料庫正规化的主要目的是?", "options": ["a. 儲存更多的資料", "b. 減少資料庫中的冗餘資料", "c. 提高查詢速度", "d. 增加資料庫的大小"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "49", "question_text": "在SQL中,用於從資料表中刪除記錄的關鍵字是?", "options": ["a. REMOVE", "b. <PERSON>", "c. <PERSON>", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "網路學習科技研究所", "year": "113", "question_number": "50", "question_text": "資料庫中的「索引」(Index)有什麼作用?", "options": ["a. 儲存資料", "b. 定義資料型別", "c. 提高搜索效率", "d. 限制訪問權限"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "1", "question_text": "請詳細說明 QUIC (Quick UDP Internet Connection)協定的發展背景與特色。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "2", "question_text": "在今日網路環境,無論對個人或企業,VPN(Virtual Private Network)都是重要的網路安全工具。請先說明VPN的一般功能,然後比較 IPSec VPN, SSL VPN以及MPLS VPN三者之間的區別。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "3-甲", "question_text": "此系統的TLB觸及範圍(TLB Reach)為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "3-乙", "question_text": "此系統的實體位址空間大小為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "3-丙", "question_text": "若分頁表採取了三階層式(three-level)的實作方式,其記憶體有效存取時間(effective access time)為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "4-甲", "question_text": "請問會產生多少個獨立的行程(process)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "4-乙", "question_text": "請問會產生多少個獨立的執行緒(thread)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "5", "question_text": "資料庫正規化中的1NF、2NF與3NF是以哪一重要觀念發展出來的?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "6", "question_text": "已知存放a變數的記憶體位置為0X0012FF74;b變數的記憶體位置為0X0012FF78。請問,執行完程式後,a和b的值各為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "7-甲", "question_text": "(5%) Which normal form(s) does Table X satisfy (1<sup>st</sup> NF, 2<sup>nd</sup> NF, 3<sup>rd</sup> NF, or others)? Why?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "7-乙", "question_text": "(5%) Does further normalization needed for Table X to avoid anomaly? How and what normal form can we get?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "7-丙", "question_text": "(5%) If there are 10000 rows in Table X, how much storage space can your answer in (b) save in maximum in the extreme case? Why? Please illustrate your answer for the extreme case.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "8-甲", "question_text": "(5%) What are the difference when applying either shared lock or exclusive lock to a data item?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "8-乙", "question_text": "(5%) The size of the locked data item can be small, such as a tuple or a record in a table; or big, such as a table or a database. Please provide the pros and cons for applying locking to small and/or large sized data item.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "9", "question_text": "(5%) In Java programing language, write a program to swap two numbers without using a third variable in your program.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "10", "question_text": "(10%) Write a Java program to check if a given number is a prime number. Don't use Java's library to directly check for prime number, design your own solution. Your program needs to take an input number, and print out your answer with the input number. Explain why your program is the most efficient one as efficiency and correctness are both graded for the question.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "1", "question_text": "請詳細說明 QUIC (Quick UDP Internet Connection)協定的發展背景與特色。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "2", "question_text": "在今日網路環境,無論對個人或企業,VPN(Virtual Private Network)都是重要的網路安全工具。請先說明VPN的一般功能,然後比較 IPSec VPN, SSL VPN以及MPLS VPN三者之間的區別。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "3-甲", "question_text": "此系統的TLB觸及範圍(TLB Reach)為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "3-乙", "question_text": "此系統的實體位址空間大小為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "3-丙", "question_text": "若分頁表採取了三階層式(three-level)的實作方式,其記憶體有效存取時間(effective access time)為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "4-甲", "question_text": "請問會產生多少個獨立的行程(process)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "4-乙", "question_text": "請問會產生多少個獨立的執行緒(thread)?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "5", "question_text": "資料庫正規化中的1NF、2NF與3NF是以哪一重要觀念發展出來的?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "6", "question_text": "已知存放a變數的記憶體位置為0X0012FF74;b變數的記憶體位置為0X0012FF78。請問,執行完程式後,a和b的值各為多少?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "7-甲", "question_text": "(5%) Which normal form(s) does Table X satisfy (1st NF, 2nd NF, 3rd NF, or others)? Why?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "7-乙", "question_text": "(5%) Does further normalization needed for Table X to avoid anomaly? How and what normal form can we get?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "7-丙", "question_text": "(5%) If there are 10000 rows in Table X, how much storage space can your answer in (b) save in maximum in the extreme case? Why? Please illustrate your answer for the extreme case.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "8-甲", "question_text": "(5%) What are the difference when applying either shared lock or exclusive lock to a data item?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "8-乙", "question_text": "(5%) The size of the locked data item can be small, such as a tuple or a record in a table; or big, such as a table or a database. Please provide the pros and cons for applying locking to small and/or large sized data item.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "9", "question_text": "(5%) In Java programing language, write a program to swap two numbers without using a third variable in your program.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中央大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "10", "question_text": "(10%) Write a Java program to check if a given number is a prime number. Don't use Java's library to directly check for prime number, design your own solution. Your program needs to take an input number, and print out your answer with the input number. Explain why your program is the most efficient one as efficiency and correctness are both graded for the question.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "1", "question_text": "以下哪種網路類型通常用於連接一個辦公室或家庭中的設備？", "options": ["a. 廣域網路(WAN)", "b. 都會網路(MAN)", "c. 區域網路(LAN)", "d. 無線區域網路(WLAN)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "2", "question_text": "高級持久性滲透攻擊(APT)的三個主要要素分別是什麼？", "options": ["a. 高級、長期、威脅", "b. 快速、單次、隨機", "c. 網絡、區域、全球", "d. 物理、虛擬、混合"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "3", "question_text": "網路型防火牆通常設置在哪裡？", "options": ["a. 單一伺服器", "b. 網路節點", "c. 網路邊界", "d. 應用程式"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "4", "question_text": "CVSS(通用漏洞評分系統)通常使用哪個範圍來表示漏洞的危險程度？", "options": ["a. 0到100", "b. 0到10", "c. 1到5", "d. 1到10"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "5", "question_text": "何謂TCP的三向交握？", "options": ["a. 客戶端向服務器端發送SYN封包，服務器端回應ACK封包，然後客戶端再次回應ACK封包。", "b. 客戶端向服務器端發送SYN封包，服務器端回應SYN封包，然後客戶端回應ACK封包。", "c. 客戶端向服務器端發送ACK封包，服務器端回應SYN封包，然後客戶端再次回應ACK封包。", "d. 客戶端向服務器端發送SYN封包，服務器端不回應，然後客戶端再次發送SYN封包。"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "6", "question_text": "OSI 模型是一種用於理解和描述計算機網絡中不同協議的模型。請問OSI模型共有幾層？", "options": ["a. 3個層次", "b. 5個層次", "c. 7個層次", "d. 9個層次"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "7", "question_text": "在計算機網絡中,有多個不同層次的協議共同工作。請問以下哪個協議位於網絡層,用於在不同網絡之間路由數據包？", "options": ["a. H<PERSON>", "b. <PERSON>", "c. IP", "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> INTERFACES"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "8", "question_text": "當使用網路卡的混亂模式(Promiscuous mode)時,會發生什麼情況？", "options": ["a. 網絡速度增加", "b. 封包數量減少", "c. 所有可接收的網絡封包都會被處理", "d. 主機位置會被隱藏"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "9", "question_text": "下列哪種攻擊是一種針對錯誤配置的網路服務和協議的攻擊，攻擊者嘗試偽裝成合法的網路設備以欺騙其他設備？", "options": ["a. ARP SPOOF", "b. IP SPOOF", "c. DNS SPOOF", "d. PH<PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "12", "question_text": "下列哪一個不是軟體發展在實務上的三大考量依據?", "options": ["a. 範圍(SCOPE)", "b. 時程(SCHEDULE)", "c. 經費預算(BUDGET)", "d. 使用者體驗 (USER EXPERIENCE)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "13", "question_text": "在程式安全分類中,哪一個類別主要關注輸入驗證及表示(Input Validation and Representation)?", "options": ["a. API 誤用(ABUSE)", "b. 安全特性(SECURITY FEATURES)", "c. 輸入驗證及表示(INPUT VALIDATION AND REPRESENTATION)", "d. 時間與狀態(TIME AND STATE)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "14", "question_text": "在軟體開發生命週期(SDLC)的每個環節中,哪個核心概念關注確保只有授權的用戶能夠訪問資源和功能?", "options": ["a. 保密(CONFIDENTIALITY)", "b. 完整性(INTEGRITY)", "c. 可用性(AVAILABILITY)", "d. 授權(AUTHORIZATION)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "15", "question_text": "ISO 27001 資訊安全管理系統(ISMS)的主要目標是什麼?", "options": ["a. 保護組織的資訊財產", "b. 提高網絡速度", "c. 提升員工技能", "d. 增加公司收入"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "16", "question_text": "下列何者不屬於資訊安全威脅的來源分類?", "options": ["a. 環境因素威脅", "b. 外部人員威脅", "c. 內部授權人員威脅", "d. 系統弱點"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "17", "question_text": "下列何種加密方式為明文中的每個字母對應到一群數字,為此增加破解的困難度?", "options": ["a. 簡單替代法(Simple Substitution)", "b. 同音異字替代法(Homophonic Substitution)", "c. 多字母替代法(Polyalphabetic Substitution)", "d. 多圖替代法(Polygram Substitution)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "18", "question_text": "鐵軌法屬於哪種加密方法？", "options": ["a. 換位加密方法", "b. 置換加密方法", "c. 替換加密方法", "d. 複雜替換加密方法"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "19", "question_text": "加密處理PKI的主要目的是什麼？", "options": ["a. 認證使用者的身份", "b. 建立私鑰基礎建設", "c. 保護敏感數據的隱私", "d. 創建網路防火牆"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "20", "question_text": "加密處理DES的總位元長度是多少？", "options": ["a. 32位元", "b. 48位元", "c. 56位元", "d. 64位元"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "21", "question_text": "在區塊鏈的以太坊中，什麼是Gas？", "options": ["a. 以太幣的初始價格", "b. 僅限礦工使用的加密貨幣", "c. 用於支付交易手續費的單位", "d. 以太坊的代碼執行過程"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "22", "question_text": "數位簽章主要用於驗證訊息的哪一個重要屬性？", "options": ["a. 機密性", "b. 可用性", "c. 完整性", "d. 可追溯性"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "23", "question_text": "哪一種浮水印技術僅能藏入一簡短的序號，不能藏入影像？", "options": ["<PERSON><PERSON>", "b. <PERSON>", "c. 數位簽章", "d. 電子簽章"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "24", "question_text": "下列何種雲端服務適用於與行動裝置連動的應用,與進行後台訊息的推送。", "options": ["a. IAAS", "b. PAAS", "c. <PERSON>", "d. SAAS"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "25", "question_text": "下列哪項不是雲端運算的優點？", "options": ["a. 成本低", "b. 節能環保", "c. 備份容易", "d. 資料安全性"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "26", "question_text": "下列何者是NoSQL 資料庫的特點之一？", "options": ["a. 高效能與開發容易性", "b. 嚴格的資料一致性模型", "c. 僅適用於小型資料集", "d. 僅支援關聯式資料"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "28", "question_text": "下列哪個不是Linux的特色？", "options": ["a. 自由開放", "b. 擁有龐大的GNU軟體", "c. 易於安裝", "d. 依賴於專有軟體"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "29", "question_text": "下列哪一個不是MariaDB的優點？", "options": ["a. 可擴展性高", "b. 能即時訪問", "c. 具備Mysql 核心功能", "d. 速度比Mysql 慢"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "30", "question_text": "下列哪個不是造成密碼弱點的人性/習慣之一?", "options": ["a. 常見/慣用密碼", "b. 使用複雜的密碼", "c. 與使用者名稱相同", "d. 過於簡單"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "31", "question_text": "哪種社交工程攻擊利用攻擊者偽裝成公司內部技術人員或問卷調查人員,要求受害者提供密碼等關鍵資訊?", "options": ["a. 假託(pretexting)", "b. 調虎離山(diversion theft)", "c. 線上聊天/電話釣魚(ivr/phone phishing)", "d. 同情心"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "32", "question_text": "根據資訊系統弱點管理流程,以下哪個是正確的步驟順序?", "options": ["a. 掃瞄準備→弱點掃瞄→修補→復測→預防", "b. 弱點掃瞄→修補→復測→掃瞄準備→預防", "c. 復測→掃瞄準備→弱點掃瞄→修補→預防", "d. 預防→掃瞄準備→弱點掃瞄→修補→復測"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "33", "question_text": "非監督式學習的主要特點之一是什麼?", "options": ["a. 僅使用已知的輸入和輸出對來訓練模型", "b. 需要高度專業的測試人員執行", "c. 僅有輸入資料而沒有標註標記", "d. 通常具有比監督式學習更高的準確率"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "36", "question_text": "增強式學習的主要組成部分包括什麼？", "options": ["a. 算法、數據集、測試", "b. 環境、代理機器人、獎勵", "c. 標記、特徵、損失函數", "d. 監督者、評價標準、迭代"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "37", "question_text": "請問關於哪種AI風險涵蓋範圍最廣，包括自然語言處理、機器學習、電腦視覺等技術？", "options": ["a. 集中委外風險", "b. 隱私風險", "c. 市場價格操縱問題", "d. 決策黑箱風險"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "38", "question_text": "暗網是資安情資的一個重要來源，它通常使用什麼特定方式來進行連線和溝通？", "options": ["a. 開放網路", "b. 特殊門戶網站", "c. 特定加密方式", "d. <PERSON>交媒體平台"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "39", "question_text": "機器學習過程中，如果你的資料數值服從均勻分佈，通常適合使用哪種方式來平衡特徵？", "options": ["a. 標準化 (Standard Scaler)", "b. 最小最大化 (minmax Scaler)", "c. 歸一化 (Normalization)", "d. 正規化 (Regularization)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "40", "question_text": "AI中GAN和CNN分別是什麼？", "options": ["a. GAN是一種監督式學習模型，CNN是一種生成模型。", "b. GAN是一種生成模型，CNN是一種監督式學習模型。", "c. <PERSON><PERSON>和CNN都是生成模型。", "d. <PERSON>和CNN都是監督式學習模型。"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "1", "question_text": "請列出SQL語法可以查詢資料表Section 內 Course_id為'CS-102'的所有欄位資料?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "2", "question_text": "請列出 SQL語法可以查詢資料表 Teach內在2009年Fall 有開課的資料?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "3", "question_text": "請列出 SQL語法可以查詢資料表 Teach和Session內,以Course_id為Key列出id, course_id, Building 三個欄位?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "會計與資訊科技學系碩士班資訊組", "year": "113", "question_number": "4", "question_text": "請列出 SQL語法可以修改資料表 Teach 內的id為1004資料,將 Semester 欄位改為Spring且Year欄位改為2010?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "1", "question_text": "The binary representation of 28-10 is:", "options": ["a. 10000", "b. 10001", "c. 10010", "d. 10011"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "2", "question_text": "The binary notation of 66.375 is:", "options": ["a. 1000100.111", "b. 1000100.101", "c. 1000010.101", "d. 1000010.011"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "4", "question_text": "The technique to consolidate multiple signals into a single composite signal is called:", "options": ["a. multiplexing", "b. pipelining", "c. multiprocessing", "d. multitasking"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "5", "question_text": "The following processes arrive for execution at the times indicated. Each process will run the listed amount of time. Suppose the non-preemptive shortest job first (SJF) scheduling algorithm is used, what is the average waiting time for these processes?", "options": ["a. 4.67", "b. 5.00", "c. 5.33", "d. 5.67"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "6", "question_text": "Following the question above. What is the average turnaround time for these processes?", "options": ["a. 9.00", "b. 9.33", "c. 9.67", "d. 10.00"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "8", "question_text": "Each time the dispatcher awards a time slice to a process, it initiates a timer circuit that will indicate the end of the slice by generating a signal called:", "options": ["a. interrupt", "b. signaling I/O", "c. deadlock", "d. spinlock"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "9", "question_text": "How many IP addresses are in a class B network?", "options": ["a. 254", "b. 256", "c. 65534", "d. 65536"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "10", "question_text": "Which of the following connects two bus networks?", "options": ["a. switch", "b. access point", "c. repeater", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "11", "question_text": "Which of the following refers to a small piece of data sent from a website and stored on the user's computer by the user's web browser while the user is browsing so as to remember stateful information?", "options": ["a. etag", "b. proxy", "c. cookie", "d. register"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "12", "question_text": "The application layer protocol of LINE is:", "options": ["a. H<PERSON>", "b. SM<PERSON>", "c. <PERSON>", "d. V<PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "13", "question_text": "Which of the following is polynomially bounded?", "options": ["a. $100^{\\log n}$", "b. $2^n$", "c. $n^n$", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "14", "question_text": "The worst time complexity of quick sort is:", "options": ["a. O(n)", "b. <PERSON>(nloglogn)", "c. <PERSON>(nlogn)", "d. O(n$^2$)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "15", "question_text": "The average time complexity of insertion sort is:", "options": ["a. <PERSON>(nloglogn)", "b. <PERSON>(nlogn)", "c. O(n$^2$)", "d. O(n$^2$logn)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "16", "question_text": "What is the printed value of the following code?\n```c++\nsum = 0;\nfor(int i = 1; i <= 8; ++i)\n    sum += i;\ncout << sum << endl;\n```", "options": ["a. 32", "b. 36", "c. 0", "d. 40320"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "17", "question_text": "What is the value of arr[5] of the following code?\n```c++\nint arr[10];\nfor(int i = 0; i < 10; ++i)\n    arr[i] = 0;\nfor(int i = 0; i < 10; ++i)\n    if (i == 5)\n        continue;\n    arr[i] = i * i;\n```", "options": ["a. 0", "b. 5", "c. 25"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "18", "question_text": "What is the printed value of the following code?\nint arr[10];\nfor(int i=0; i<10; ++i)\narr[i]=i*i;\nif(i%3==1)\ncout << arr[i] << \" \";\nif (i==5)\nbreak;\ncout << endl;", "options": ["a. 1 16", "b. 0 149 16", "c. 1 16 49", "d. 0 149 16 25 36 49 64 81"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "19", "question_text": "______ allows one class to encompass the properties of another.", "options": ["a. encapsulation", "b. polymorphism", "c. inheritance", "d. interface"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "20", "question_text": "The way to give the function direct access to the actual parameters by telling it the addresses of the actual parameters in the calling program unit is called ______.", "options": ["a. call by value", "b. call by reference", "c. fruitful function", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "21", "question_text": "A problem occurred during a program's execution is called ______.", "options": ["a. compile error", "b. syntax error", "c. exception", "d. none of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "22", "question_text": "Given $T(n) = 2T(n/2) + 1$, $T(0) = 0$, $T(1) = 1$, $T(n) = ?$ ", "options": ["a. $\\Theta(\\log n)$", "b. $\\Theta(n)$", "c. $\\Theta(n \\log \\log n)$", "d. $\\Theta(n \\log n)$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "23", "question_text": "Given $T(n) = 2T(n/2) + \\log n$, $T(0) = 0$, $T(1) = 1$, $T(n) = ?$ ", "options": ["a. $\\Theta(\\log^2 n)$", "b. $\\Theta(\\log n \\log \\log n)$", "c. $\\Theta(n)$", "d. $\\Theta(n \\log n)$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "24", "question_text": "Which of the following has the lowest complexity?", "options": ["a. 100!", "b. $2^{\\log n}$", "c. n", "d. $4^{\\log n}$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "25", "question_text": "Given a binary search tree. The inorder and postorder traversal of this tree are 1,3,5,7,9,11,13 and 1,5,3,7,13,11,9, respectively. What is the preorder traversal of this tree?", "options": ["a. 9,7,3,1,5,13,11", "b. 9,7,3,1,5,11,13", "c. 9,7,3,5,1,13,11", "d. 9,7,3,1,5,11,13"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "26", "question_text": "Given a binary tree with 16 nodes, and it has 3 nodes with degree 1. How many leafs in this tree?", "options": ["a. 5", "b. 6", "c. 7", "d. 8"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "27", "question_text": "In the relational database, a column in a relation is called ______.", "options": ["a. dictionary", "b. item", "c. tuple", "d. attribute"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "28", "question_text": "Which of the following extracts columns from a relation?", "options": ["a. SELECT", "b. PROJECT", "c. CHO<PERSON>", "<PERSON><PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "29", "question_text": "Which of the following is not an animation software?", "options": ["a. 3ds Max", "b. <PERSON><PERSON>", "c. Maya", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "30", "question_text": "The ______ model is a RNN model.", "options": ["a. LSTM", "b. VGG16", "c. <PERSON>", "d. actor-critic"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "31", "question_text": "The ______ model is not for reinforcement learning.", "options": ["a. GRU", "b. <PERSON><PERSON><PERSON>", "c. DD<PERSON>", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "32", "question_text": "Which of the following is not a branch of machine learning?", "options": ["a. unsupervised learning", "b. particle swarm optimization", "c. reinforcement learning", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "34", "question_text": "Which of the following is a NP-complete problem?", "options": ["a. depth-first-search problem", "b. spanning tree problem", "c. all-pairs-shortest-path problem", "d. 0/1 knapsack problem"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "35", "question_text": "Which of the following is not a NP-complete problem?", "options": ["a. all-pairs-shortest-path problem", "b. 0/1 knapsack problem", "c. traveling-salesman problem", "d. clique problem"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "1-a", "question_text": "Convert the Hexadecimal number $(6F)_{16}$ into the following carry number: (a) (3 pts) Octal number", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "1-b", "question_text": "Convert the Hexadecimal number $(6F)_{16}$ into the following carry number: (b) (3 pts) Decimal number", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "2", "question_text": "Please use the do-while loop in C/C++ to calculate the product of integers from 1 to 10.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "3", "question_text": "Given 6 data 6,5,2,3,4,1, write the result of each iteration in insertion sort (in ascending order).", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "4", "question_text": "Given a binary tree with 32 nodes, write its maximal and minimal height.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班乙組", "year": "113", "question_number": "5", "question_text": "What is the relationship between artificial intelligence, machine learning, and deep learning?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "1", "question_text": "The binary representation of 28-10 is:", "options": ["a. 10000", "b. 10001", "c. 10010", "d. 10011"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "2", "question_text": "The binary notation of 66.375 is:", "options": ["a. 1000100.111", "b. 1000100.101", "c. 1000010.101", "d. 1000010.011"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "4", "question_text": "The technique to consolidate multiple signals into a single composite signal is called:", "options": ["a. multiplexing", "b. pipelining", "c. multiprocessing", "d. multitasking"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "5", "question_text": "The following processes arrive for execution at the times indicated. Each process will run the listed amount of time. Suppose the non-preemptive shortest job first (SJF) scheduling algorithm is used, what is the average waiting time for these processes?", "options": ["a. 4.67", "b. 5.00", "c. 5.33", "d. 5.67"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "6", "question_text": "Following the question above. What is the average turnaround time for these processes?", "options": ["a. 9.00", "b. 9.33", "c. 9.67", "d. 10.00"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "7", "question_text": "Which of the following has the worst space efficiency?", "options": ["a. RAID 0", "b. RAID 1", "c. RAID 3", "d. <PERSON> 5"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "8", "question_text": "Each time the dispatcher awards a time slice to a process, it initiates a timer circuit that will indicate the end of the slice by generating a signal called:", "options": ["a. interrupt", "b. signaling I/O", "c. deadlock", "d. spinlock"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "9", "question_text": "How many IP addresses are in a class B network?", "options": ["a. 254", "b. 256", "c. 65534", "d. 65536"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "10", "question_text": "Which of the following connects two bus networks?", "options": ["a. switch", "b. access point", "c. repeater", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "11", "question_text": "Which of the following refers to a small piece of data sent from a website and stored on the user's computer by the user's web browser while the user is browsing so as to remember stateful information?", "options": ["a. etag", "b. proxy", "c. cookie", "d. register"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "12", "question_text": "The application layer protocol of LINE is:", "options": ["a. H<PERSON>", "b. SM<PERSON>", "c. <PERSON>", "d. V<PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "13", "question_text": "Which of the following is polynomially bounded?", "options": ["a. $100^{\\log n}$", "b. $2^n$", "c. $n^n$", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "15", "question_text": "The average time complexity of insertion sort is:", "options": ["a. <PERSON>(nloglogn)", "b. <PERSON>(nlogn)", "c. O(n²)", "d. <PERSON>(n²logn)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "16", "question_text": "What is the printed value of the following code?\n`sum=0;\nfor(int i=1; i<=8; ++i)\n    sum+=i;\ncout << sum << endl;`", "options": ["a. 32", "b. 36", "c. 0", "d. 40320"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "17", "question_text": "What is the value of arr[5] of the following code?\n`int arr[10];\nfor(int i=0; i<10; ++i)\n    arr[i]=0;\nfor(int i=0; i<10; ++i)\n    if (i==5)\n        continue;\n    arr[i]=i*i;`", "options": ["a. 0", "b. 5", "c. 25"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "18", "question_text": "What is the printed value of the following code?\nint arr[10];\nfor(int i=0; i<10; ++i)\narr[i]=i*i;\nif(i%3==1)\ncout << arr[i] << \" \";\nif (i--5)\nbreak;\ncout << endl;", "options": ["a. 1 16", "b. 0 149 16", "c. 1 16 49", "d. 0 149 16 25 36 49 64 81"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "19", "question_text": "______ allows one class to encompass the properties of another.", "options": ["a. encapsulation", "b. polymorphism", "c. inheritance", "d. interface"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "20", "question_text": "The way to give the function direct access to the actual parameters by telling it the addresses of the actual parameters in the calling program unit is called ______.", "options": ["a. call by value", "b. call by reference", "c. fruitful function", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "21", "question_text": "A problem occurred during a program's execution is called ______.", "options": ["a. compile error", "b. syntax error", "c. exception", "d. none of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "22", "question_text": "Given $T(n) = 2T(n/2) + 1$, $T(0) = 0$, $T(1) = 1$, $T(n) = ?$ ", "options": ["a. $\\Theta(\\log n)$", "b. $\\Theta(n)$", "c. $\\Theta(n \\log \\log n)$", "d. $\\Theta(n \\log n)$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "23", "question_text": "Given $T(n) = 2T(n/2) + \\log n$, $T(0) = 0$, $T(1) = 1$, $T(n) = ?$ ", "options": ["a. $\\Theta(\\log n)$", "b. $\\Theta(\\log n \\log \\log n)$", "c. $\\Theta(n)$", "d. $\\Theta(n \\log n)$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "24", "question_text": "Which of the following has the lowest complexity?", "options": ["a. 100!", "b. $2^{\\log n}$", "c. n", "d. $4^{\\log n}$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "25", "question_text": "Given a binary search tree. The inorder and postorder traversal of this tree are 1,3,5,7,9,11,13 and 1,5,3,7,13,11,9, respectively. What is the preorder traversal of this tree?", "options": ["a. 9,7,3,1,5,13,11", "b. 9,7,3,1,5,11,13", "c. 9,7,3,5,1,13,11", "d. 9,7,3,1,5,11,13"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "26", "question_text": "Given a binary tree with 16 nodes, and it has 3 nodes with degree 1. How many leafs in this tree?", "options": ["a. 5", "b. 6", "c. 7", "d. 8"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "27", "question_text": "In the relational database, a column in a relation is called ______.", "options": ["a. dictionary", "b. item", "c. tuple", "d. attribute"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "28", "question_text": "Which of the following extracts columns from a relation?", "options": ["a. SELECT", "b. PROJECT", "c. CHO<PERSON>", "<PERSON><PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "29", "question_text": "Which of the following is not an animation software?", "options": ["a. 3ds Max", "b. <PERSON><PERSON>", "c. Maya", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "30", "question_text": "The ______ model is a RNN model.", "options": ["a. LSTM", "b. VGG16", "c. <PERSON>", "d. actor-critic"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "31", "question_text": "The ______ model is not for reinforcement learning.", "options": ["a. GRU", "b. <PERSON><PERSON><PERSON>", "c. DD<PERSON>", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "32", "question_text": "Which of the following is not a branch of machine learning?", "options": ["a. unsupervised learning", "b. particle swarm optimization", "c. reinforcement learning", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "35", "question_text": "Which of the following is not a NP-complete problem?", "options": ["a. all-pairs-shortest-path problem", "b. 0/1 knapsack problem", "c. traveling-salesman problem", "d. clique problem"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "1-a", "question_text": "Convert the Hexadecimal number $(6F)_{16}$ into the following carry number: (3 pts) Octal number", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "2", "question_text": "Please use the do-while loop in C/C++ to calculate the product of integers from 1 to 10.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "3", "question_text": "Given 6 data 6,5,2,3,4,1, write the result of each iteration in insertion sort (in ascending order).", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "4", "question_text": "Given a binary tree with 32 nodes, write its maximal and minimal height.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系碩士班甲組", "year": "113", "question_number": "5", "question_text": "What is the relationship between artificial intelligence, machine learning, and deep learning?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "1", "question_text": "The binary representation of 28-10 is:", "options": ["a. 10000", "b. 10001", "c. 10010", "d. 10011"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "2", "question_text": "The binary notation of 66.375 is:", "options": ["a. 1000100.111", "b. 1000100.101", "c. 1000010.101", "d. 1000010.011"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "3", "question_text": "Which of the following has the lowest access speed?", "options": ["a. main memory", "b. hard disk", "c. cache", "d. register"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "4", "question_text": "The technique to consolidate multiple signals into a single composite signal is called:", "options": ["a. multiplexing", "b. pipelining", "c. multiprocessing", "d. multitasking"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "5", "question_text": "The following processes arrive for execution at the times indicated. Each process will run the listed amount of time. Suppose the non-preemptive shortest job first (SJF) scheduling algorithm is used, what is the average waiting time for these processes?", "options": ["a. 4.67", "b. 5.00", "c. 5.33", "d. 5.67"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "6", "question_text": "Following the question above. What is the average turnaround time for these processes?", "options": ["a. 9.00", "b. 9.33", "c. 9.67", "d. 10.00"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "8", "question_text": "Each time the dispatcher awards a time slice to a process, it initiates a timer circuit that will indicate the end of the slice by generating a signal called:", "options": ["a. interrupt", "b. signaling I/O", "c. deadlock", "d. spinlock"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "9", "question_text": "How many IP addresses are in a class B network?", "options": ["a. 254", "b. 256", "c. 65534", "d. 65536"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "10", "question_text": "Which of the following connects two bus networks?", "options": ["a. switch", "b. access point", "c. repeater", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "11", "question_text": "Which of the following refers to a small piece of data sent from a website and stored on the user's computer by the user's web browser while the user is browsing so as to remember stateful information?", "options": ["a. etag", "b. proxy", "c. cookie", "d. register"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "12", "question_text": "The application layer protocol of LINE is:", "options": ["a. H<PERSON>", "b. SM<PERSON>", "c. <PERSON>", "d. V<PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "13", "question_text": "Which of the following is polynomially bounded?", "options": ["a. $100^{\\log n}$", "b. $2^n$", "c. $n^m$", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "14", "question_text": "The worst time complexity of quick sort is:", "options": ["a. O(n)", "b. <PERSON>(nloglogn)", "c. <PERSON>(nlogn)", "d. O(n$^2$)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "15", "question_text": "The average time complexity of insertion sort is:", "options": ["a. <PERSON>(nloglogn)", "b. <PERSON>(nlogn)", "c. O(n$^2$)", "d. O(n$^2$logn)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "16", "question_text": "What is the printed value of the following code?\n```c++\nsum = 0;\nfor(int i = 1; i <= 8; ++i)\n    sum += i;\ncout << sum << endl;\n```", "options": ["a. 32", "b. 36", "c. 0", "d. 40320"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "17", "question_text": "What is the value of arr[5] of the following code?\n```c++\nint arr[10];\nfor(int i = 0; i < 10; ++i)\n    arr[i] = 0;\nfor(int i = 0; i < 10; ++i)\n    if (i == 5)\n        continue;\n    arr[i] = i * i;\n```", "options": ["a. 0", "b. 5", "c. 25"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "18", "question_text": "What is the printed value of the following code?\nint arr[10];\nfor(int i=0; i<10; ++i)\narr[i]=i*i;\nif(i%3==1)\ncout << arr[i] << \" \";\nif (i==5)\nbreak;\ncout << endl;", "options": ["a. 1 16", "b. 0 149 16", "c. 1 16 49", "d. 0 149 16 25 36 49 64 81"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "19", "question_text": "______ allows one class to encompass the properties of another.", "options": ["a. encapsulation", "b. polymorphism", "c. inheritance", "d. interface"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "20", "question_text": "The way to give the function direct access to the actual parameters by telling it the addresses of the actual parameters in the calling program unit is called ______.", "options": ["a. call by value", "b. call by reference", "c. fruitful function", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "21", "question_text": "A problem occurred during a program's execution is called ______.", "options": ["a. compile error", "b. syntax error", "c. exception", "d. none of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "22", "question_text": "Given $T(n) = 2T(n/2) + 1$, $T(0) = 0$, $T(1) = 1$, $T(n) = ?$ ", "options": ["a. $\\Theta(\\log n)$", "b. $\\Theta(n)$", "c. $\\Theta(n \\log \\log n)$", "d. $\\Theta(n \\log n)$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "23", "question_text": "Given $T(n) = 2T(n/2) + \\log n$, $T(0) = 0$, $T(1) = 1$, $T(n) = ?$ ", "options": ["a. $\\Theta(\\log^2 n)$", "b. $\\Theta(\\log n \\log \\log n)$", "c. $\\Theta(n)$", "d. $\\Theta(n \\log n)$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "24", "question_text": "Which of the following has the lowest complexity?", "options": ["a. 100!", "b. $2^{\\log n}$", "c. n", "d. $4^{\\log n}$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "25", "question_text": "Given a binary search tree. The inorder and postorder traversal of this tree are 1,3,5,7,9,11,13 and 1,5,3,7,13,11,9, respectively. What is the preorder traversal of this tree?", "options": ["a. 9,7,3,1,5,13,11", "b. 9,7,3,1,5,11,13", "c. 9,7,3,5,1,13,11", "d. 9,7,3,1,5,11,13"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "26", "question_text": "Given a binary tree with 16 nodes, and it has 3 nodes with degree 1. How many leafs in this tree?", "options": ["a. 5", "b. 6", "c. 7", "d. 8"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "27", "question_text": "In the relational database, a column in a relation is called _______.", "options": ["a. dictionary", "b. item", "c. tuple", "d. attribute"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "28", "question_text": "Which of the following extracts columns from a relation?", "options": ["a. SELECT", "b. PROJECT", "c. CHO<PERSON>", "<PERSON><PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "29", "question_text": "Which of the following is not an animation software?", "options": ["a. 3ds Max", "b. <PERSON><PERSON>", "c. Maya", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "30", "question_text": "The _______ model is a RNN model.", "options": ["a. LSTM", "b. VGG16", "c. <PERSON>", "d. actor-critic"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "31", "question_text": "The _______ model is not for reinforcement learning.", "options": ["a. GRU", "b. <PERSON><PERSON><PERSON>", "c. DD<PERSON>", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "32", "question_text": "Which of the following is not a branch of machine learning?", "options": ["a. unsupervised learning", "b. particle swarm optimization", "c. reinforcement learning", "d. all of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "34", "question_text": "Which of the following is a NP-complete problem?", "options": ["a. depth-first-search problem", "b. spanning tree problem", "c. all-pairs-shortest-path problem", "d. 0/1 knapsack problem"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "35", "question_text": "Which of the following is not a NP-complete problem?", "options": ["a. all-pairs-shortest-path problem", "b. 0/1 knapsack problem", "c. traveling-salesman problem", "d. clique problem"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "1-a", "question_text": "Convert the Hexadecimal number $(6F)_{16}$ into the following carry number: (3 pts) Octal number", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "1-b", "question_text": "Convert the Hexadecimal number $(6F)_{16}$ into the following carry number: (3 pts) Decimal number", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "2", "question_text": "Please use the do-while loop in C/C++ to calculate the product of integers from 1 to 10.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "3", "question_text": "Given 6 data 6,5,2,3,4,1, write the result of each iteration in insertion sort (in ascending order).", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "4", "question_text": "Given a binary tree with 32 nodes, write its maximal and minimal height.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立中正大學", "department": "資訊管理學系醫療資訊管理碩士班", "year": "113", "question_number": "5", "question_text": "What is the relationship between artificial intelligence, machine learning, and deep learning?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1-a", "question_text": "請詳細解釋物件導向程式設計(Object-Oriented Programming)三個特性?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1-b", "question_text": "請詳細解釋形成死結(Deadlock)的原因,並且說明該如何防止死結?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1-c", "question_text": "請定義演算法(Algorithm)與程式(Program),並且說明兩者的差別?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1-d", "question_text": "請定義白箱測試(glass-box testing)與黑箱測試(black-box testing),並說明兩者的差別?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "2-a", "question_text": "Router 跟 Switch 都是構成網路架構的設備,但是兩者之間的用途是不同的,請定義兩者的功能,並且說明兩者的差別?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "2-b", "question_text": "在建構自己的網路服務的時候可以自己購買主機並且租用固定IP來架設Client-Server的服務,但是也可以尋找雲端服務的業者租用雲端服務。請就資料儲存、費用成本、可擴充性與系統可靠性來討論Client-Server與Cloud Computing的差別?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "2-c", "question_text": "ping 指令常用來確定與另外一台電腦連線是否正常,當我們使用ping 指令時何種封包會被用來傳送到指定位址?(3%)在OSI模型當中,此封包所使用到的哪一層的通訊協定?(3%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "3", "question_text": "請回答以下問題(25分)\n嘉義大學合作社的銷售系統資料庫中有三個資料表,分別是訂單資料表 orders、訂單明細資料表 items 和客戶資料表 customers,其中訂單資料表中包含訂單編號欄位 order_id、客戶編號欄位 customer_id 和訂單日期欄位 order_date,訂單明細資料表中包含訂單編號欄位 order_id、產品編號欄位 product_id、產品名稱欄位 product_name 和數量欄位 quantity,客戶資料表中包含客戶編號欄位 customer_id 和客戶姓名欄位 customer_name。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4-a", "question_text": "請寫一SQL查詢,顯示每筆訂單的訂單編號、訂單日期和下訂單的客戶姓名?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4-b", "question_text": "請寫一SQL查詢,顯示2023/12/7所有客戶的訂購明細,訂購明細須包含客戶姓名、產品名稱和數量?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4-c", "question_text": "請寫一SQL 查詢,顯示客戶「Alan」於2023年期間採購的產品明細,產品明細須包含訂單日期、產品名稱和數量?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4-d", "question_text": "客戶來電客訴訂單編號10303的訂單,Ncyu Milk只送了9瓶,請寫一SQL 更新,將該訂單中Ncyu Milk的數量改成9?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4-e", "question_text": "請說明何謂關聯式資料庫第二正規化(2NF),上面的資料表是2NF嗎?如果不是請對該資料表進行第二正規化?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1-d", "question_text": "計算每個類別的F1分數和總體的F1分數？", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立嘉義大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1-e", "question_text": "要如何知道模型有無過擬合(Overfitting)或擬合不足(Underfitting)？", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "1-1", "question_text": "大型語言模型(Large Language Model) (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "1-2", "question_text": "智能合約(Smart Contract) (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "1-3", "question_text": "邊緣運算(Edge Computing) (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "1-4", "question_text": "可解釋人工智慧(Explainable AI, XAI) (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "1-5", "question_text": "知識圖譜(Knowledge Graph) (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "2", "question_text": "黃金分割搜尋法(Golden Section Search)是一種數值最佳化方法,用於尋找一維函數的極小值。主要方法是在搜尋區間內,以黃金分割比例劃分區間,並選擇能夠使目標函數值較小的那個子區間繼續搜尋。黃金分割比例通常是根號5減1除以2,其近似值約為0.618。在每一步中,搜尋區間被從左右兩邊以黃金分割比例分割為兩部分,然後通過比較兩個分割點處的函數值,決定縮小區間的位置。這樣的過程不斷重複,直到達到預先給予的誤差容忍度,找到極小值的近似位置。試回答下列問題:(30%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "2-1", "question_text": "請用任何一種妳/你所熟悉的電腦程式語言,實作黃金分割搜尋法來找到一維函數的極小值。請實作一個函數 golden search,接受以下參數:(20%)\nfunc:一個單變數函數$(x-2)^2$,表示待最小化的目標函數。\na和b:搜尋區間[a,b]的上下界。\ntolerance:誤差容忍度,當兩次連續迭代的極小值之間的距離小於 tolerance時,則停止迭代。\n函數需傳回找到的極小值X。", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "2-2", "question_text": "相較於二元搜尋法(Binary Search),黃金分割搜尋法有那些優點? (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "2-3", "question_text": "黃金分割搜尋法與二元搜尋法的時間複雜度與空間複雜度分別為何?(5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "3", "question_text": "考慮一個學生成績的簡單資料庫系統,包含以下兩個表格,試回答以下問題:(30%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "3-1", "question_text": "以SQL指令找出每位學生的姓名、院系,以及他們修讀的所有課程的平均分數。(10%)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "3-2", "question_text": "以SQL指令找出每門課程的平均分數,並按照平均分數由高到低排序。(10%)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "3-3", "question_text": "以SQL指令找出在每個院系中,每個年級的學生數量。(10%)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "圖書資訊與檔案學研究所圖書資訊學組", "year": "113", "question_number": "4", "question_text": "請解釋作業系統中的「臨界區域」(Critical Section)是什麼,並請提出一個簡單的實際例子來說明?(15%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "1", "question_text": "The CAP theorem explains some of the competing requirements in a distributed system with replication. Which statements are accurate regarding this theorem?", "options": ["a. C means \"Consistency\", which guarantees that the nodes will have the same copies of a replicated data item in various transactions.", "b. A means \"Atomicity\", which guarantees that each transaction is a single unit that either succeeds completely or fails completely.", "c. P means \"Partition tolerance\", which guarantees that the system can continue operating even if the network fails and the nodes form disconnected partitions.", "d. Distributed databases like MongoDB and Cassandra tend to prioritize \"C\" and \"P\" at the cost of sacrificing \"A\".", "e. A MySQL database configured in the Master-Slave setting satisfies \"C\" and \"A\" but compromises \"P\"."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "2", "question_text": "Which statements about SQL and NoSQL databases are most accurate?", "options": ["a. SQL is based on a structured query language with a fixed schema, while NoSQL is schema-less and can store unstructured data.", "b. NoSQL is designed to handle distributed data stores, making them a common choice for cloud storage and big data applications.", "c. Database normalization (i.e. 1NF, 2NF, 3NF, and 4NF) does not apply to NoSQL databases.", "d. NoSQL databases often sacrifice some of the ACID (Atomicity, Consistency, Isolation, Durability) properties for better scalability and performance.", "e. SQL is predominantly better at handling complex queries due to its rigid and well-defined schema."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "3", "question_text": "Which statements are most accurate regarding network protocol behavior and standards?", "options": ["a. OSPF is a routing protocol that calculates the shortest path for data packets to travel within an IP network using a path cost metric.", "b. ARP operates at the Internet layer to translate network addresses such as IP addresses into physical MAC (Media Access Control) addresses.", "c. ICMP is used for establishing and managing session states, often implemented at the Transport layer, alongside TCP and UDP.", "d. HTTP/2 introduces multiplexing of requests over a single TCP connection to reduce the amount of required connections.", "e. SSL/TLS protocols work at the Network layer to provide secure encryption capabilities for data packets"], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "4", "question_text": "What are the pros and cons of using threads compared to processes in parallel programming?", "options": ["a. Threads lead to more efficient system resource utilization than separate processes.", "b. Threads have lower context-switch overhead because they do not need to switch the memory space.", "c. Processes are more resilient to errors since one process crashing does not affect other processes. In contrast, thread errors can cause the entire process to crash.", "d. Threads need more complex synchronization mechanisms to manage shared resources and to avoid concurrency issues.", "e. Processes are generally easier to implement and debug, while threads can introduce complicated debugging scenarios because of shared state and asynchronous execution."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "5", "question_text": "Which statements about the Unified Modeling Language (UML) are most accurate?", "options": ["a. Sequence diagram shows the interaction between objects in a system by modeling messages exchanged between them over time.", "b. Activity diagram shows the workflow of a system by modeling activities, actions, and control flows.", "c. Use case diagram shows the functionality of a system by modeling actors, use cases, and their relationships.", "d. Composition represents a \"has-a\" relationship, while Aggregation represents an \"is-part-of\" relationship between the aggregated object and the aggregate object.", "e. A UML use case describes the overall behavior of the system from the perspective of the system."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "6", "question_text": "Which statements are most accurate regarding cybersecurity?", "options": ["a. A rootkit is a set of specialized tools for system administrators to manage and monitor system security.", "b. A Zero-Day attack targets a vulnerability after the vulnerability is disclosed and before it is fixed.", "c. Post-quantum cryptography exploits quantum mechanics to secure communication and enhance cryptography.", "d. Social engineering is a type of attack that manipulates individuals into sharing confidential information that they should not share.", "e. Two-factor authentication (2FA) is a security process aiming to prevent the Man-in-the-Middle attack."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "7", "question_text": "Which of the following statements accurately describes the mechanisms of the GPT (Generative Pre-trained Transformer) architecture?", "options": ["a. GPT uses an attention mechanism to focus on relevant parts of the input sequence and to facilitate context-aware text generation.", "b. GPT uses a pre-training phase on a large text corpus to learn general language patterns before fine-tuning on task-specific datasets.", "c. GPT uses recurrent neural layers that process input text sequentially, retaining state information across different positions in the text.", "d. GPT uses reinforcement learning to improve its predictions based on user feedback.", "e. GPT relies on stacked transformer blocks that utilize both self-attention and feed-forward neural networks within each block."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "9", "question_text": "Which statements about software development are most accurate?", "options": ["a. Waterfall development sets clear milestones in each stage and moves to the next stage only after the goal of the current stage is fulfilled.", "b. Waterfall development appreciates a high degree of customer involvement so that the project outcome can be adjusted in a timely manner.", "c. Waterfall development is more suitable than Agile when multiple software components must be designed in parallel for final integration.", "<PERSON><PERSON> allows the team members to be involved with other work depending on the phases, while Waterfall demands highly devoted team members throughout the development."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "1", "question_text": "(5 points) What problems would imbalanced training data cause in machine learning? How do you mitigate these problems? Give at least one problem and one solution.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "2", "question_text": "(10 points) Explain the major differences between virtual machines (VMs) and containers. Give a scenario where VMs are preferable to containers, and a scenario where containers are preferable to VMs.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "4", "question_text": "(15 points) Inspect the following multi-threaded Java program and answer the questions.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "4.1", "question_text": "Argue that no two threads can print to the terminal at the same time.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "4.2", "question_text": "Is the program still thread-safe after we change `synchronized(this)` to `synchronized(lock)`? Why?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "4.3", "question_text": "Is it true that every thread eventually prints a message? Why?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "5", "question_text": "Consider a table `Customers` that contains fields `CustomerID` and `Country`. Write a MySQL code to list the number of customers in each country, ordered by the country with the most customers first.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "6", "question_text": "Suppose that you have two tables, `Customers` and `Orders`. Each entry of `Customers` contains two fields: `CustomerID` and `CustomerName`. Each entry of `Orders` contains two fields: `CustomerID` and an `OrderID`. A customer can be related to several orders through `CustomerID`. Write a MySQL code to list the customer names and the number of orders associated with each customer.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "7", "question_text": "<PERSON>'s triangle is defined as below. The row number starts from 1 (e.g., Row 1 is [1], Row 2 is [1 1], Row 3 is [1 2 1], etc.). Write a function that takes a row number $k \\ge 1$ and returns Row $k$ of the triangle. Your program should run in $O(k^2)$ time and $O(k)$ space.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班科技組", "year": "113", "question_number": "1", "question_text": "Examples:\nInput: 1 Output: [1]\nInput: 3 Output: [1 2 1]\nInput: 4 Output: [1 3 3 1]", "options": [], "type": "other", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "1-E", "question_text": "Agile relies on careful documentation and testing to ensure the quality and understanding of the intermediate software deliverables.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "2-B", "question_text": "Activity diagram shows the workflow of a system by modeling activities, actions, and control flows.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "2-D", "question_text": "Composition represents a \"has-a\" relationship, while Aggregation represents an \"is-part-of\" relationship between the aggregated object and the aggregate object.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "3-A", "question_text": "SQL is based on a structured query language with a fixed schema, while NoSQL is schema-less and can store unstructured data.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "3-B", "question_text": "NoSQL is designed to handle distributed data stores, making them a common choice for cloud storage and big data applications.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "3-C", "question_text": "Database normalization (i.e. 1NF, 2NF, 3NF, and 4NF) typically does not apply to NoSQL databases.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "3-D", "question_text": "NoSQL databases often sacrifice some of the ACID (Atomicity, Consistency, Isolation, Durability) properties for better scalability and performance.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "3-E", "question_text": "SQL is predominantly better at handling complex queries due to its rigid and well-defined schema.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "4", "question_text": "Which statements are most accurate regarding network protocol behavior and standards?", "options": ["a. OSPF is a routing protocol that calculates the shortest path for data packets to travel within an IP network using a path cost metric.", "b. ARP operates at the Internet layer to translate network addresses such as IP addresses into physical MAC (Media Access Control) addresses.", "c. ICMP is used for establishing and managing session states, often implemented at the Transport layer, alongside TCP and UDP.", "d. HTTP/2 introduces multiplexing of requests over a single TCP connection to reduce the amount of required connections.", "e. SSL/TLS protocols work at the Network layer to provide secure encryption capabilities for data packets transmitted across networks."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "5", "question_text": "Which statements are most accurate regarding cybersecurity?", "options": ["a. A rootkit is a set of specialized tools for system administrators to manage and monitor system security.", "b. A Zero-Day attack targets a vulnerability after the vulnerability is disclosed and before it is fixed.", "c. Post-quantum cryptography is a technology that exploits quantum mechanics to secure communication and enhance cryptography.", "d. Social engineering is a type of attack that manipulates individuals into sharing confidential information that they should not share.", "e. Two-factor authentication (2FA) is a security process aiming to prevent the Man-in-the-Middle attack."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "6", "question_text": "Which statements are correct for the Open Systems Interconnection (OSI) model?", "options": ["a. IEEE 802 standards (like 802.3 (Ethernet) and 802.11 (Wi-Fi)) operate at the Network layer.", "b. TCP is a connection-oriented transmission protocol operating in the Session layer.", "c. Protocols such as HTTP, FTP, and SMTP operate in the Application layer.", "d. TCP/IP is a simplified version of OSI by removing three layers from the latter model.", "e. While the OSI model is more solid in theory, the TCP/IP model is more popular for practical and historical reasons."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "7", "question_text": "Which statements about laaS, PaaS, and SaaS are correct?", "options": [], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "7", "question_text": "下列敘述何者正確？", "options": ["a. laas是雲端運算的原始形式，幾乎所有主要的雲端服務供應商現今都提供此服務。", "b. SaaS提供一個雲端託管的開發環境，用於執行和管理集中式位置的應用程式軟體。", "c. 使用者通常透過應用程式介面(API)與IaaS互動，並透過圖形使用者介面(GUI)存取PaaS和SaaS。", "d. SaaS通常支援API或協定，讓使用者可以在不同的服務供應商之間遷移。", "e. 將IaaS、PaaS和SaaS技術整合到企業資訊系統中，通常可以提高系統的彈性和資料安全性。"], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "8", "question_text": "關於開源授權的敘述，哪些是最準確的？", "options": ["a. 如果軟體使用GPL授權的程式碼，則在散布時必須公開原始程式碼。", "b. MIT授權要求在所有軟體副本中包含著作權和許可聲明。", "c. Apache授權允許軟體取得專利，但禁止使用授權者的商標。", "d. GPL允許同時取得軟體專利和使用授權者商標的權利。", "e. MIT和Apache允許商業使用授權軟體，但GPL則不行。"], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "9", "question_text": "CAP定理說明了分散式系統中一些相互競爭的需求。關於此定理，哪些陳述是準確的？", "options": ["a. \"C\"代表一致性，保證節點在各種交易中都具有複製資料項的相同副本。", "b. \"A\"代表原子性，保證每個交易都是一個單元，要麼完全成功，要麼完全失敗。", "c. \"P\"代表分區容錯性，保證即使網路發生故障且節點形成斷開連接的分區，系統也能繼續運作。", "d. 像MongoDB和Cassandra這樣的分散式資料庫傾向於優先考慮\"C\"和\"P\"，而犧牲\"A\"。", "e. 在主從設定中配置的MySQL資料庫滿足\"C\"和\"A\"，但會損害\"P\"。"], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "10", "question_text": "Which statements accurately reflect the pros and cons of 4G and 5G technologies?", "options": ["a. 5G enables applications such as VR, AR, and IoT due to its high data rates and low latency.", "b. 5G deployment requires a denser network of cells than 4G, which can lead to higher infrastructure costs and more complex network maintenance.", "c. 4G networks tend to be more energy-efficient than 5G networks because the latter's higher data rates and lower latency need more power.", "d. 4G is currently more widespread and accessible than 5G, offering sufficient speeds for most conventional mobile and broadband uses at a lower operational cost.", "e. The higher frequency bands used by 5G can result in reduced penetration through walls and obstacles compared to 4G."], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "11-1", "question_text": "For each of the following commands, explain its functionality and give a situation when you need to use that command: 1. ifconfig 2. nslookup 3. traceroute", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "11-2-1", "question_text": "What problems would data imbalance cause in supervised machine learning?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "11-2-2", "question_text": "What characteristics of a trained model indicate possibly imbalanced training data?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "11-2-3", "question_text": "How do you mitigate the problems caused by imbalanced data?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "11-3-1", "question_text": "The List interface is implemented by LinkedList, ArrayList, and Vector, among others. In what situation would you use one implementation instead of another? Why?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "11-3-2", "question_text": "Similarly, what are the differences between the classes HashMap and TreeMap? When do you choose to use one class instead of the other?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "11-4", "question_text": "\"Machine learning bias\" refers to the phenomenon that a machine learning model makes predictions correlated to sensitive features like gender and race, even though such correlations are not supposed to be present in reality.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "1", "question_text": "Give two typical sources/causes of biases in supervised machine learning.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "5", "question_text": "(4 points) Consider a table Customers that contains fields CustomerID and Country. Write a SQL code to list the number of customers in each country, ordered by the number of customers in the decreasing order.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "6", "question_text": "(4 points) Suppose that you have two tables, Customers and Orders. Each entry of Customers contains two fields: CustomerID and CustomerName. Each entry of Orders contains two fields: CustomerID and an OrderID. A customer can be related to several orders through CustomerID. Write a SQL code to list the customer names and the number of orders associated with each customer.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立政治大學", "department": "資訊管理學系碩士班資管組", "year": "113", "question_number": "7", "question_text": "(12 points) We would like to estimate an unknown real number $1 \\le x < \\infty$. Suppose we have a Boolean function $f(\\cdot)$ such that $f(y)$ returns true if $x \\le y$, and $f(y)$ returns false otherwise. Describe an optimal algorithm that, given an error bound $0 < \\epsilon < 1$, computes two nonnegative real numbers low and high satisfying\n\\begin{itemize}\n    \\item $low \\le x \\le high$\n    \\item $high - low \\le \\epsilon$\n\\end{itemize}\nArgue that your algorithm invokes the function $f$ for at most $O(\\lg \\frac{1}{\\epsilon} + \\lg x)$ times.", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1", "question_text": "雲端遷移(Migration)已在全球產業形成不可逆的趨勢,例如台灣的金管會已於2023年大幅簡化了上雲申請流程,促使金融業展開新一波加速上雲的浪潮。而在法規上極為嚴苛的鄰國日本,亦於2023年底廢除FD(floppy disk)、CD-ROM等阻礙發展線上應用和公部門數位轉型的過時儲存媒體,在不指定特定媒體的情況下,讓雲端工具成為可能的選項。試問:", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1-1", "question_text": "(1)為何公有雲(Public Cloud)對雲端遷移來說十分重要?(10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1-2", "question_text": "(2)請用名詞條列的方式,列舉至少三項、至多五項雲端運算(Cloud Computing)的特性,並說明之。(10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "3", "question_text": "Define and differentiate between Artificial General Intelligence (AGI), General AI (GAI), and Generative AI. Provide a detailed comparison in terms of their capabilities, theoretical frameworks, and potential applications. How does each type of AI address different challenges and objectives in the field of artificial intelligence?", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4", "question_text": "Why is quantization essential for improving the computational efficiency of generative AI models? What are the different types of quantization techniques used in generative AI models? Please calculate the GPU VRAM requirements for a Large Language Model (LLM) with 7 billion parameters using three different data formats: FP32, INT8, and INT4. Provide the specific VRAM values in gigabytes for each format and explain how the memory requirement changes with each data format.", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "5", "question_text": "What are the differences and similarities in data representation and modeling between Vector Databases and Graph Databases?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺北教育大學", "department": "數學暨資訊教育學系人工智慧與資訊教育碩士班", "year": "113", "question_number": "1", "question_text": "請解釋操作作業系統(OS, Operation System)的主要功能是什麼？舉例說明它們在計算機運作中的重要性。(15%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "數學暨資訊教育學系人工智慧與資訊教育碩士班", "year": "113", "question_number": "3", "question_text": "考慮以下 python的程式區塊,程式錯誤的地方是什麼?(10%)\n```python\nnumbers = [1, 2, 3, 4, 5]\nindex = 0\nwhile index <= len(numbers):\n    print(numbers[index])\n    index = index + 1\n```", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺北教育大學", "department": "數學暨資訊教育學系人工智慧與資訊教育碩士班", "year": "113", "question_number": "4", "question_text": "請問OpenAI的ChatGPT 4.0提供了哪些服務,其背後的原理是什麼呢?並舉例說明其在教育與生活上的應用為何?(15%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北教育大學", "department": "數學暨資訊教育學系人工智慧與資訊教育碩士班", "year": "113", "question_number": "6", "question_text": "國小階段如果要教物聯網觀念與積木式的程式設計,請問你會選擇使用何種開發板及程式設計語言?(15%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北教育大學", "department": "數學暨資訊教育學系人工智慧與資訊教育碩士班", "year": "113", "question_number": "7", "question_text": "何謂Google Effects?對未來的科技輔助教學與學習有何影響?(15%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "1-1", "question_text": "何謂Deadlock?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "1-2", "question_text": "發生Deadlock的必要條件為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "1-3", "question_text": "Deadlock Prevention 與 Deadlock Avoidance有何不同?何者會導致較低的資源利用率?請說明其原因。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "1-4", "question_text": "何謂Atomic Instruction?舉例說明其有何用途。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "2-1", "question_text": "什麼是虛擬記憶體(Virtual Memory)?為何需要虛擬記憶體?其是如何運作的?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "2-2", "question_text": "分頁(Paging)和分段(Segmentation)這兩種記憶體管理技術有何不同?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "2-3", "question_text": "什麼是Page Table?舉例說明其如何作用?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "2-4", "question_text": "何謂 Page Fault?當其發生時,應如何處理?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "3-1", "question_text": "何謂Ad Hoc 網路模式?何謂 Infrastructure 網路模式?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "3-2", "question_text": "什麼是WEP、WPA和WPA2?它們是用來做什麼的?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "3-3", "question_text": "SSID是什麼?它的作用為何?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "3-4", "question_text": "何謂Hidden Node Problem?如何解決?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "4", "question_text": "Show the IEEE 754 Excess_127 format (single precision) representation of the number R. Here, R=A+B, and, A and B are also IEEE 754 Excess_127 format (single precision) representation.", "options": [], "type": "long-answer", "image_file": ["國立臺北教育大學_資訊科學碩士班_113_4-1.png"], "image_regions": [{"x": 120, "y": 630, "width": 200, "height": 80}], "predicted_category": "演算法"}, {"school": "國立臺北教育大學", "department": "資訊科學碩士班", "year": "113", "question_number": "5", "question_text": "請問下列C語言是否可以正確執行?若不可以執行請指出哪裡有錯誤並修正成正確的語法?若可以執行請問執行後的輸出結果?(20分)", "options": [], "type": "short-answer", "image_file": ["國立臺北教育大學_資訊科學碩士班_113_5-1.png"], "image_regions": [{"x": 130, "y": 125, "width": 400, "height": 270}], "predicted_category": "程式設計"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "1-1", "question_text": "下列哪一個不是程式語言的種類？", "options": ["a. 組合語言", "b. 機器語言", "c. 自然語言", "d. 高階語言"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "1-2", "question_text": "二進制數「1011」等於十進制中的多少？", "options": ["a. 9", "b. 11", "c. 13", "d. 15"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "1-3", "question_text": "哪種語言是高級程序設計語言？", "options": ["a. 機器語言", "b. 匯編語言", "c. C++", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "1-4", "question_text": "TCP/IP是指哪兩種協議？", "options": ["a. 傳輸控制協議和網際網路協議", "b. 文本傳輸協議和國際協議", "c. 技術通訊協議和接口協議", "d. 電話通訊協議和身份識別協議"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "1-5", "question_text": "在數據庫管理系統中，SQL用於什麼目的？", "options": ["a. 創建圖形界面", "b. 搜索和操縱數據", "c. 創建和修改硬件配置", "d. 進行網絡連接"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "1-6", "question_text": "下列哪一個不是網路通訊協定？", "options": ["a. TCP/IP", "b. HTT<PERSON>", "c. FTP", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "1-7", "question_text": "下列哪一個不是操作系統的功能？", "options": ["a. 資源管理", "b. 程式編譯", "c. 使用者介面", "d. 系統維護"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "1-9", "question_text": "哪一種數據結構適合用於實現遞歸算法？", "options": ["a. 陣列", "b. 隊列", "c. 堆疊", "d. 圖"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "2-1", "question_text": "解釋什麼是IoT，並給出兩個實際應用的例子。(15分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "2-2", "question_text": "請比較關聯式資料庫與非關聯式資料庫的特性及適用場合？(15分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "2-3", "question_text": "請執行下列python程式區段的結果為何，請寫出完整答案(10分)\n(注意：程式執行時，請輸入年齡：19。是否就學(y/n):n)\nage = int(input(\"請輸入年齡：\"))\ndata = input('是否就學(y/n):')\nif data == 'y':\nelse:\n    school = False\n    pay = 0\n    if age > 5 and school == True:\n        pay = 10\n    elif age >= 5 and school == False:\n        if age <= 17:\n            pay = 30\n        else:\n            pay = 60\n    print('入場費=', pay)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺北科技大學", "department": "經營管理系研究所不分組", "year": "113", "question_number": "2-4", "question_text": "請執行下列python程式，結束迴圈後，total的值為多少？(10分)\ntotal = 1\nn = 5\nwhile (i <= n):\n    total *= i\n    i += 1\nprint(total)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "1-1", "question_text": "The worst-case running time of quicksort algorithm is O(n²).", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "1-2", "question_text": "If a problem that is in the class NP has a polynomial time solution, then P is equal to NP.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "1-3", "question_text": "$\\pi^2 + 4^2 = 2(\\pi^2)$", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "1-4", "question_text": "The shortest path tree from a root u for a positively weighted undirected graph G does not change if the weight of every edge in G is doubled (i.e., the new weight is twice the old weight).", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "1-5", "question_text": "Let T be a minimum spanning tree (MST) of graph G. Given a connected subgraph H of G, $T \\cap H$ is contained in some MST of H.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "2-2", "question_text": "Characters B and H have the same path length from the root in T.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "2-3", "question_text": "Recall that the weighted path length (WPL) is defined as $\\sum (f_i p_i)$ where $f_i$ is the frequency of character i and $p_i$ is the length of the path in T from the root to the character i. Then, the weighted path length of T is 249.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "2-4", "question_text": "The path length of character C is 2.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "2-5", "question_text": "Consider another set S' of characters (A, B, C, D, E, F, G, H) with relative frequencies (10, 5, 23, 18, 13, 12, 15, 4). Let T' be an optimal <PERSON><PERSON><PERSON> code tree for S'. Then, WPL(T) and WPL(T') differ by 13.", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "3-1", "question_text": "$C_{1,5} = 1655$", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "3-2", "question_text": "$C_{2,6}$ is computed before $C_{1,3}$", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "3-3", "question_text": "$C_{2,5}$ is derived from $C_{2,3}$ and $C_{4,5}$", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "3-4", "question_text": "The optimal order to multiply $A_1, ..., A_6$ with fewest number of scalar multiplications is $((A_1A_2)((A_3(A_4A_5))A_6))$", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "4-1", "question_text": "Let G be an undirected, connected graph with at least one vertex of odd degree. Show that G contains no Eulerian walk. An Eulerian walk is a cycle in which each edge in G is passed exactly once.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "4-2", "question_text": "Consider the following recurrence and derive the asymptotic upper bound using the \\\"big oh\\\" notation with T(1) = d for some constant d. \n$T(n) = 35T(\\frac{n}{7}) + 7n^3$", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "4-3", "question_text": "Given two sets A and B represented as sorted sequences, describe an efficient algorithm for computing A△B, which is the set of elements that are in A or B, but not in both. Please write the pseudo-code and give the time complexity of your algorithm.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "5-1-a", "question_text": "Port number is used for Transport Layer", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "5-1-b", "question_text": "IP address is used in Network Layer", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "5-1-c", "question_text": "MAC address is used in the physical layer (Physical Layer)", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "5-1-d", "question_text": "Email address is used in Application Layer", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "5-2-a", "question_text": "Router", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "5-2-c", "question_text": "Server", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "5-2-d", "question_text": "Bridge", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "6-1", "question_text": "OSI seven-layer network communication model, what is the encapsulation sequence for data transmission from the upper layer to the lower layer ?", "options": [], "type": "fill-in-the-blank", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "6-2", "question_text": "Among common encryption technologies, which of the following two combinations are asymmetric encryption technologies?", "options": ["a. DES", "b. RSA", "c. A<PERSON>", "d. <PERSON>", "e. ECC"], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "7-1", "question_text": "In a computer system equipped with a Translation Lookaside Buffer (TLB), the efficiency of memory access is critical. Assume that the probability of finding the desired page number in the TLB is 95 percent. The time taken to search the TLB is 20 nanoseconds. However, if the page number is not found in the TLB, the system must access the memory, which takes an additional 100 nanoseconds. Calculate the Effective Memory Access Time (EAT) for this system.", "options": ["a. 24 nanoseconds", "b. 25 nanoseconds", "c. 120 nanoseconds", "d. 125 nanoseconds"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "7-2", "question_text": "In a paging system with a page table, which of the following statements is true?", "options": ["a. The page table is stored in the CPU", "b. A page table maps virtual addresses to physical addresses", "c. Each process has the same page table to ensure consistency", "d. Paging eliminates the need for fragmentation handling"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "7-3", "question_text": "The memory allocation strategies are used to determine where incoming programs or data should be placed in memory. Which of the following descriptions is corrent?", "options": ["a. Worst Fit prioritizes using the largest partition, resulting in larger remaining blocks, which are more likely to accommodate other space requirements.", "b. <PERSON> Fit uses the block closest in size to the requirement, minimizing the size of the remaining allocatable memory blocks and improving space utilization in memory.", "c. First Fit allocates the first block that is sufficiently large, with the advantage of being simple and fast in allocation.", "d. Next <PERSON>t starts searching from the point of the last allocation, looking for a block closest in size to the requirement.", "e. All of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "7-4", "question_text": "In the file systems, which of the following statements is true?", "options": ["a. Directories can only contain files, not other directories", "b. File allocation table (FAT) does not support hierarchical file structure.", "c. Inodes in UNIX contain the actual data of the files.", "d. A symbolic link can point to a file on a different file system."], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "7-5", "question_text": "Regarding the deadlocks, which of the following statements is correct?", "options": ["a. Deadlock prevention is achieved by designing a system that cannot enter a deadlock state.", "b. Deadlock avoidance involves dynamically checking resource allocation at runtime to ensure the system does not enter an unsafe state.", "c. An operating system can effectively avoid deadlocks without knowing the future resource needs of processes.", "d. A deadlock situation can arise when the following four conditions occur simultaneously in a system: Mutual Exclusion, Hold and Use, No Preemption, and Circular Wait."], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "7-8", "question_text": "Consider the following snapshot of a system, calculate the Need matrix for each process and verify the accuracy of the given resource request claims (a) to (e) for processes P0 to P4. Identify the correct one.", "options": ["a. Need matrix for P0 is (1,1,0,1)", "b. Need matrix for P1 is (1,0,0,1)", "c. Need matrix for P2 is (0,1,0,0)", "d. Need matrix for P3 is (1,1,1,1)", "e. Need matrix for P4 is (0,1,1,0)"], "type": "single-choice", "image_file": ["國立臺北科技大學_資訊工程研究所_113_7-8-1.png"], "image_regions": [{"x": 560, "y": 600, "width": 200, "height": 100}], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "7-9", "question_text": "Regarding to the above question 7(8), is the system in a safe state? Please explain your answer in detail.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "8-1", "question_text": "Translate the following C code to MIPS assembly code. Please fill the blanks (8-a)~(8-d) with correct statements. (8 pts, each 2 pts)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺北科技大學", "department": "資訊工程研究所", "year": "113", "question_number": "8-3", "question_text": "For the 5 stages pipeline processor as the below figure, assume the following sequence of instructions: (10 pts, each 2 pts)", "options": [], "type": "fill-in-the-blank", "image_file": ["國立臺北科技大學_資訊工程研究所_113_8-3-1.png"], "image_regions": [{"x": 500, "y": 350, "width": 400, "height": 250}], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "1-a", "question_text": "Having multiple cores in a processor is potentially beneficial for the performance of task execution. Is it beneficial for the throughput and/or response time of task execution? Why? (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "2-a", "question_text": "Draw a generic storage-device hierarchy. (5%)", "options": [], "type": "draw-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "2-b", "question_text": "Explain the organization of the hierarchy in terms of the storage capacity and access time. (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "3-a", "question_text": "Explain search tree and hash table in details. Provide an example of each. (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "3-b", "question_text": "Why are search trees still being widely used, when hash tables seem to provide better expected access performance? (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "5", "question_text": "Logical operations. Use C or C++ programming language to implement a function `popu_count()` that returns the number of bit-1s in an unsigned integer n. The prototype of the function should be `int popu_count(unsigned int n)`. For convenience, you may safely assume that n is 64-bit long, if needed. Please do not use any libraries such as C++ STL. Make your program as efficient as possible. (10%)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "6-a", "question_text": "What are processes and threads? Explain and compare them in detail. (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "6-b", "question_text": "What are the advantages and limitations of threads, as compared to processes? (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "7", "question_text": "Concerning the asymptotic notations. Show that $\\lg(n!) = O(n\\lg n)$. (10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "8-a", "question_text": "Race condition. (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "9", "question_text": "Provide simple example(s) to explain the purposes and mechanisms of random backoff? How does it work? (5%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺北科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "10", "question_text": "There are many neurons in a neural network, each activated by the activation function. What is an activation function? Explain <PERSON>g<PERSON><PERSON> and ReLU, two popular choices of activation functions. What are their primary advantages and drawbacks, if any? (10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "1-1", "question_text": "作業系統的畢雷地現象(Belady anomaly)。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "1-2", "question_text": "簡述作業系統的程序(process)滿足死結(deadlock)的四個條件。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "1-3", "question_text": "滿足關聯資料庫的第三正規化(Third normal form)的條件。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "2-1", "question_text": "解釋 Prompt engineer?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "2-2", "question_text": "從資訊搜尋與尋求觀點來看,生成式人工智慧(Generative Artificial Intelligence),如:Open AI推出的ChatGPT相關技術對於人們使用實體與數位圖書館、博物館或科學館學習可以產生的幫助或服務?(至少250字)", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "3-2", "question_text": "圖1為年齡與信用狀況的分布圖,請根據圖1畫出決策樹圖。", "options": [], "type": "draw-answer", "image_file": ["國立臺灣師範大學_圖書資訊學研究所_113_3-2-1.png"], "image_regions": [{"x": 140, "y": 620, "width": 200, "height": 150}], "predicted_category": "新興技術"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "3-3", "question_text": "於樹中標註每個葉節點(leave node)的信用不良的數目?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "3-4", "question_text": "請寫出決策樹中,預測信用良好之正確率為80%的規則。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "4-1", "question_text": "Please partition the table below into 3 tables and use SQL commands to list: 1. All the teachers in this database. 2. All the students that take a certain class, e.g., BCC", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "6-1", "question_text": "下列何者比較不是關聯式資料庫系統?", "options": ["a. SQLite", "b. MySQL", "c. No SQL", "<PERSON><PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "6-2", "question_text": "下列哪一種不是在伺服器端執行的程式環境或程式架構?", "options": ["<PERSON><PERSON>", "b. <PERSON>", "c. <PERSON>.js", "<PERSON><PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "6-3", "question_text": "下列哪一種不是網路通訊協定(network protocol)?", "options": ["a. Unix shell", "b. telnet", "c. FTP", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "6-4", "question_text": "在Python中,依序執行:L1=[1, 2]; L2=[L1, 3]; L1=[4],最後L2會是甚麼?", "options": ["a. L2==[[1, 2],3]", "b. L2==[[4],3]", "c. L2==[L1, 3]", "d. 以上皆非"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "5", "question_text": "對位元串 11011001 使用循環左移運算(circular left shift operation) 2次,其結果為何?", "options": ["a. 01100000", "b. 01100111", "c. 01110110", "d. 10110110"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "7", "question_text": "若數字以8位元二的補數來表示,則$(11000010)_2 + (11111110)_2$的結果為何?", "options": ["a. C(64)$_{10}$", "b. C(-63)$_{10}$", "c. C(-128)$_{10}$", "d. C(-64)$_{10}$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "8", "question_text": "下列那一個是最普及的編碼系統且僅需要七個位元(bits)即可儲存一個符號?", "options": ["a. ASCII", "b. EBCDIC", "c. Unicode", "d. Big5"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "9", "question_text": "一資料庫中擁有800篇文章而與某主題相關的文獻有200篇,某檢索者由此資料庫檢索到100篇文章,與該主題相關的文章有30篇,請問此次檢索的準確率(Precision)為多少?", "options": ["a. 15%", "b. 25%", "c. 30%", "d. 50%"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "10", "question_text": "下列網路的那一層(OSI模型),係關於網路的位址敘述,以及決定網路的路由(Routing)?", "options": ["a. 傳輸層(Transport Layer)", "b. 資料連結層(Data Link Layer)", "c. 網路層(Network Layer)", "d. 應用層(Application Layer)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "1", "question_text": "numbers = [1, 2, 3, 4]\nsquared_numbers = map(lambda x: x ** 2, numbers)\nprint(list(squared_numbers))", "options": ["a. [1, 2, 3, 4]", "b. [3, 5, 7, 6]", "c. [2, 4, 6, 8]", "d. [1, 4, 9, 16]"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "2", "question_text": "考慮這組程序(Process) $P_i$，其中 $P = \\{P_1, P_2, P_3, P_4\\}$，如表1所示。假設這些程序的到達順序為 $P_1, P_2, P_3, P_4$。對於FCFS(先到先服務)算法，平均等待時間是多少？", "options": ["a. 9", "b. 9.5", "c. 11", "d. 9.75"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺灣師範大學", "department": "圖書資訊學研究所", "year": "113", "question_number": "3", "question_text": "由CPU生成的地址稱為_____", "options": ["a. physical address", "b. logical address", "c. post relocation register address", "d. Memory-Management Unit (MMU) generated address"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "1", "question_text": "下列哪一個不是開源的學習管理系統(Learning Management System, LMS)? (2分)", "options": ["a. <PERSON>LMS", "b. <PERSON><PERSON>", "c. <PERSON>", "<PERSON><PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "2", "question_text": "在關聯式資料模式聯代數運算子中,下列何者為非原始運算子(Non-primitive Operators)? (2分)", "options": ["a. 交集運算(Intersection)", "b. 差集運算(Set Difference)", "c. 乘積運算(Cartesian Product)", "d. 聯集運算(Union)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "3", "question_text": "下列何者針對802.11無線網路標準之傳輸速度描述正確?(2分)", "options": ["a. 802.11n>802.11aj>802.11ac", "b. 802.11aj>802.11ac>802.11n", "c. 802.11ac>802.11aj>802.11n", "d. 802.11aj>802.11n>802.11ac"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "5", "question_text": "下列針對區塊鏈之描述那個為不正確?(2分)", "options": ["a. 使用拜占庭協議維護資料保密性", "b. 使用挖礦技術將資料加入區塊鏈上", "c. 使用不對稱密碼技術", "d. 可用於跨單位學歷認證"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "6", "question_text": "如果有一個已經排序好的資料陣列,以Binary Search 找出資料22,需要尋找多少次?(2分)  2, 5, 6, 8, 10, 11, 14, 15, 19, 20, 22, 24, 25, 29, 30", "options": ["a. 11", "b. 3", "c. 4", "d. 5"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "7", "question_text": "下列針對 Quick-Sort 何者之描述不正確? (2分)", "options": ["a. Worst case為O(n2)", "b. 是Unstable 的排列方法", "c. Average case為O(nlog2n)", "d. 需要一個額外儲存位置"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "8", "question_text": "無線乙太網路(Wireless Ethernet)的標準是IEEE 802.11,使用下列何者之通訊方式?(2分)", "options": ["a. ISO/OSI", "b. TCP/IP", "c. CSMA/CA", "d. CSMA/CD"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "9", "question_text": "下列何者為5G網路的缺點?(2分)", "options": ["a. 回應時間太快,設備閒置率高", "b. 使用低頻通訊,通訊範圍很短", "c. 傳輸速度快,通訊費很高", "d. 難穿透固體,需建置更多基地台"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "10", "question_text": "下列何者非虛擬網路位址(Virtual IP Address)?(2分)", "options": ["a. **************", "b. **************", "c. **************", "d. **************"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "2", "question_text": "請詳細說明文字生成式AI技術,如ChatGPT,其資料生成的運作原理。(15分)", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "3", "question_text": "請從資料結構模式及資料運作方式兩方面,詳細比較關聯式資料庫系統及非關聯式資料庫系統之差異。(15分)", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "4", "question_text": "以雙層迴圈來設計排序的程式時,請問下面三種排序演算法,對15,6,23,35,11,3進行排序時,每跑完一整圈外圈迴圈(一回合)後,所呈現的數字為何?請填入下表,最後外圈結束後,所有數字從小到大排好。(12分)", "options": [], "type": "fill-in-the-blank", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "5", "question_text": "請問採用霍夫曼編碼(Huffman Coding)壓縮以下英文字「BBBBBAAAAABBBBAAAAAADDDAAAAACCCC」之後,會儲存成的二進位碼是?(8分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "6", "question_text": "插入35,17,78,53,99,25,46,3,將它變成一棵「二元搜尋樹」的圖形。(5分)", "options": [], "type": "draw-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "7", "question_text": "一個二元樹有8個節點:請問此二元樹的前序走訪結果依序是?並請畫出此二元樹。(10分)\n後序:EFCHGDBA\n中序:EFCABHDG", "options": [], "type": "draw-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "8", "question_text": "請以任何你熟悉的語言撰寫出遞迴和迴圈兩種方法來計算N!的值。(10分)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣師範大學", "department": "科技應用與人力資源發展學系碩士班", "year": "113", "question_number": "9", "question_text": "請問 K-Means 是監督式學習還是非監督式學習?請進一步說明其演算法(步驟)。並請舉實例說明K-Means可以在數位學習領域的應用面,提出K-Means在教育上的可能使用之處或情境。(5分)", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "1-a", "question_text": "將十進制值 7.625 轉成 IEEE 754 單精確度浮點數以十六進制表示的值。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "1-b", "question_text": "將 IEEE 754 單精確度浮點數以十六進制表示的值 C0AC0000 轉成十進制值。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "1-d", "question_text": "將十進制值 778477 轉成三十六進制值。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "2-a", "question_text": "請利用你熟悉的程式語言撰寫此遞迴函式 $C(n, k)$。", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "2-b", "question_text": "求 $C(10, 8)$ 的值。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "2-c", "question_text": "求 $C(n, k)$ 所需的時間複雜度，請以 Big-O 表示之。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "3-c", "question_text": "請問（b）中所形成的二元樹之後序走訪（Post-Order Traversal）結果為何？", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "4", "question_text": "請詳細說明如何使用 <PERSON><PERSON>'s algorithm 在圖一的權重無向圖中找尋最小花費生成樹（Minimum Cost Spanning Tree）。", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "5-a", "question_text": "什麼是 BFS（Breadth-First Search）？", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "5-b", "question_text": "請說明如何利用 BFS 在圖一的無向圖中找尋一個 BFS spanning tree，在此假設節點 A 為樹根（起始點）。", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "6", "question_text": "請寫出圖二程式執行結果?(10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "7-b", "question_text": "請簡述NFV功能或特性。(3分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "7-c", "question_text": "請簡述NAT功能或特性。(3分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "7-d", "question_text": "請簡述SDN功能或特性。(3分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立高雄科技大學（建工校區）", "department": "電子工程系碩士班（丙組）", "year": "113", "question_number": "7-e", "question_text": "請簡述TCP功能或特性。(3分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "3", "question_text": "網路協議TCP和UDP的主要區別是什麼?", "options": ["a. 連接導向 vs 連接非導向", "b. 加密 vs 非加密", "c. 速度 vs 安全性", "d. 數據傳輸量"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "4", "question_text": "哪種排序算法的平均時間複雜度是$O(n\\log n)$:", "options": ["a. 冒泡排序", "b. 插入排序", "c. 快速排序", "d. 選擇排序"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "5", "question_text": "在計算機安全領域,最近出現的「零信任」模型是什麼?", "options": ["a. 一種新的加密算法", "b. 身份驗證協議", "c. 網絡安全框架", "d. 惡意軟件類型"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "6", "question_text": "0x10℃的十進位表示為:", "options": [], "type": "fill-in-the-blank", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "7", "question_text": "哪種資料結構特別適合於實現遞迴演算法?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "8", "question_text": "下列以 python-like 程式語言撰寫的程式碼片段,總共要執行多少 statement 該行一共會被執行過幾次?\nA = 10\nwhile A >= 3:\n  statement\n  A = A - 3", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "9", "question_text": "在資料庫管理中,SQL代表?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "10", "question_text": "已知大寫字母M的ASCII碼為01001101,則大寫字母P的ASCII碼為何?", "options": [], "type": "fill-in-the-blank", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "11", "question_text": "哈希函數在計算機科學中主要用於什麼?(5分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "12", "question_text": "請解釋說明AIGC? (5分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "13", "question_text": "什麼是WebSocket? (10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "14", "question_text": "在計算機科學中,「P vs NP 問題」主要探討的是哪個領域的問題?請簡述其的意涵(10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "15", "question_text": "解釋量子計算的基本原理及其對未來計算模型的潛在影響。(10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系碩士班", "year": "113", "question_number": "16", "question_text": "請解釋說明何謂FinOps(Financial Operations)? (10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "3", "question_text": "網路協議TCP和UDP的主要區別是什麼？", "options": ["a. 連接導向 vs 連接非導向", "b. 加密 vs 非加密", "c. 速度 vs 安全性", "d. 數據傳輸量"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "4", "question_text": "哪種排序算法的平均時間複雜度是$O(n\\log n)$？", "options": ["a. 冒泡排序", "b. 插入排序", "c. 快速排序", "d. 選擇排序"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "5", "question_text": "在計算機安全領域，最近出現的「零信任」模型是什麼？", "options": ["a. 一種新的加密算法", "b. 身份驗證協議", "c. 網絡安全框架", "d. 惡意軟件類型"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "6", "question_text": "0x10C的十進位表示為:", "options": [], "type": "fill-in-the-blank", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "7", "question_text": "哪種資料結構特別適合於實現遞迴演算法?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "8", "question_text": "下列以 python-like 程式語言撰寫的程式碼片段,總共要執行多少 statement 該行一共會被執行過幾次?\n```\nA = 10\nwhile A >= 3:\n    statement\n    A = A - 3\n```", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "9", "question_text": "在資料庫管理中,SQL代表?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "10", "question_text": "已知大寫字母M的ASCII碼為01001101,則大寫字母P的ASCII碼為何?", "options": [], "type": "fill-in-the-blank", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "11", "question_text": "哈希函數在計算機科學中主要用於什麼?(5分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "12", "question_text": "請解釋說明AIGC? (5分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "13", "question_text": "什麼是WebSocket? (10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "14", "question_text": "在計算機科學中,「P vs NP 問題」主要探討的是哪個領域的問題?請簡述其的意涵(10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "15", "question_text": "解釋量子計算的基本原理及其對未來計算模型的潛在影響。(10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立高雄科技大學（第一校區）", "department": "資訊管理系電子商務碩士班", "year": "113", "question_number": "16", "question_text": "請解釋說明何謂FinOps(Financial Operations)? (10分)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "3", "question_text": "L1與L2都為真(True),請問下列哪個邏輯運算結果為真?\n(A) NOT L1 (B) L1 AND L2 (C) L1 XOR L2 (D) NOT L2", "options": ["a. NOT L1", "b. L1 AND L2", "c. L1 XOR L2", "d. NOT L2"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "6", "question_text": "SQL語法中哪一個是用來從資料庫中取得資料的指令?\n(A)GET (B)INSERT (C)SELECT (D) CREATE", "options": ["a. GET", "b. INSERT", "c. SELECT", "d. CREATE"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "7", "question_text": "請問顏色深度為256位元(bits)且解析度(resolution)為300*200的點陣圖(bitmap)需要多少位元組(bytes)來儲存?\n(A)1,920,000 (B)7,500 (C)60,000 (D)30,000", "options": ["a. 1,920,000", "b. 7,500", "c. 60,000", "d. 30,000"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "8", "question_text": "下列程式中push(n)是將資料n放入堆疊(stack);pop()是從堆疊中取出一筆資料。請問下列程式執行完後,堆疊中剩餘的資料為何?\npush(3), push(2), push(4), push(pop() + pop()), pop()\n(A)3 (B)5 (C)6 (D)4", "options": ["a. 3", "b. 5", "c. 6", "d. 4"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "9", "question_text": "下列程式中add(n)是將資料n放入佇列(queue); remove()是從佇列中取出一筆資料。請問下列程式執行完後,佇列中剩餘的資料為何?\nadd(3), add(2), add(4), add(remove() + remove()), remove()\n(A)3 (B)5 (C)6 (D)4", "options": ["a. 3", "b. 5", "c. 6", "d. 4"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "10", "question_text": "下列何者目標為確保資料不會被篡改(或篡改後可以很快被發現)?\n(A)完整性(integrity) (B) 可得性(availability) (C) 私密性(confidentiality) (D) 不可否認性(non-repudiation)", "options": ["a. 完整性(integrity)", "b. 可得性(availability)", "c. 私密性(confidentiality)", "d. 不可否認性(non-repudiation)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "1-a", "question_text": "請依據下圖回答問題 (a) (5%)寫出從節點1進行廣度優先搜尋(BFS)的搜尋順序", "options": [], "type": "short-answer", "image_file": ["私立東吳大學_資料科學系碩士班_113_1-a-1.png"], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "1-b", "question_text": "請寫出從節點1進行深度優先搜尋(DFS)的搜尋順序", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "3-1-a", "question_text": "當n為5時,請問三段程式碼最後a的輸出結果各為何？", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "3-1-b", "question_text": "三段程式碼的時間複雜度(Big-O)各為何？", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立東吳大學", "department": "資料科學系碩士班", "year": "113", "question_number": "3-2", "question_text": "假設換幣機提供1元、5元、10元與50元四種硬幣，請設計一個程式可以將使用者投入的金額N，轉換成等價的最少硬幣數量。", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1", "question_text": "How many nodes that a full binary tree of depth h can have?", "options": ["a. 2h-1", "b. 2h+1", "c. $2^{h+1}$", "d. $2^{h-1}$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "3", "question_text": "Given an empty Queue, what are the elements would be (from bottom to top) in the Queue after the following instructions?\nInsert (A) → Insert (B) → Insert (C) → Delete() → Insert (D) → Insert (A) → Insert (B) → Delete()", "options": ["a. CDAB", "b. ABBDA", "c. ABDA", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4", "question_text": "Which of the following statement is correct?", "options": ["a. Gateway connects devices and networks", "b. UDP is more reliable than TCP", "c. In Ring topology, all devices connect to each other so that it can make sure the data would be sent to the designated device even few of the the devices is not working.", "d. The OSI model includes Physical, Data link, Network, Transport, Session, Presentation, and Application layers. The HTTP is in the Application layer."], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1", "question_text": "Please describe the two types of encryption, symmetric and asymmetric key encryption, and compare the differences between them.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "2-1", "question_text": "Convert 45.1275 from decimal into binary.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "2-2", "question_text": "Convert ECB.75 from hexadecimal into decimal", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "2-3", "question_text": "Represents the decimal number 75.015625 in IEEE 754 standard single precision.(IEEE 754 consists of 1 bit of sign, 8 bits of exponent, and 23 bits of mantissa)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4", "question_text": "Please explain how the context switch works", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "5-1", "question_text": "Please draw the binary tree", "options": [], "type": "draw-answer", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "5-2", "question_text": "Write down its postorder sequence", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立東吳大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "6", "question_text": "Please write down how to represent the following expression in the postfix form and the results. The prefix expression is (E-X)/(A+M)*(S+C-U).", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立輔仁大學", "department": "應用統計研究所", "year": "113", "question_number": "1", "question_text": "若想為自己的線上商店架設一個網站,就內容建置與網頁寄存兩方面來說,有哪些方式可以達到目的?試分別說明這些方法的優缺點。(10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "私立輔仁大學", "department": "應用統計研究所", "year": "113", "question_number": "2", "question_text": "試說明資料分類與資料分群的差異,並各舉一個演算法作為例子。(10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立輔仁大學", "department": "應用統計研究所", "year": "113", "question_number": "3", "question_text": "已知有一數列{10, 11, 12, 13, 14, 15, 16, 17, 18, 19}。試說明分別以線性搜尋法(linear search)與內插搜尋法(interpolation search)搜尋15的過程,以及各需做幾次比較?(10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "私立輔仁大學", "department": "應用統計研究所", "year": "113", "question_number": "4", "question_text": "請將物件導向程式設計中的繼承(inheritance)與多型(polymorphism)的觀念各寫一小段程式說明其特徵。(10%)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "私立輔仁大學", "department": "應用統計研究所", "year": "113", "question_number": "5", "question_text": "請舉出兩種你曾使用過的資料分析軟體/工具,並說明其特色。(10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "私立輔仁大學", "department": "應用統計研究所", "year": "113", "question_number": "6", "question_text": "關連式資料庫的完整性規則(integrity rules)是什麼?試舉例並說明之。(10%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "1-a", "question_text": "A direct mapped cache", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "1-b", "question_text": "A fully associative cache", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "1-c", "question_text": "A 8-way set associative cache", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "2", "question_text": "(10 points) Find E[X] and Var[X] when the density function of X is $f(x) = \\begin{cases} 2x, & \\text{if } 0 \\le x \\le 1 \\\\ 0, & \\text{otherwise} \\end{cases}$", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "3", "question_text": "(10 points) If a die is rolled 4 times, what is the probability that 6 comes up at least one?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "4", "question_text": "(10 points) What constitutes the OSI 7-layer model? Among these layers, which ones operate in a point-to-point capacity, and which ones operate in an end-to-end capacity?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "6", "question_text": "(15 points) Convert the decimal number $(47802.6875)_{10}$ to binary, octal, and hexadecimal.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "7", "question_text": "(15 points) Please provide the hexadecimal representation for the decimal number 63.25, formatted according to the IEEE-754 single precision standard.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "8", "question_text": "(10 points) Please briefly describe the concept of \"forwarding (by passing)\" for a RISC processor.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所乙組", "year": "113", "question_number": "9", "question_text": "(10 points) Please derive the simplest SOP (Sum of Products) expression for the following Boolean function. $f(w, x, y, z) = w'xy' + w'y'z + wxy'z' + xyz'$", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所甲組", "year": "113", "question_number": "1-c", "question_text": "A 8-way set associative cache", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所甲組", "year": "113", "question_number": "2", "question_text": "(10 points) Find E[X] and Var[X] when the density function of X is $f(x) = \\begin{cases} 2x, & \\text{if } 0 \\le x \\le 1\\\\ 0, & \\text{otherwise} \\end{cases}$", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所甲組", "year": "113", "question_number": "3", "question_text": "(10 points) If a die is rolled 4 times, what is the probability that 6 comes up at least one?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所甲組", "year": "113", "question_number": "4", "question_text": "(10 points) What constitutes the OSI 7-layer model? Among these layers, which ones operate in a point-to-point capacity, and which ones operate in an end-to-end capacity?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所甲組", "year": "113", "question_number": "6", "question_text": "(15 points) Convert the decimal number (47802.6875)$_{10}$ to binary, octal, and hexadecimal.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所甲組", "year": "113", "question_number": "7", "question_text": "(15 points) Please provide the hexadecimal representation for the decimal number 63.25, formatted according to the IEEE-754 single precision standard.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所甲組", "year": "113", "question_number": "8", "question_text": "(10 points) Please briefly describe the concept of \"forwarding (by passing)\" for a RISC processor.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資訊工程研究所甲組", "year": "113", "question_number": "9", "question_text": "(10 points) Please derive the simplest SOP (Sum of Products) expression for the following Boolean function. $f(w, x, y, z) = w'xy' + w'y'z + wxy'z' + xyz'$", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "1", "question_text": "One of the latest techniques for user authentication involves something that is related to who the user is. This technique is called", "options": ["a. smartphone detection", "b. ATM card", "c. biometric sensing", "d. password reset"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "2", "question_text": "Some of the common techniques listed below help protect the computer resources from being used by unauthorized personnel. Choose the one that is incorrect.", "options": ["a. Each user has an unique login account", "b. An administrator account monitors and alters settings of regular users", "c. The user login password is taped to the monitor/screen so he/she does not forget the password", "d. Only authorized software downloads are permitted on regular user accounts"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "3", "question_text": "Which of the following would not require real-time processing?", "options": ["a. Typing a document with a word processor", "b. Navigation of an aircraft", "c. Forecasting world-wide trend for the next five-year period", "d. Maintaining a airline reservation system"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "4", "question_text": "Which of the following is assigned the task of providing individual users access to the Internet?", "options": ["a. Tier-1 ISPs", "b. Tier-2 ISPs", "c. Access ISPS", "d. I<PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "5", "question_text": "Which kind of system describes a distributed system in which many independent computers work closely together to provide computation or services comparable to a much larger machine?", "options": ["a. Cluster computing", "b. Peer-to-peer network", "c. Open network", "d. POP3"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "6", "question_text": "Most machine languages are based on the", "options": ["a. Imperative paradigm", "b. Declarative paradigm", "c. Functional paradigm", "d. Object-oriented paradigm"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "7", "question_text": "Which of the following is not a step in the process of translating a program?", "options": ["a. Executing the program", "b. Parsing the program", "c. Lexical analysis", "d. Code generation"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "8", "question_text": "Which of the following is not a parse tree of an expression based on the following grammar?", "options": ["a. ", "b. ", "c. "], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "9", "question_text": "When searching within the list\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>\nwhich of the following entries will be found most quickly using the sequential search algorithm?", "options": ["<PERSON><PERSON>", "b. <PERSON>", "c. Tom"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "10", "question_text": "In general, an algorithm in which of the following categories is considered more efficient?", "options": ["a. $\\Theta(\\log_2 n)$", "b. $\\Theta(n)$", "c. $\\Theta(n \\log_2 n)$", "d. $\\Theta(n^2)$"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "11", "question_text": "Which of the following software engineering methodologies is the most rigid?", "options": ["a. Inc<PERSON> model", "b. Waterfall model", "c. Extreme programming", "d. Evolutionary prototyping"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "14", "question_text": "Suppose you were going to retrieve items of data that you would later need to process in the opposite order from that in which they were retrieved. Which of the following would be the best structure in which to store the items?", "options": ["<PERSON><PERSON>", "b<PERSON> <PERSON>", "c<PERSON>", "d. Traditional linked list"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "15", "question_text": "If the two-dimensional array X were stored in row-major order, then in the block of main memory containing X, which of the following would be true?", "options": ["a. The entry X[1,2] would appear before X[2,1].", "b. The entry X[1,2] would appear after X[2,1].", "c. The entry X[1,2] would be in the same location as X[2,1].", "d. None of the above"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "16", "question_text": "Given the relation X below\nX: A B C\n2 5 7\n3 3 3\n4 3 2\n5 2 8\nwhat value will be extracted by the following query?\nTEMP ← SELECT from X where B > C\nRESULT ← PROJECT A from TEMP", "options": ["a. 2", "b. 3", "c. 4", "d. 5"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "17", "question_text": "Given the two relations X and Y below\nX: A B\n7 s\n2 z\nY: C D\nt 3\nr 2\nwhat value would be retrieved by executing the following SQL statement?\nSELECT Y.C FROM X, Y WHERE X.A > Y.D", "options": ["a. s", "b. z", "c. t", "d. r"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "19", "question_text": "The restricted portion of the projection plane that defines the boundaries of the final image is known as the", "options": ["a. Image window", "b. Z-buffer", "c. Center of projection", "<PERSON><PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "22", "question_text": "If the heuristic being used is the-number-of-tiles-out-of-place, which of the following eight-puzzle will be given priority for further consideration by a heuristic search?", "options": ["a. 123\n45\n786", "b. 23\n156\n478", "c. 13\n426\n758", "d. 13\n426\n758"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "23", "question_text": "Which of the following Bare Bones programs is self-terminating?", "options": ["a. while X not 0:\ndecr x", "b. while X not 0:\nwhile X not 0:", "c. decr X"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "24", "question_text": "Which of the following statements is false?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "25", "question_text": "Which of the following questions has not yet been answered by researchers?", "options": ["a. Is P contained in NP?", "b. Is NP contained in P?", "c. Are all the problems in NP solvable?", "d. Are all the problems in P solvable?"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "26", "question_text": "Which of the following is not a role of a typical operating system?", "options": ["a. Control the allocation of the machine's resources", "b. Control access to the machine", "c. Maintain records regarding files stored in mass storage", "d. Assist the computer user in the task of processing digital photographs"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "27", "question_text": "Which of the following items of information would not be contained in an operating system's process table?", "options": ["a. The location of the memory area assigned to the process", "b. The priority of each process", "c. Whether the process is ready or waiting", "d. The machine language instructions being executed by the process"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "28", "question_text": "The latest trend in distributed systems whereby huge pools of shared computers on the network can be allocated for use by clients as needed is called _______.", "options": ["a. LAN computing", "b. cloud computing", "c. tier-1 computing", "d. VO<PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "29", "question_text": "Which layer of the TCP/IP hierarchy is responsible for obtaining the correct address for a message's destination?", "options": ["a. Application", "b. Transport", "c. Network", "d<PERSON> <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "30", "question_text": "Which of the following identifies the application to which a message arriving from the Internet should be given?", "options": ["a. Protocol", "b. Port number", "c. Domain", "d. <PERSON> count"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "31", "question_text": "Which of the following is a program that enters a computer system disguised as a desirable program, such as a game or useful utility package, that is willingly imported by the victim?", "options": ["a. Vir<PERSON>", "b<PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "d. Trojan horse"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "32", "question_text": "Which of the following is ignored by a compiler?", "options": ["a. Control statements", "b. Declarations of constants", "c. Function headers", "d. Comment statements"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "33", "question_text": "Which of the following is not associated with object-oriented programming?", "options": ["a. Inheritance", "b. <PERSON>", "c. Encapsulation", "d. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "34", "question_text": "Which of the following is a loop invariant at the point at which the test for termination is performed in the following loop structure?\nX - 3\nrepeat:\nX = X + 2\nuntil (X > 5)", "options": ["a. X > 5", "b. X < 8", "c. X ≥ 5", "d. X ≤ 6"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "35", "question_text": "Which of the following is not a way of representing algorithms?", "options": ["a. Stepwise refinement", "b. <PERSON><PERSON><PERSON><PERSON>", "c. <PERSON>", "d. Programming language"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "36", "question_text": "Which of the following is not a means of repeating a block of instructions?", "options": ["a. Pretest loop", "b. Posttest loop", "c. Recursion", "d. Assignment statement"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "資管系", "year": "113", "question_number": "37", "question_text": "Which of the following is most likely an example of a one-to-one relationship?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "38", "question_text": "Which of the following is most likely an example of a many-to-many relationship?", "options": ["a. Subscribers and magazines", "b. Birth dates and people", "c. Planets and their moons", "d. Dinner guests and table settings"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "39", "question_text": "If a stack contained the entries w, x, y, z (from top to bottom), which of the following would be the contents after two entries were removed and the entry r was inserted?", "options": ["a. w, x, r", "b. y, z, r", "c. r, y, z", "d. r, w, x"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "40", "question_text": "As dynamic data structures grow and shrink, storage space is used and released. The process of reclaiming unused storage space for future use is known as ______.", "options": ["a. memory leak", "b. aggregation", "c. garbage collection", "d. abstraction"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "41", "question_text": "If the longest path in a binary tree contained exactly four nodes, what is the maximum number of nodes that could be in the entire tree?", "options": ["a. 4", "b. 7", "c. 15", "d. 31"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "42", "question_text": "Which of the following is not a potential problem caused by multiple transactions manipulating a database at the same time?", "options": ["a. Lost update problem", "b. <PERSON>lustering", "c<PERSON>", "d. <PERSON>rect summary problem"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "43", "question_text": "Which of the following features within a DBMS is not provided to maintain database integrity?", "options": ["a. Concurrent transaction processing", "b. <PERSON>g", "c. Locking protocol", "d<PERSON> points"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "44", "question_text": "Which of the following data mining techniques would be applied when trying to identify common properties between different groups of shoppers?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "45", "question_text": "Hidden-surface removal is the process of", "options": ["a. Discarding those objects that fall outside the view volume", "b. Applying a parallel projection rather than a perspective projection", "c. Identifying parts of surfaces that are in a shadow", "d. Identifying surfaces that are blocked from the camera's view"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "46", "question_text": "Motion capture is a means of applying", "options": ["a. A global lighting model", "b. A local lighting model", "c. <PERSON>", "<PERSON>. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "47", "question_text": "In an artificial neural network, which of the following pairs of input connection weights would cause a neuron with two inputs and a threshold value of 3 to produce an output of 1 only when both of its inputs are 1?", "options": ["a. 0, 0", "b. 2, 0", "c. 0, 2", "d. 2, 2"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "48", "question_text": "The assumption that a statement is false unless it can be explicitly derived from the information available is called", "options": ["a. <PERSON>-reasoning", "b. The closed-world assumption", "c. The frame problem", "d. The Turing test"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "49", "question_text": "The diagram below represents an associative memory as described in the text. What stable state will the system reach if it is initiated with the top and bottom neurons excited and the others inhibited?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "49", "question_text": "如圖所示，此神經網路中，哪一個選項最能描述神經元的激發狀態？", "options": ["a. 所有神經元都被激發", "b. 只有側面的神經元被激發", "c. 沒有神經元被激發", "d. 只有頂部和底部的 神經元被激發"], "type": "single-choice", "image_file": ["國立雲林科技大學_資訊管理研究所_113_49-1.png"], "image_regions": [{"x": 120, "y": 130, "width": 160, "height": 160}], "predicted_category": "新興技術"}, {"school": "國立雲林科技大學", "department": "資訊管理研究所", "year": "113", "question_number": "50", "question_text": "研究人員尚未確定以下哪個問題的精確時間複雜度？", "options": ["a. 排序一個列表", "b. 搜尋一個列表中的特定條目", "c. 旅行推銷員問題", "d. 列出給定委員會中所有可能的子委員會"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "1-a", "question_text": "Convert the following hexadecimal representations of 2's complement binary numbers to decimal number. xF0", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "1-b", "question_text": "Convert the following hexadecimal representations of 2's complement binary numbers to decimal number. xF77", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "1-d", "question_text": "Convert the following hexadecimal representations of 2's complement binary numbers to decimal number. x8000", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "2-a", "question_text": "Convert these decimal numbers to 8-bit 2's complement binary numbers. 102", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "2-b", "question_text": "Convert these decimal numbers to 8-bit 2's complement binary numbers. 64", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "2-c", "question_text": "Convert these decimal numbers to 8-bit 2's complement binary numbers. 33", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "2-d", "question_text": "Convert these decimal numbers to 8-bit 2's complement binary numbers. -128", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "2-e", "question_text": "Convert these decimal numbers to 8-bit 2's complement binary numbers. 127", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "4", "question_text": "A symbol of 2-to-1 mux is listed below. Please draw the gate-level circuit of 2-to-1 mux.", "options": [], "type": "draw-answer", "image_file": ["國立雲林科技大學_電子工程研究所甲組_113_4-1.png"], "image_regions": [{"x": 410, "y": 780, "width": 100, "height": 100}], "predicted_category": "程式設計"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "6", "question_text": "Show the single precision representation to the decimal number $250.125_{10}$", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "7-a", "question_text": "Assume d is between 75 and 80; what value is modular multiplicative inverse $d$?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "7-b", "question_text": "Assume that the encrypted information(ciphertext) is 3, what is the original information?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "國立雲林科技大學", "department": "電子工程研究所甲組", "year": "113", "question_number": "9", "question_text": "How does the CPU use the pipeline technique in the operating system to decode a series of instructions?", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "1", "question_text": "Perform the binary subtraction operation of the two decimal signed integers -150 and -75 using two's complement representation. Show the step-by-step process in detail, including the conversion to two's complement, the subtraction, and any necessary adjustments.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "2", "question_text": "Draw a deterministic finite automaton (DFA) diagram for the language accepting strings ending with 'abb' over input alphabets $\\Sigma = \\{a, b\\}$. Present the step-by-step process in detail.", "options": [], "type": "draw-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "3", "question_text": "Suppose the numbers 7, 5, 1, 8, 3, 6, 0, 9, 4, 2 are inserted in that order into an initially empty binary search tree. The binary search tree uses the usual ordering on natural numbers. What is the inorder traversal sequence of the resultant tree? Draw the resultant tree with your answer.", "options": [], "type": "draw-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "4", "question_text": "Insert the keys 79, 69, 98, 72, 14, 50 into the hash table of size 13. Resolve all collisions using double hashing technique. The first hash-function is $h_1(k) = k \\mod 13$, and the second hash-function is $h_2(k) = 1 + (k \\mod 11)$. Show the step-by-step process of how the keys are hashed and inserted, and indicate the resulting structure of the hash table after all insertions.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "5-a", "question_text": "Draw a graph G to represent this situation.", "options": [], "type": "draw-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "5-b", "question_text": "List the vertex set, and the edge set, using set notation. In other words, show sets V and E for the vertices and edges, respectively, in $G = \\{V, E\\}$", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "5-c", "question_text": "Draw an adjacency matrix for G.", "options": [], "type": "draw-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "7-a", "question_text": "Construct a <PERSON><PERSON><PERSON> tree for the given set of characters based on their frequencies.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "7-b", "question_text": "Determine the <PERSON><PERSON><PERSON> codes for each character in the set.", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "7-c", "question_text": "Encode the message \"bee\" using the <PERSON><PERSON><PERSON> codes obtained in part (b).", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "8", "question_text": "Consider a hypothetical microcontroller with a simple assembly language. The microcontroller has three general-purpose registers: A, B, and C. The following instructions are supported:\n• MOV A, #5: Move the immediate value 5 into register A.\n• ADD B, A: Add the content of register A to register B.\n• SUB C, B: Subtract the content of register B from register C.\n• MUL B, C: Multiply the content of register C with register B.\n• JMP 5: Jump to the instruction at address 5.\n• HLT: Halt the microcontroller.\nAssume the program counter (PC) starts at address 0. Write a simple assembly program that calculates the following expression: $(A+5) \\times (B-3)$ \nYour program should store the final result in register C and then halt.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "9-a", "question_text": "Find and print the maximum element and its index in the array.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "9-b", "question_text": "Print the array in reverse order.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣大學", "department": "生物機電系碩士班丁組", "year": "113", "question_number": "10", "question_text": "Develop a program in a programming language of your choice to determine and display the prime factorization of a given positive integer. Prime factorization involves breaking down a number into its individual prime factors. Create a computer program that prompts the user to input a positive integer and then outputs its prime factorization. Make sure your program incorporates suitable loops and functions, and includes comments to elucidate the logic of your code.", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "1", "question_text": "Is the following statement true (T) or false (F)? \"5G is the next generation of radio systems and network architecture that delivers extreme broadband, ultra-robust, low latency connectivity, and massive networking for the Internet of Things.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "2", "question_text": "Is the following statement true (T) or false (F)? \"GPU has become one of the most important types of computing technology, known for graphics and widely used in gaming or nowadays training Machine Learning models. It is designed for parallel processing.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "3", "question_text": "Is the following statement true (T) or false (F)? \"JPEG is a commonly used method of lossless compression for digital images.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "4", "question_text": "Is the following statement true (T) or false (F)? \"The seven layers of OSI from lowest-level to highest-level are the Physical Layer, the Transport Layer, the Data Link Layer, the Network Layer, the Session Layer, the Presentation Layer, and the Application Layer.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "5", "question_text": "\"Deep learning is a subset of Machine Learning and Machine Learning is a subset of artificial intelligence that uses algorithms to learn patterns from data.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "6", "question_text": "Simply the boolean algebra equation. Which of the following expression is equivalent to $CD1 + D(CAA + BB)$? (A) $D + B + A$; (B) $D + B$; (C) $CD + DB$.", "options": ["a. D+B+A", "b. D+B", "c. CD+DB"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "7", "question_text": "We are going to send the string \"successes\" over a network using <PERSON><PERSON><PERSON> coding. So we first compute the character frequencies, (s: 4, u: 1, c: 2, e: 2), and then derive the <PERSON><PERSON><PERSON> code. How many bits do we need to send the string? (A) 17 bits; (B) 20 bits; (C) 32 bits.", "options": ["a. 17 bits", "b. 20 bits", "c. 32 bits"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "8", "question_text": "What is the output of the code about the stack shown on the right?", "options": ["a. x=6\ny=11", "b. x=11\ny=6"], "type": "single-choice", "image_file": ["國立臺灣大學_地質科學系及海洋研究所聯合招生_113_8-1.png"], "image_regions": [{"x": 567, "y": 376, "width": 167, "height": 167}], "predicted_category": "程式設計"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "9", "question_text": "What is the necessary condition for a deadlock situation? (A) Mutual exclusion; (B) No preemption; (C) Hold and wait; (D) Circular set; (E) All the above.", "options": ["a. Mutual exclusion", "b. No preemption", "c. Hold and wait", "d. Circular set", "e. All the above"], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "10", "question_text": "Convert the decimal number 32 into binary representation. (A) 00100001; (B) 00100000; (C) 01000000.", "options": ["a. 00100001", "b. 00100000", "c. 01000000"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "11", "question_text": "Choose the recursive formula for the <PERSON><PERSON><PERSON><PERSON> series. ($n \\ge 1$)(A) $F(n) = F(n+1) + F(n+2)$; (B) $F(n) = F(n) + F(n+1)$; (C) $F(n) = F(n-1) + F(n-2)$; (D) $F(n) = F(n-1) - F(n-2)$.", "options": ["a. F(n) = F(n+1) + F(n+2)", "b. F(n) = F(n) + F(n+1)", "c. F(n) = F(n-1) + F(n-2)", "d. F(n) = F(n-1) - F(n-2)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "12", "question_text": "What is the minimum cost spanning tree for the graph on the right? (A) 28; (B) 11; (C) 13.", "options": ["a. 28", "b. 11", "c. 13"], "type": "single-choice", "image_file": ["國立臺灣大學_地質科學系及海洋研究所聯合招生_113_12-1.png"], "image_regions": [{"x": 648, "y": 533, "width": 115, "height": 112}], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "13", "question_text": "Here is a binary tree as shown on the right. What is the postorder traversal of this binary tree? (A) 0136742689; (B) 6371402859; (C) 6734189520.", "options": ["a. 0136742689", "b. 6371402859", "c. 6734189520"], "type": "single-choice", "image_file": ["國立臺灣大學_地質科學系及海洋研究所聯合招生_113_13-1.png"], "image_regions": [{"x": 648, "y": 664, "width": 115, "height": 112}], "predicted_category": "資料結構"}, {"school": "國立臺灣大學", "department": "地質科學系及海洋研究所聯合招生", "year": "113", "question_number": "14", "question_text": "What is the inorder traversal of the same binary tree shown above? (A) 0136742689; (B) 6371402859; (C) 6734189520.", "options": ["a. 0136742689", "b. 6371402859", "c. 6734189520"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料結構"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "1", "question_text": "Is the following statement true (T) or false (F)? \"5G is the next generation of radio systems and network architecture that delivers extreme broadband, ultra-robust, low latency connectivity, and massive networking for the Internet of Things.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "2", "question_text": "Is the following statement true (T) or false (F)? \"GPU has become one of the most important types of computing technology, known for graphics and widely used in gaming or nowadays training Machine Learning models. It is designed for parallel processing.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "3", "question_text": "Is the following statement true (T) or false (F)? \"JPEG is a commonly used method of lossless compression for digital images.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "4", "question_text": "Is the following statement true (T) or false (F)? \"The seven layers of OSI from lowest-level to highest-level are the Physical Layer, the Transport Layer, the Data Link Layer, the Network Layer, the Session Layer, the Presentation Layer, and the Application Layer.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "5", "question_text": "\"Deep learning is a subset of Machine Learning and Machine Learning is a subset of artificial intelligence that uses algorithms to learn patterns from data.\"", "options": [], "type": "true-false", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "6", "question_text": "Simply the boolean algebra equation. Which of the following expression is equivalent to $CD1 + D(CAA + BB)$?\n(A) $D + B + A$\n(B) $D + B$\n(C) $CD + DB$", "options": ["a. D+B+A", "b. D+B", "c. CD+DB"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "7", "question_text": "We are going to send the string \"successes\" over a network using <PERSON><PERSON><PERSON> coding. So we first compute the character frequencies, (s: 4, u: 1, c: 2, e: 2), and then derive the <PERSON><PERSON><PERSON> code. How many bits do we need to send the string?\n(A) 17 bits; (B) 20 bits; (C) 32 bits.", "options": ["a. 17 bits", "b. 20 bits", "c. 32 bits"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "8", "question_text": "What is the output of the code about the stack shown on the right?\n(A) x=6\ny=11\n(B) x=11\ny=6", "options": ["a. x=6, y=11", "b. x=11, y=6"], "type": "single-choice", "image_file": ["國立臺灣大學_生醫電子與資訊學研究所丙組_113_8-1.png"], "image_regions": [{"x": 560, "y": 380, "width": 200, "height": 180}], "predicted_category": "資料結構"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "9", "question_text": "What is the necessary condition for a deadlock situation?\n(A) Mutual exclusion; (B) No preemption; (C) Hold and wait; (D) Circular set; (E) All the above.", "options": ["a. Mutual exclusion", "b. No preemption", "c. Hold and wait", "d. Circular set", "e. All the above"], "type": "multiple-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "10", "question_text": "Convert the decimal number 32 into binary representation.\n(A) 00100001; (B) 00100000; (C) 01000000.", "options": ["a. 00100001", "b. 00100000", "c. 01000000"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "數學計算"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "11", "question_text": "Choose the recursive formula for the <PERSON><PERSON><PERSON><PERSON> series. (n>=1)\n(A) $F(n) = F(n+1) + F(n+2)$; (B) $F(n) = F(n) + F(n+1)$; (C) $F(n) = F(n-1) + F(n-2)$; (D) $F(n) = F(n-1) - F(n-2)$.", "options": ["a. F(n) = F(n+1) + F(n+2)", "b. F(n) = F(n) + F(n+1)", "c. F(n) = F(n-1) + F(n-2)", "d. F(n) = F(n-1) - F(n-2)"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "12", "question_text": "What is the minimum cost spanning tree for the graph on the right?\n(A) 28; (B) 11; (C) 13.", "options": ["a. 28", "b. 11", "c. 13"], "type": "single-choice", "image_file": ["國立臺灣大學_生醫電子與資訊學研究所丙組_113_12-1.png"], "image_regions": [{"x": 620, "y": 520, "width": 150, "height": 120}], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "生醫電子與資訊學研究所丙組", "year": "113", "question_number": "13", "question_text": "Here is a binary tree as shown on the right. What is the postorder traversal of this binary tree?\n(A) 0136742689; (B) 6371402859; (C) 6734189520.", "options": ["a. 0136742689", "b. 6371402859", "c. 6734189520"], "type": "single-choice", "image_file": ["國立臺灣大學_生醫電子與資訊學研究所丙組_113_13-1.png"], "image_regions": [{"x": 620, "y": 660, "width": 150, "height": 120}], "predicted_category": "資料結構"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "1", "question_text": "Among the options listed, which component is not considered part of the IT infrastructure ecosystem within firms? (A) Internet platforms (B) Operating system platforms (C) Enterprise software applications (D) Virtual reality systems", "options": ["a. Internet platforms", "b. Operating system platforms", "c. Enterprise software applications", "d. Virtual reality systems"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "2", "question_text": "What category of Database Management Systems (DBMS) technology organizes data into two-dimensional tables? (A) mobile DBMS (B) relational DBMS (C) hierarchical DBMS (D) object-oriented DBMS", "options": ["a. mobile DBMS", "b. relational DBMS", "c. hierarchical DBMS", "d. object-oriented DBMS"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4", "question_text": "What technology automatically retrieves content from websites and delivers it to users' computers? (A) FTP (B) RSS (C) HTTP (D) Bluetooth", "options": ["a. FTP", "b. <PERSON><PERSON>", "c. HTTP", "<PERSON>. <PERSON>"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "5", "question_text": "An attack that involves utilizing multiple computers to inundate and overwhelm the network from various launch points is known as (A) Distributed Denial-of-Service (DDoS) (B) Denial-of-Service (DoS) (C) SQL injection (D) phishing (E) botnet", "options": ["a. Distributed Denial-of-Service (DDoS)", "b. Denial-of-Service (DoS)", "c. SQL injection", "d. phishing", "e. botnet"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "資訊安全"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "7", "question_text": "In relation to initiatives addressing sustainable development, which statement is incorrect? (A) Greenwashing (漂綠) is an activity worthy of encouragement. (B) The United Nations introduced 17 Sustainable Development Goals (SDGs) in 2015. (C) The phased implementation of the Carbon Border Adjustment Mechanism (CBAM) by the European Union began in October 2023. (D) The United States introduced the draft of the Clean Competition Act (CCA) in 2022.", "options": ["a. Greenwashing (漂綠) is an activity worthy of encouragement.", "b. The United Nations introduced 17 Sustainable Development Goals (SDGs) in 2015.", "c. The phased implementation of the Carbon Border Adjustment Mechanism (CBAM) by the European Union began in October 2023.", "d. The United States introduced the draft of the Clean Competition Act (CCA) in 2022."], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "9", "question_text": "Which of the following statements about 5G is true? (A) 5G will be capable of transmitting data in the gigabit range. (B) 5G will transmit data in the megabyte range. (C) 5G is constructed on the foundation of 4G networks. (D) 5G will have longer transmission delays.", "options": ["a. 5G will be capable of transmitting data in the gigabit range.", "b. 5G will transmit data in the megabyte range.", "c. 5G is constructed on the foundation of 4G networks.", "d. 5G will have longer transmission delays."], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "10", "question_text": "What output does the following Python code produce? (A) 9.650 (B) 9.65 (C) 9.66 (D) 9.7\n```python\nA = 9.65\nprint (\"%.3f\" % (A))\n```", "options": ["a. 9.650", "b. 9.65", "c. 9.66", "d. 9.7"], "type": "single-choice", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "2", "question_text": "請簡單說明深度學習(Deep Learning)和類神經網路(Artificial Neural Network),並比較兩者之間的關係。", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "新興技術"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "3", "question_text": "請依據下表「程式需求」,自「Python程式」找出至少2個錯誤並改寫。", "options": [], "type": "coding-answer", "image_file": ["私立輔仁大學_資訊管理學系碩士班_113_3-1.png"], "image_regions": [{"x": 180, "y": 310, "width": 230, "height": 140}, {"x": 320, "y": 310, "width": 180, "height": 140}], "predicted_category": "程式設計"}, {"school": "私立輔仁大學", "department": "資訊管理學系碩士班", "year": "113", "question_number": "4", "question_text": "隨著網路技術和AI (Artificial Intelligence) 快速發展,大幅度減少資訊搜尋成本,使我們能夠非常容易地獲取大量資訊,但這也帶來了新的挑戰和機會,即如何有效地管理、評估,甚至是操弄這些巨量資訊。資訊科技可以突破時間(流通速度)和空間侷限(全球化),讓過去不可能的做到的事變為可能,也能讓一個產業或企業,瞬間從優勢變成劣勢。\n對於即將成為資訊管理學系的研究生,您認為資訊管理領域應該朝著什麼方向發展?請嘗試提出一個可能的研究議題和預期效益。", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "1", "question_text": "若有一數字未重複之正整數數列，整數個數為n，試問若以選擇排序法進行此數列的排序，請問其時間複雜度為多少？請解釋與證明之。(15%)", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "演算法"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "2", "question_text": "請問TCP協定中的Fast Retransmission之用途為何？有何優點？請說明其運作原理為何？(15%)", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "3-a", "question_text": "IPv4網段 ***********/16 所代表之意義為何？(4%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "3-b", "question_text": "其主要用途通常為何？(4%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "作業系統"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "3-c", "question_text": "倘若欲將此網段切割成約60個子網段，請問應該如何進行？(4%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "3-d", "question_text": "切割後之子網路的網路遮罩為多少(請以十進位表示)？(4%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "3-e", "question_text": "每個子網路可以使用的IP數量為多少？(4%)", "options": [], "type": "short-answer", "image_file": [], "image_regions": [], "predicted_category": "網路技術"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "4", "question_text": "請說明何為編譯式程式語言，何為直譯式程式語言，兩者有何差異？試問JavaScript屬於何種？此程式語言的常見應用場域為何？與其搭配運行之應用程式通常為何？(10%)", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "5", "question_text": "撰寫程式時會常使用到浮點數，請說明為何單倍精準數之浮點數表示法(IEEE 754)需要進行過剩127(Excess 127)處理？以及為何要採用IEEE 754標準？(15%)", "options": [], "type": "long-answer", "image_file": [], "image_regions": [], "predicted_category": "程式設計"}, {"school": "國立臺灣大學", "department": "圖書資訊系碩士班乙組", "year": "113", "question_number": "6", "question_text": "於關聯式資料庫中，若有一個employee關聯，其中欄位有ID, Name, Department, PhoneNumber, Salary。請撰寫單一個SQL查詢指令，查詢結果為輸出三個欄位，分別為每一位員工的姓名、此員工薪水與平均員工薪水的差異、以及此員工薪水與最高員工薪水的差異，差異請皆以正值的方式呈現，並請依據員工的ID進行排序。(15%)", "options": [], "type": "coding-answer", "image_file": [], "image_regions": [], "predicted_category": "資料庫"}]