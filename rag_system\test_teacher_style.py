#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試真正老師風格的教學系統
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from context_tutor import ContextTutor

def test_natural_teaching():
    """測試自然教學風格"""
    print("🧪 測試真正老師風格的教學")
    print("="*60)
    
    tutor = ContextTutor()
    
    # 開始新問題
    print("🤔 學生問題: 什麼是銀行家演算法？")
    response1 = tutor.start_new_question("什麼是銀行家演算法？")
    print(f"🎓 老師回應:\n{response1}")
    print("\n" + "="*40)
    
    # 學生回答1
    print("💭 學生回答: 死鎖指系統陷入僵局")
    response2 = tutor.continue_conversation("死鎖指系統陷入僵局")
    print(f"🎓 老師回應:\n{response2}")
    print("\n" + "="*40)
    
    # 學生回答2
    print("💭 學生回答: 利用資源分配圖確保不會有迴圈情況發生")
    response3 = tutor.continue_conversation("利用資源分配圖確保不會有迴圈情況發生")
    print(f"🎓 老師回應:\n{response3}")
    print("\n" + "="*40)
    
    # 學生回答3
    print("💭 學生回答: 先給p1執行讓p2等待，等到p1釋放資源執行完後才換p2")
    response4 = tutor.continue_conversation("先給p1執行讓p2等待，等到p1釋放資源執行完後才換p2")
    print(f"🎓 老師回應:\n{response4}")
    print("\n" + "="*40)
    
    # 學生回答4
    print("💭 學生回答: 不太懂安全序列是什麼")
    response5 = tutor.continue_conversation("不太懂安全序列是什麼")
    print(f"🎓 老師回應:\n{response5}")

def test_different_topics():
    """測試不同主題的教學風格"""
    print("\n🧪 測試不同主題的教學風格")
    print("="*60)
    
    topics = [
        "什麼是死鎖？",
        "虛擬記憶體的作用是什麼？",
        "進程和線程的區別？"
    ]
    
    for topic in topics:
        print(f"\n📚 主題: {topic}")
        print("-" * 30)
        
        tutor = ContextTutor()
        response = tutor.start_new_question(topic)
        print(f"🎓 老師回應:\n{response[:200]}...")

def analyze_teaching_quality():
    """分析教學品質"""
    print("\n🔍 教學品質分析")
    print("="*60)
    
    tutor = ContextTutor()
    
    # 測試對話
    tutor.start_new_question("什麼是銀行家演算法？")
    response = tutor.continue_conversation("死鎖指系統陷入僵局")
    
    print("📊 品質檢查:")
    
    # 檢查是否有格式化標題
    has_formatting = "**" in response or "###" in response
    print(f"  無格式化標題: {'✅' if not has_formatting else '❌'}")
    
    # 檢查語氣自然度
    natural_words = ["很好", "對的", "不錯", "讓我們", "你覺得", "想想看"]
    is_natural = any(word in response for word in natural_words)
    print(f"  語氣自然: {'✅' if is_natural else '❌'}")
    
    # 檢查是否記住原始問題
    remembers_topic = "銀行家演算法" in response or "銀行家" in response
    print(f"  記住主題: {'✅' if remembers_topic else '❌'}")
    
    # 檢查回應長度
    is_concise = 50 < len(response) < 300
    print(f"  長度適中: {'✅' if is_concise else '❌'} ({len(response)} 字)")
    
    print(f"\n📝 回應內容:\n{response}")

def main():
    """主測試函數"""
    print("🎓 真正老師風格教學系統測試")
    print("="*70)
    print("目標：測試系統是否像真正的老師在身邊輔導")
    
    try:
        # 測試自然教學風格
        test_natural_teaching()
        
        # 測試不同主題
        test_different_topics()
        
        # 分析教學品質
        analyze_teaching_quality()
        
        print("\n🎉 測試完成！")
        print("\n💡 期待效果:")
        print("1. 🗣️ 語氣自然親切，像真正的老師")
        print("2. 📝 簡短重新說明重點，確保理解")
        print("3. 💡 補充學生可能遺漏的概念")
        print("4. 🎯 始終圍繞原始問題進行教學")
        print("5. 🔄 每次回應都推進學生理解")
        
        print("\n🚀 如果效果良好，可以運行: python rag_main.py")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
