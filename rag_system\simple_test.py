#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化測試 - 快速驗證防重複功能
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from context_tutor import ContextTutor

def quick_test():
    """快速測試"""
    print("🧪 快速測試防重複功能")
    print("="*50)
    
    tutor = ContextTutor()
    
    # 第一輪
    print("🤔 學生問: 什麼是銀行家演算法？")
    r1 = tutor.start_new_question("什麼是銀行家演算法？")
    print(f"🎓 回應1: {r1[:80]}...")
    
    # 第二輪
    print("\n💭 學生答: 死鎖指系統陷入僵局")
    r2 = tutor.continue_conversation("死鎖指系統陷入僵局")
    print(f"🎓 回應2: {r2[:80]}...")
    
    # 第三輪
    print("\n💭 學生答: 利用資源分配圖")
    r3 = tutor.continue_conversation("利用資源分配圖")
    print(f"🎓 回應3: {r3[:80]}...")
    
    # 檢查重複
    if r2[:50] == r3[:50]:
        print("\n❌ 發現重複回應")
    else:
        print("\n✅ 沒有重複回應")
    
    print(f"\n📊 上下文: {len(tutor.context)} 字符")
    print(f"📊 原始問題: {tutor.original_question}")

if __name__ == "__main__":
    quick_test()
