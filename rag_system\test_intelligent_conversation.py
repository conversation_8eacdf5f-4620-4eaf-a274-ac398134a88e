#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試智能對話教學系統
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from intelligent_tutor import IntelligentTutor

def test_translation():
    """測試中英文翻譯功能"""
    print("🧪 測試中英文翻譯功能")
    print("="*50)
    
    tutor = IntelligentTutor()
    
    test_questions = [
        "什麼是作業系統？",
        "銀行家演算法如何工作？",
        "虛擬記憶體的優點是什麼？",
        "死鎖的四個必要條件",
        "進程和線程的區別"
    ]
    
    for question in test_questions:
        english = tutor.translate_to_english(question)
        print(f"中文: {question}")
        print(f"英文: {english}")
        print("-" * 30)

def test_question_analysis():
    """測試問題分析功能"""
    print("\n🧪 測試問題智能分析")
    print("="*50)
    
    tutor = IntelligentTutor()
    
    test_questions = [
        "什麼是進程？",  # 應該是直接回答
        "為什麼需要虛擬記憶體？",  # 應該是引導式
        "如何解決死鎖問題？",  # 應該是引導式
        "CPU是什麼？"  # 應該是直接回答
    ]
    
    for question in test_questions:
        print(f"\n🔍 問題: {question}")
        analysis = tutor.analyze_question_intelligence(question)
        print(f"  教學方式: {analysis['teaching_approach']}")
        print(f"  複雜度: {analysis['complexity_level']}")
        print(f"  需要引導: {analysis['needs_guidance']}")

def test_bilingual_search():
    """測試雙語搜索功能"""
    print("\n🧪 測試雙語搜索功能")
    print("="*50)
    
    tutor = IntelligentTutor()
    
    chinese_question = "什麼是作業系統？"
    print(f"中文問題: {chinese_question}")
    
    search_results = tutor.search_knowledge_bilingual(chinese_question)
    print(f"搜索結果: {len(search_results)} 個")
    
    if search_results:
        for i, result in enumerate(search_results[:3]):
            content = result.get('content', '')[:100]
            similarity = result.get('similarity', 0)
            print(f"  結果 {i+1}: 相似度={similarity:.3f}")
            print(f"    內容: {content}...")

def test_teaching_responses():
    """測試教學回答生成"""
    print("\n🧪 測試教學回答生成")
    print("="*50)
    
    tutor = IntelligentTutor()
    
    # 測試直接回答
    print("📝 測試直接回答模式:")
    question1 = "什麼是CPU？"
    search_results1 = tutor.search_knowledge_bilingual(question1)
    direct_answer = tutor.generate_direct_answer(question1, search_results1)
    print(f"問題: {question1}")
    print(f"回答: {direct_answer[:200]}...")
    
    print("\n" + "="*30)
    
    # 測試引導式回答
    print("🎯 測試引導式回答模式:")
    question2 = "為什麼需要虛擬記憶體？"
    search_results2 = tutor.search_knowledge_bilingual(question2)
    guided_answer = tutor.generate_guided_response(question2, search_results2)
    print(f"問題: {question2}")
    print(f"回答: {guided_answer[:300]}...")

def test_full_conversation():
    """測試完整對話流程"""
    print("\n🧪 測試完整對話流程")
    print("="*50)
    
    tutor = IntelligentTutor()
    
    # 模擬學生問問題
    question = "什麼是銀行家演算法？"
    print(f"學生問題: {question}")
    
    # 系統處理問題
    response = tutor.handle_question(question)
    print(f"系統回答: {response[:200]}...")
    
    # 如果是引導模式，模擬學生回答
    if tutor.teaching_mode == 'guided':
        print(f"\n當前教學模式: {tutor.teaching_mode}")
        
        # 模擬學生回答
        student_response = "我覺得銀行家演算法是用來避免死鎖的"
        print(f"學生回答: {student_response}")
        
        # 系統評估和回應
        follow_up = tutor.continue_guidance(student_response)
        print(f"系統回應: {follow_up[:200]}...")
        print(f"理解程度: {tutor.student_understanding}%")

def main():
    """主測試函數"""
    print("🎯 智能對話教學系統測試")
    print("="*70)
    
    try:
        # 測試翻譯功能
        test_translation()
        
        # 測試問題分析
        test_question_analysis()
        
        # 測試雙語搜索
        test_bilingual_search()
        
        # 測試教學回答
        test_teaching_responses()
        
        # 測試完整對話
        test_full_conversation()
        
        print("\n🎉 所有測試完成！")
        print("\n💡 系統功能說明:")
        print("1. 🌐 中文問題自動翻譯成英文搜索")
        print("2. 🧠 AI智能判斷教學方式")
        print("3. 🎯 自適應引導式教學")
        print("4. 📊 動態理解程度評估")
        print("5. 🔄 互動式學習進度控制")
        
        print("\n🚀 現在可以運行: python interactive_learning.py")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
