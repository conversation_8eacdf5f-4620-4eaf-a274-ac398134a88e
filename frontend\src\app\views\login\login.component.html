<div class="bg-light dark:bg-transparent min-vh-100 d-flex flex-column">
  <header class="navbar navbar-light shadow-sm custom-2">
    <div class="container">
      <a class="navbar-brand d-flex align-items-center" href="dashboard">
        <img src="assets/logo.png" alt="Your Logo" class="sidebar-brand-full mx-1" height="32">
        <span class="fs-4" style="color:white">資管系研究所學習網站</span>
      </a>
    </div>
  </header>
  <div class="flex-grow-1 d-flex flex-row align-items-center">
    <c-container breakpoint="md">
      <c-row class="justify-content-center">
        <c-col lg="10" xl="8">
          <div class="taichi-container">
            <div class="taichi-slider" id="taichiSlider">
              <div class="taichi-panel login-panel">
                <c-card class="p-4 shadow-sm custom-card">
                  <c-card-body class="text-center">
                    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                      <strong class="text-primary mb-3">登入</strong>
                      <p class="text-body-secondary mb-4">登入您的帳號</p>
      
                      <div *ngIf="errorMessage" class="alert alert-danger">
                        {{ errorMessage }}
                      </div>
                      <c-input-group class="mb-4 justify-content-center">
                        <span cInputGroupText>帳號</span>
                        <input autoComplete="email" formControlName="email" placeholder="Email" class="form-control rounded-pill" />
                      </c-input-group>

                      <c-input-group class="mb-4 justify-content-center">
                        <span cInputGroupText>密碼</span>
                        <input autoComplete="password" formControlName="password" placeholder="Password" type="password" class="form-control rounded-pill" />
                      </c-input-group>
                      <c-row>
                        <c-col xs="6">
                          <button cButton class="btn custom-2 rounded-pill px-4" type="submit">
                            登入
                          </button>
                        </c-col>
                        <c-col class="text-right" xs="6">
                          <button cButton color="link" routerLink="/forget-pwd">
                            忘記密碼？
                          </button>
                        </c-col>
                      </c-row>
                    </form>
                  </c-card-body>
                </c-card>
                <div class="taichi-toggle-btn">
                  <button class="btn custom-3 rounded-pill px-4 py-2" id="showRegisterBtn">
                    沒有帳號? 註冊帳號
                  </button>
                </div>
              </div>
              <div class="taichi-panel register-panel">
                <c-card class="p-4 shadow-sm custom-card">
                  <c-card-body class="text-center">
                    <form [formGroup]="registerForm" (ngSubmit)="onRegister()">
                      <strong class="text-primary mb-3">註冊帳號</strong>
                      <p class="text-body-secondary mb-4">創建您的新帳號</p>
      
                      <div *ngIf="regErrorMessage" class="alert alert-danger">
                        {{ regErrorMessage }}
                      </div>
                      <c-input-group class="mb-3 justify-content-center">
                        <span cInputGroupText>
                          <svg cIcon name="cilUser"></svg>
                        </span>
                        <input formControlName="name" placeholder="姓名" class="form-control rounded-pill" />
                      </c-input-group>
                      <c-input-group class="mb-3 justify-content-center">
                        <span cInputGroupText>&#64;</span>
                        <input formControlName="email" placeholder="Email" class="form-control rounded-pill" />
                      </c-input-group>
                      <c-input-group class="mb-3 justify-content-center">
                        <span cInputGroupText>
                          <svg cIcon name="cilLockLocked"></svg>
                        </span>
                        <input formControlName="password" placeholder="密碼" type="password" class="form-control rounded-pill" />
                      </c-input-group>
                      <c-input-group class="mb-4 justify-content-center">
                        <span cInputGroupText>
                          <svg cIcon name="cilLockLocked"></svg>
                        </span>
                        <input formControlName="confirmPassword" placeholder="確認密碼" type="password" class="form-control rounded-pill" />
                      </c-input-group>
                      <button cButton class="btn custom-3 rounded-pill px-4" type="submit">
                        立即註冊
                      </button>
                    </form>
                  </c-card-body>
                </c-card>
                <div class="taichi-toggle-btn">
                  <button class="btn custom-2 rounded-pill px-4 py-2" id="showLoginBtn">
                    已有帳號? 我要登入
                  </button>
                </div>
              </div>
            </div>
          </div>
        </c-col>
      </c-row>
    </c-container>
  </div>
</div>