from ultralytics import YOLO
import cv2
import os
import re

# 使用絕對路徑載入模型
model = YOLO(r'D:\GitHub\MIS_teach_all\yolo\runs\test_dataset63\weights\best.pt')

# 絕對路徑圖片（請改為自己的路徑）
image_paths = [
    '../ocr/exam_img/101-計算機概論-私立健行科技大學-電子工程研究所_page_2.png',
    '../ocr/exam_img/108-計算機概論-國立臺北商業大學-資訊與決策科學研究所碩士班_page_3.png',
    '../ocr/exam_img/113-計算機概論-國立臺北科技大學-資訊工程研究所_page_3.png',
    "../ocr/exam_img/101-計算機概論-私立健行科技大學-電子工程研究所_page_2.png",
    "../ocr/exam_img/102-計算機概論-私立中原大學-工業與系統工程學系碩士班甲組_page_4.png",
    "../ocr/exam_img/102-計算機概論-私立中原大學-工業與系統工程學系碩士班甲組_page_3.png",
    "../ocr/exam_img/102-計算機概論-私立中原大學-電子工程學系碩士班晶片設計組_page_3.png",
    "../ocr/exam_img/102-計算機概論-國立中央大學-資訊管理學系碩士班乙組_page_4.png",
    "../ocr/exam_img/102-計算機概論-國立成功大學-資訊管理研究所乙組_page_4.png",
    "../ocr/exam_img/103-計算機概論-國立中央大學-資訊管理學系碩士班甲組_page_2.png"

]

# 預測
results = model(image_paths)

# 確保輸出資料夾存在
output_dir = 'test_output'
os.makedirs(output_dir, exist_ok=True)

# 處理每張圖片
for idx, image_path in enumerate(image_paths):
    # 獲取預測結果
    predictions = results[idx].cpu().numpy()
    boxes = predictions.boxes.xyxy.astype(int)
    classes = predictions.boxes.cls.astype(int)
    names = model.names

    # 讀取原始圖片
    image = cv2.imread(image_path)

    # 設定縮放比例 (例如縮小到原始尺寸的 50%)
    scale_percent = 50
    width = int(image.shape[1] * scale_percent / 100)
    height = int(image.shape[0] * scale_percent / 100)
    dim = (width, height)

    # 調整圖片大小
    resized_image = cv2.resize(image, dim, interpolation=cv2.INTER_AREA)

    # 在縮小後的圖片上繪製不帶信賴度分數的邊框和標籤
    for i in range(len(boxes)):
        x1, y1, x2, y2 = boxes[i]
        # 需要根據縮放比例調整邊框座標
        resized_x1 = int(x1 * scale_percent / 100)
        resized_y1 = int(y1 * scale_percent / 100)
        resized_x2 = int(x2 * scale_percent / 100)
        resized_y2 = int(y2 * scale_percent / 100)

        class_id = classes[i]
        class_name = names[class_id]
        label = class_name
        color = (0, 255, 0)  # 框的顏色 (例如綠色)
        cv2.rectangle(resized_image, (resized_x1, resized_y1), (resized_x2, resized_y2), color, 2)
        cv2.putText(resized_image, label, (resized_x1, resized_y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

    # 根據不同路徑格式產生不同的輸出檔名
    if "exam_img" in image_path:
        # 例如: "../ocr_and_testcode/exam_img/103-計算機概論-國立中央大學-資訊管理學系碩士班甲組_page_2.png"
        # 輸出: "103-計算機概論-國立中央大學-資訊管理學系碩士班甲組_page_2_test.png"
        base_name = os.path.basename(image_path)
        output_filename = base_name.replace(".png", "_test.png")
    else:
        # 例如: "../ocr_and_testcode/data/111/111-計算機概論-國立政治大學-資訊管理學系碩士班資管組/original_pages/原始頁面_1.png"
        # 輸出: "111-計算機概論-國立政治大學-資訊管理學系碩士班資管組.png"
        parts = image_path.split('/')
        for part in parts:
            if re.match(r'\d+-', part):  # 找到符合格式的部分 (以數字開頭後接連字符)
                output_filename = f"{part}.png"
                break
        else:
            # 如果沒找到符合格式的部分，使用原始檔名
            output_filename = os.path.basename(image_path)

    output_path = os.path.join(output_dir, output_filename)

    # 儲存調整大小後的圖片
    cv2.imwrite(output_path, resized_image)
    print(f"Saved: {output_path}")