#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能蘇格拉底式教學系統
解決重複性、引導深度、評估準確性問題
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import requests
import json
import re
from typing import Dict, List, Any, Optional
from rag_ai_responder import AIResponder
import config

class SmartTutor:
    """智能蘇格拉底式教師"""
    
    def __init__(self):
        """初始化智能教師"""
        self.ai_responder = AIResponder(language='chinese')
        self.ai_base_url = config.AI_CONFIG['base_url']
        self.ai_model = config.AI_CONFIG['model']
        
        # 對話狀態
        self.current_topic = None
        self.student_understanding_level = 0  # 0-5 理解層次
        self.conversation_context = []
        self.topic_knowledge = ""
        self.covered_points = set()  # 已覆蓋的知識點
        
        print("🎓 智能蘇格拉底式教學系統已啟動")
        print("💡 深度引導，避免重複，精準評估")
    
    def analyze_student_response(self, response: str) -> Dict[str, Any]:
        """AI深度分析學生回答"""
        prompt = f"""你是專業的教育評估專家。請分析學生的回答，並決定最佳的教學策略。

當前主題：{self.current_topic}
學生回答：{response}
已討論過的要點：{list(self.covered_points)}

請分析學生的回答並提供教學建議，用JSON格式回答：
{{
    "understanding_level": "1-5的數字，評估學生理解程度",
    "response_analysis": "對學生回答的具體分析",
    "student_strengths": "學生回答中的正確或好的部分",
    "areas_to_improve": "需要改進或澄清的地方",
    "next_teaching_focus": "下一步應該重點教學的內容",
    "best_teaching_approach": "最適合的教學方式描述",
    "suggested_response": "建議的老師回應方式"
}}

請完全基於學生的具體回答來分析，不要使用預設的分類。只回答JSON。"""

        try:
            response_obj = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.2, "num_predict": 300}
                },
                timeout=25
            )
            
            if response_obj.status_code == 200:
                ai_response = response_obj.json().get('response', '')
                json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
        except:
            pass
        
        # 備用分析
        return {
            "understanding_level": 2,
            "response_analysis": "需要進一步了解學生的想法",
            "student_strengths": "願意參與對話",
            "areas_to_improve": "需要更深入的理解",
            "next_teaching_focus": "繼續當前概念的探討",
            "best_teaching_approach": "用引導問題幫助思考",
            "suggested_response": "繼續引導學生思考"
        }
    
    def generate_smart_response(self, student_answer: str = None, is_new_question: bool = False) -> str:
        """生成智能回應"""
        if is_new_question:
            return self._handle_new_question(student_answer)
        else:
            return self._handle_student_response(student_answer)
    
    def _handle_new_question(self, question: str) -> str:
        """處理新問題"""
        self.current_topic = question
        self.covered_points.clear()
        self.learning_path = ["基礎理解"]
        
        # 獲取知識背景
        self.topic_knowledge = self._get_topic_knowledge(question)
        
        prompt = f"""你是資管系專業老師，學生問：{question}

教材知識：{self.topic_knowledge[:800]}

請開始蘇格拉底式教學：
1. 簡潔確認問題（不要重複問題內容）
2. 一句話說明科目領域
3. 提出一個基礎但關鍵的引導問題

要求：
- 語氣自然親切，像真正的老師
- 不要使用「但是」「不過」等轉折詞
- 直接進入引導，不要冗長說明
- 一次只問一個問題

回答要簡潔有力，直接切入重點。"""

        return self._call_ai(prompt)
    
    def _handle_student_response(self, student_answer: str) -> str:
        """處理學生回應"""
        # AI分析學生回答
        analysis = self.analyze_student_response(student_answer)
        self.student_understanding_level = int(analysis.get("understanding_level", 2))

        # 構建完全基於AI分析的回應
        prompt = f"""你是專業的資管系老師，正在進行蘇格拉底式教學。

當前主題：{self.current_topic}
學生剛才回答：{student_answer}

AI教學分析：
- 理解程度：{analysis.get('understanding_level')}/5
- 回答分析：{analysis.get('response_analysis')}
- 學生優點：{analysis.get('student_strengths')}
- 需要改進：{analysis.get('areas_to_improve')}
- 下一步重點：{analysis.get('next_teaching_focus')}
- 建議方式：{analysis.get('best_teaching_approach')}

對話歷史：{self._get_recent_context()}

請根據AI分析結果，自然地回應學生：
1. 針對學生回答給予具體評價
2. 根據建議的教學方式繼續引導
3. 語氣要自然親切，像真正的老師
4. 不要重複之前討論過的內容
5. 避免套話和機械化回應

請直接開始老師的回應，不要寫標題或格式。"""

        return self._call_ai(prompt)
    
    def _get_topic_knowledge(self, question: str) -> str:
        """獲取主題知識"""
        try:
            # 翻譯並搜索
            english_q = self._translate_to_english(question)
            results = self.ai_responder.search_knowledge(english_q)
            if results:
                return "\n".join([r.get('content', '')[:400] for r in results[:2]])
        except:
            pass
        return ""
    
    def _translate_to_english(self, text: str) -> str:
        """翻譯成英文"""
        try:
            prompt = f"Translate to English: {text}"
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.1, "num_predict": 50}
                },
                timeout=10
            )
            if response.status_code == 200:
                return response.json().get('response', '').strip()
        except:
            pass
        return text
    
    def _get_recent_context(self) -> str:
        """獲取最近對話上下文"""
        if len(self.conversation_context) >= 2:
            recent = self.conversation_context[-2:]
            return "\n".join([f"學生：{item.get('student', '')}\n老師：{item.get('teacher', '')}" for item in recent])
        return ""
    
    def _call_ai(self, prompt: str) -> str:
        """調用AI"""
        try:
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.4, "num_predict": 200}
                },
                timeout=25
            )
            
            if response.status_code == 200:
                return response.json().get('response', '').strip()
        except Exception as e:
            return f"抱歉，我需要一點時間思考。請重新提問。"
        
        return "請重新提問。"
    
    def record_conversation(self, student_input: str, teacher_response: str):
        """記錄對話"""
        self.conversation_context.append({
            "student": student_input,
            "teacher": teacher_response
        })
        
        # 保持最近5輪對話
        if len(self.conversation_context) > 5:
            self.conversation_context = self.conversation_context[-5:]
    
    def get_learning_progress(self) -> str:
        """獲取學習進度"""
        return f"""
📊 學習進度
主題: {self.current_topic or '無'}
理解程度: {self.student_understanding_level}/5
已討論要點: {len(self.covered_points)}
對話輪數: {len(self.conversation_context)}
"""
    
    def reset(self):
        """重置對話"""
        self.current_topic = None
        self.learning_path = []
        self.student_understanding_level = 0
        self.conversation_context = []
        self.covered_points.clear()
