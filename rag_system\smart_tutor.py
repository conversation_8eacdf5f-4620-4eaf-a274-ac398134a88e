#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能蘇格拉底式教學系統
解決重複性、引導深度、評估準確性問題
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import requests
import json
import re
from typing import Dict, List, Any, Optional
from rag_ai_responder import AIResponder
import config

class SmartTutor:
    """智能蘇格拉底式教師"""
    
    def __init__(self):
        """初始化智能教師"""
        self.ai_responder = AIResponder(language='chinese')
        self.ai_base_url = config.AI_CONFIG['base_url']
        self.ai_model = config.AI_CONFIG['model']
        
        # 對話狀態
        self.current_topic = None
        self.learning_path = []  # 學習路徑
        self.student_understanding_level = 0  # 0-5 理解層次
        self.conversation_context = []
        self.topic_knowledge = ""
        self.covered_points = set()  # 已覆蓋的知識點
        
        # 引導問題類型
        self.question_types = {
            "基礎理解": ["定義", "概念", "是什麼"],
            "深入分析": ["為什麼", "如何工作", "原理"],
            "應用場景": ["什麼時候用", "實際應用", "例子"],
            "比較對比": ["區別", "優缺點", "與其他方法比較"],
            "問題解決": ["如何解決", "步驟", "實作"],
            "評估判斷": ["評價", "選擇", "判斷標準"]
        }
        
        print("🎓 智能蘇格拉底式教學系統已啟動")
        print("💡 深度引導，避免重複，精準評估")
    
    def analyze_student_response(self, response: str) -> Dict[str, Any]:
        """深度分析學生回答"""
        prompt = f"""作為專業教育評估專家，分析學生回答：

學生回答：{response}
當前主題：{self.current_topic}
已討論要點：{list(self.covered_points)}

請分析並回答JSON格式：
{{
    "understanding_level": 1-5,
    "response_quality": "excellent/good/basic/poor/unclear",
    "key_concepts_mentioned": ["概念1", "概念2"],
    "misconceptions": ["誤解1", "誤解2"],
    "next_focus": "應該重點關注的下一個概念",
    "teaching_strategy": "direct_explain/guided_question/example_illustration/comparison",
    "specific_feedback": "具體的回饋內容"
}}

只回答JSON。"""

        try:
            response_obj = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.2, "num_predict": 300}
                },
                timeout=25
            )
            
            if response_obj.status_code == 200:
                ai_response = response_obj.json().get('response', '')
                json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
        except:
            pass
        
        # 備用分析
        return {
            "understanding_level": 2,
            "response_quality": "basic",
            "key_concepts_mentioned": [],
            "misconceptions": [],
            "next_focus": "基礎概念",
            "teaching_strategy": "guided_question",
            "specific_feedback": "讓我們繼續探討"
        }
    
    def generate_smart_response(self, student_answer: str = None, is_new_question: bool = False) -> str:
        """生成智能回應"""
        if is_new_question:
            return self._handle_new_question(student_answer)
        else:
            return self._handle_student_response(student_answer)
    
    def _handle_new_question(self, question: str) -> str:
        """處理新問題"""
        self.current_topic = question
        self.covered_points.clear()
        self.learning_path = ["基礎理解"]
        
        # 獲取知識背景
        self.topic_knowledge = self._get_topic_knowledge(question)
        
        prompt = f"""你是資管系專業老師，學生問：{question}

教材知識：{self.topic_knowledge[:800]}

請開始蘇格拉底式教學：
1. 簡潔確認問題（不要重複問題內容）
2. 一句話說明科目領域
3. 提出一個基礎但關鍵的引導問題

要求：
- 語氣自然親切，像真正的老師
- 不要使用「但是」「不過」等轉折詞
- 直接進入引導，不要冗長說明
- 一次只問一個問題

回答要簡潔有力，直接切入重點。"""

        return self._call_ai(prompt)
    
    def _handle_student_response(self, student_answer: str) -> str:
        """處理學生回應"""
        # 分析學生回答
        analysis = self.analyze_student_response(student_answer)
        self.student_understanding_level = analysis["understanding_level"]
        
        # 更新已覆蓋的知識點
        self.covered_points.update(analysis["key_concepts_mentioned"])
        
        # 決定下一步教學策略
        next_focus = analysis["next_focus"]
        strategy = analysis["teaching_strategy"]
        
        # 構建上下文
        recent_context = self._get_recent_context()
        
        if strategy == "direct_explain":
            prompt = f"""學生回答：{student_answer}
分析結果：{analysis["specific_feedback"]}

請直接解釋關鍵概念：{next_focus}
要求：
- 簡潔明確，不重複之前說過的內容
- 用生活化例子說明
- 語氣鼓勵但不過度讚美
- 解釋後問一個深入問題

避免說「你的答案是正確的」這類套話。"""
        
        elif strategy == "example_illustration":
            prompt = f"""學生回答：{student_answer}
需要用例子說明：{next_focus}

請用具體例子幫助理解：
- 選擇貼近學生經驗的例子
- 例子要能清楚說明概念
- 例子後問學生是否理解這個類比
- 不要重複之前的內容

語氣要自然，像在聊天。"""
        
        else:  # guided_question
            prompt = f"""學生回答：{student_answer}
理解程度：{analysis["understanding_level"]}/5
下一個重點：{next_focus}
已討論：{list(self.covered_points)}

請繼續引導：
1. 簡短回應學生答案（不要套話）
2. 提出一個深入的引導問題，關注：{next_focus}

要求：
- 不要重複之前討論過的內容
- 問題要有邏輯遞進性
- 語氣自然，避免「但是你知道嗎」等句式
- 一次只問一個問題

讓對話自然流暢。"""
        
        return self._call_ai(prompt)
    
    def _get_topic_knowledge(self, question: str) -> str:
        """獲取主題知識"""
        try:
            # 翻譯並搜索
            english_q = self._translate_to_english(question)
            results = self.ai_responder.search_knowledge(english_q)
            if results:
                return "\n".join([r.get('content', '')[:400] for r in results[:2]])
        except:
            pass
        return ""
    
    def _translate_to_english(self, text: str) -> str:
        """翻譯成英文"""
        try:
            prompt = f"Translate to English: {text}"
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.1, "num_predict": 50}
                },
                timeout=10
            )
            if response.status_code == 200:
                return response.json().get('response', '').strip()
        except:
            pass
        return text
    
    def _get_recent_context(self) -> str:
        """獲取最近對話上下文"""
        if len(self.conversation_context) >= 2:
            recent = self.conversation_context[-2:]
            return "\n".join([f"學生：{item.get('student', '')}\n老師：{item.get('teacher', '')}" for item in recent])
        return ""
    
    def _call_ai(self, prompt: str) -> str:
        """調用AI"""
        try:
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.4, "num_predict": 200}
                },
                timeout=25
            )
            
            if response.status_code == 200:
                return response.json().get('response', '').strip()
        except Exception as e:
            return f"抱歉，我需要一點時間思考。請重新提問。"
        
        return "請重新提問。"
    
    def record_conversation(self, student_input: str, teacher_response: str):
        """記錄對話"""
        self.conversation_context.append({
            "student": student_input,
            "teacher": teacher_response
        })
        
        # 保持最近5輪對話
        if len(self.conversation_context) > 5:
            self.conversation_context = self.conversation_context[-5:]
    
    def get_learning_progress(self) -> str:
        """獲取學習進度"""
        return f"""
📊 學習進度
主題: {self.current_topic or '無'}
理解程度: {self.student_understanding_level}/5
已討論要點: {len(self.covered_points)}
對話輪數: {len(self.conversation_context)}
"""
    
    def reset(self):
        """重置對話"""
        self.current_topic = None
        self.learning_path = []
        self.student_understanding_level = 0
        self.conversation_context = []
        self.covered_points.clear()
