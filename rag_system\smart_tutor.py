#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能蘇格拉底式教學系統
解決重複性、引導深度、評估準確性問題
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import requests
import json
import re
from typing import Dict, List, Any, Optional
from rag_ai_responder import AIResponder
import config

class SmartTutor:
    """智能蘇格拉底式教師"""
    
    def __init__(self):
        """初始化智能教師"""
        # 完全關閉日誌輸出
        import logging
        logging.getLogger('rag_ai_responder').setLevel(logging.CRITICAL)
        logging.getLogger('rag_processor').setLevel(logging.CRITICAL)

        self.ai_responder = AIResponder(language='chinese')
        self.ai_base_url = config.AI_CONFIG['base_url']
        self.ai_model = config.AI_CONFIG['model']
        
        # 對話狀態
        self.current_topic = None
        self.student_understanding_level = 0  # 0-5 理解層次
        self.conversation_context = []
        self.topic_knowledge = ""
        self.covered_points = set()  # 已覆蓋的知識點
        
        print("🎓 智能蘇格拉底式教學系統已啟動")
        print("💡 深度引導，避免重複，精準評估")
    
    def analyze_student_response(self, response: str) -> Dict[str, Any]:
        """AI深度分析學生回答"""
        prompt = f"""分析學生回答並提供教學建議：

學生回答：{response}
當前討論主題：{self.current_topic}

請簡潔分析並用JSON回答：
{{
    "understanding_level": 3,
    "is_correct": true,
    "key_insight": "學生理解的核心要點",
    "next_step": "下一步教學重點",
    "response_style": "simple_confirm"
}}

只回答JSON，不要其他內容。"""

        try:
            response_obj = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.2, "num_predict": 300}
                },
                timeout=25
            )
            
            if response_obj.status_code == 200:
                ai_response = response_obj.json().get('response', '')
                json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
        except:
            pass
        
        # 備用分析
        return {
            "understanding_level": 2,
            "is_correct": True,
            "key_insight": "學生有基本理解",
            "next_step": "繼續深入探討",
            "response_style": "simple_confirm"
        }
    
    def generate_smart_response(self, student_answer: str = None, is_new_question: bool = False) -> str:
        """生成智能回應"""
        if is_new_question:
            return self._handle_new_question(student_answer)
        else:
            return self._handle_student_response(student_answer)
    
    def _handle_new_question(self, question: str) -> str:
        """處理新問題"""
        self.current_topic = question
        self.covered_points.clear()

        # 獲取知識背景
        self.topic_knowledge = self._get_topic_knowledge(question)

        prompt = f"""你是資管系專業老師，學生問：{question}

教材內容：{self.topic_knowledge[:1000]}

請用蘇格拉底式教學開始：
1. 先根據教材簡單解釋這個概念（2-3句話）
2. 然後從最基礎的相關概念開始提問引導

例如：如果學生問銀行家演算法，你應該：
- 先簡單說明銀行家演算法是什麼
- 然後問「你知道什麼是死鎖嗎？」這樣的基礎問題

要求：
- 語氣自然，像真正的老師在對話
- 不要用編號格式（1.2.3.）
- 先解釋再提問，循序漸進
- 從最基礎的概念開始引導

請直接開始教學對話。"""

        return self._call_ai(prompt)
    
    def _handle_student_response(self, student_answer: str) -> str:
        """處理學生回應"""
        # 獲取最近的對話上下文
        recent_context = self._get_recent_context()

        # 簡化的回應生成
        prompt = f"""你是資管系老師，學生剛才回答：{student_answer}

當前主題：{self.current_topic}
最近對話：{recent_context}

請自然回應：
1. 簡短評價學生回答（對/不完全對/需要澄清）
2. 提出下一個引導問題
3. 語氣自然，不要重複之前說過的內容
4. 一次只問一個問題

直接開始回應，不要格式化。"""

        return self._call_ai(prompt)
    
    def _get_topic_knowledge(self, question: str) -> str:
        """獲取主題知識"""
        try:
            # 翻譯並搜索
            english_q = self._translate_to_english(question)
            results = self.ai_responder.search_knowledge(english_q)
            if results:
                return "\n".join([r.get('content', '')[:400] for r in results[:2]])
        except:
            pass
        return ""
    
    def _translate_to_english(self, text: str) -> str:
        """翻譯成英文"""
        try:
            prompt = f"Translate to English: {text}"
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.1, "num_predict": 50}
                },
                timeout=10
            )
            if response.status_code == 200:
                return response.json().get('response', '').strip()
        except:
            pass
        return text
    
    def _get_recent_context(self) -> str:
        """獲取最近對話上下文"""
        if len(self.conversation_context) >= 2:
            recent = self.conversation_context[-2:]
            return "\n".join([f"學生：{item.get('student', '')}\n老師：{item.get('teacher', '')}" for item in recent])
        return ""
    
    def _call_ai(self, prompt: str) -> str:
        """調用AI"""
        try:
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.3,
                        "num_predict": 300,
                        "top_p": 0.9
                    }
                },
                timeout=30
            )

            if response.status_code == 200:
                ai_response = response.json().get('response', '').strip()
                # 確保回應不為空且合理
                if ai_response and len(ai_response) > 10:
                    return ai_response
                else:
                    return "讓我們繼續探討這個概念。"
            else:
                return "抱歉，讓我重新組織一下思路。"
        except Exception as e:
            return "請稍等，讓我重新思考一下。"
    
    def record_conversation(self, student_input: str, teacher_response: str):
        """記錄對話"""
        self.conversation_context.append({
            "student": student_input,
            "teacher": teacher_response
        })
        
        # 保持最近5輪對話
        if len(self.conversation_context) > 5:
            self.conversation_context = self.conversation_context[-5:]
    
    def get_learning_progress(self) -> str:
        """獲取學習進度"""
        return f"""
📊 學習進度
主題: {self.current_topic or '無'}
理解程度: {self.student_understanding_level}/5
已討論要點: {len(self.covered_points)}
對話輪數: {len(self.conversation_context)}
"""
    
    def reset(self):
        """重置對話"""
        self.current_topic = None
        self.learning_path = []
        self.student_understanding_level = 0
        self.conversation_context = []
        self.covered_points.clear()
