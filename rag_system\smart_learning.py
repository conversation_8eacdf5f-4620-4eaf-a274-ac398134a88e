#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能學習系統
解決重複性、深度引導、自然對話問題
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from smart_tutor import SmartTutor

class SmartLearningSystem:
    """智能學習系統"""
    
    def __init__(self):
        """初始化系統"""
        self.tutor = SmartTutor()
        self.in_conversation = False
        self.waiting_for_response = False
        
    def show_welcome(self):
        """顯示歡迎訊息"""
        print("="*80)
        print("🎓 智能蘇格拉底式學習系統 v2.0")
        print("="*80)
        print()
        print("🌟 全新特色：")
        print("  • 🧠 深度理解分析，精準評估學習程度")
        print("  • 🎯 邏輯連貫的引導路徑，避免重複提問")
        print("  • 💭 自然對話語氣，像真正的老師")
        print("  • 📚 智能知識點追蹤，確保學習完整性")
        print("  • 🔄 動態教學策略調整")
        print()
        print("💡 使用說明：")
        print("  • 直接提問開始學習")
        print("  • 積極回答老師的引導問題")
        print("  • 輸入 'new' 開始新主題")
        print("  • 輸入 'progress' 查看學習進度")
        print("  • 輸入 'quit' 退出系統")
        print()
        print("🎯 目標：透過深度引導，真正理解概念本質")
        print("="*80)
    
    def handle_special_commands(self, user_input: str) -> bool:
        """處理特殊命令"""
        user_input = user_input.lower().strip()
        
        if user_input in ['quit', 'exit', '退出', '結束']:
            print("\n👋 感謝使用智能學習系統！")
            print("🎓 希望您的學習之路收穫滿滿！")
            return True
        
        elif user_input in ['new', '新問題', '新主題', 'reset']:
            self.tutor.reset()
            self.in_conversation = False
            self.waiting_for_response = False
            print("\n🆕 已開始新的學習主題，請提出您的問題：")
            return False
        
        elif user_input in ['progress', '進度', '狀態']:
            progress = self.tutor.get_learning_progress()
            print(f"\n{progress}")
            return False
        
        elif user_input in ['help', '幫助', '說明']:
            self.show_help()
            return False
        
        return False
    
    def show_help(self):
        """顯示幫助訊息"""
        print("\n📚 智能學習系統說明")
        print("-" * 50)
        print("🎯 學習命令：")
        print("  • 直接提問 - 開始新的學習主題")
        print("  • 回答問題 - 在引導過程中回答老師提問")
        print("  • new/新問題 - 開始新的學習主題")
        print()
        print("🔧 系統命令：")
        print("  • progress/進度 - 查看學習進度和理解程度")
        print("  • help/幫助 - 顯示此說明")
        print("  • quit/退出 - 退出系統")
        print()
        print("💡 學習技巧：")
        print("  • 系統會智能分析您的回答深度")
        print("  • 引導問題會根據您的理解程度調整")
        print("  • 如果不理解，直接說「不懂」或「不清楚」")
        print("  • 嘗試用自己的話表達概念")
    
    def detect_input_type(self, user_input: str) -> str:
        """智能檢測輸入類型"""
        # 如果正在等待回應，一定是回答
        if self.waiting_for_response:
            return "answer"
        
        # 如果不在對話中，一定是新問題
        if not self.in_conversation:
            return "new_question"
        
        # 在對話中，檢查是否是新問題
        new_question_patterns = [
            "什麼是", "為什麼", "如何", "怎麼", "請解釋", "請說明",
            "能否", "可以", "告訴我", "我想知道"
        ]
        
        if any(pattern in user_input for pattern in new_question_patterns):
            return "new_question"
        
        return "answer"
    
    def run(self):
        """運行智能學習系統"""
        self.show_welcome()
        
        while True:
            try:
                # 智能提示詞
                if self.waiting_for_response:
                    prompt = "\n💭 請回答: "
                elif self.in_conversation:
                    prompt = "\n🤔 請回答下一個問題: "
                else:
                    prompt = "\n🤔 請提出您的問題: "
                
                user_input = input(prompt).strip()
                
                if not user_input:
                    print("💡 請輸入您的問題或回答。")
                    continue
                
                # 處理特殊命令
                if self.handle_special_commands(user_input):
                    break
                
                # 檢測輸入類型
                input_type = self.detect_input_type(user_input)
                
                if input_type == "new_question":
                    # 新問題 - 重置狀態
                    if self.in_conversation:
                        self.tutor.reset()
                    
                    self.in_conversation = True
                    self.waiting_for_response = False
                    
                    print("\n🎓 老師回應：")
                    response = self.tutor.generate_smart_response(user_input, is_new_question=True)
                    print(response)
                    
                    # 記錄對話
                    self.tutor.record_conversation(user_input, response)
                    
                    # 檢查是否需要等待回應
                    if self._has_question(response):
                        self.waiting_for_response = True
                
                else:
                    # 學生回答
                    if self.in_conversation:
                        print("\n🎓 老師回應：")
                        response = self.tutor.generate_smart_response(user_input, is_new_question=False)
                        print(response)
                        
                        # 記錄對話
                        self.tutor.record_conversation(user_input, response)
                        
                        # 檢查是否需要繼續等待回應
                        if self._has_question(response):
                            self.waiting_for_response = True
                        else:
                            self.waiting_for_response = False
                    else:
                        print("💡 請先提出一個問題開始學習，或輸入 'help' 查看說明。")
                
            except KeyboardInterrupt:
                print("\n\n👋 感謝使用智能學習系統！")
                break
            except Exception as e:
                print(f"\n❌ 系統錯誤: {e}")
                print("💡 請重新輸入您的問題。")
    
    def _has_question(self, response: str) -> bool:
        """檢查回應是否包含問題"""
        question_indicators = [
            "?", "？", "嗎", "呢", "想想看", "你認為", "你覺得", 
            "能否", "可以", "試著", "考慮", "思考"
        ]
        return any(indicator in response for indicator in question_indicators)

def main():
    """主函數"""
    try:
        learning_system = SmartLearningSystem()
        learning_system.run()
    except Exception as e:
        print(f"❌ 系統啟動失敗: {e}")
        print("💡 請檢查:")
        print("  1. Ollama是否運行: ollama serve")
        print("  2. 模型是否可用: ollama list")
        print("  3. 向量資料庫是否建立")

if __name__ == "__main__":
    main()
