import os
import fitz  # PyMuPdf

def convert_pdf_to_images(pdf_path, output_folder="exam_img"):
    """
    將 PDF 檔案的每一頁轉換為 PNG 圖片。

    Args:
        pdf_path (str): PDF 檔案的路徑。
        output_folder (str): 儲存圖片的資料夾名稱，預設為 "exam_img"。

    Returns:
        list: 包含所有生成圖片檔案路徑的列表。
    """
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_paths = []
    try:
        pdf_document = fitz.open(pdf_path)
        for page_number in range(len(pdf_document)):
            page = pdf_document.load_page(page_number)  # 載入每一頁
            pix = page.get_pixmap()  # 擷取頁面的像素資料 (使用 get_pixmap())
            output_filename = f"{os.path.splitext(os.path.basename(pdf_path))[0]}_page_{page_number + 1}.png"
            output_path = os.path.join(output_folder, output_filename)
            pix.save(output_path)  # 將像素資料儲存為 PNG 圖片
            image_paths.append(output_path)
        pdf_document.close()
        print(f"PDF '{pdf_path}' 已成功轉換為圖片並儲存至 '{output_folder}'")
    except Exception as e:
        print(f"轉換 PDF '{pdf_path}' 時發生錯誤: {e}")
    return image_paths

if __name__ == "__main__":
    exam_data_folder = "exam_data"
    for filename in os.listdir(exam_data_folder):
        if filename.endswith(".pdf"):
            pdf_path = os.path.join(exam_data_folder, filename)
            convert_pdf_to_images(pdf_path)