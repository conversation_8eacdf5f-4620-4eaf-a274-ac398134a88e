import os
import sys
from pathlib import Path
import re
from src.read import ProblemReader
import flet as ft
from src.flet_ui import main

def print_mode():
    """以命令行模式打印題目信息"""
    try:
        # 創建題目讀取器實例
        reader = ProblemReader()
        
        # 打印所有題目信息
        print("===== 所有題目信息 =====")
        reader.print_problem_info()
        
        # 也可以單獨打印特定題目的信息
        print("\n===== 指定題目信息 =====")
        for problem_number in range(1, 14):  # 打印題目1到13的信息
            reader.print_problem_info(problem_number)
        
    except Exception as e:
        print(f"程序執行出錯: {e}")

def gui_mode():
    """啟動GUI模式顯示題目"""
    try:
        print("啟動GUI應用...")
        print("提示: 在GUI模式中，您可以點擊「使用AI分析此題」按鈕，讓Google Gemini AI分析題目內容並提供解答")
        # 使用系統命令運行flet_ui.py
        import subprocess
        subprocess.run([sys.executable, "src/flet_ui.py"])
    except Exception as e:
        print(f"啟動GUI模式時出錯: {e}")

def run_app():
    """運行Flet應用程序"""
    try:
        # 確保當前目錄在src
        current_dir = Path(__file__).parent
        if current_dir.name == 'src':
            sys.path.append(str(Path(__file__).parent / "src"))
        
        # 啟動GUI模式
        print("啟動GUI模式...")
        ft.app(target=main)
        
    except Exception as e:
        print(f"程序運行時出錯: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    run_app()
