import cv2
import numpy as np
import os
import pytesseract
from PIL import Image
import json
import re

# 设置 Tesseract 路径 
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

def has_border(roi, gray_roi):
    height, width = gray_roi.shape
    if width < 100 or height < 100:
        return False
    edges = cv2.Canny(gray_roi, 50, 150)
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=min(width, height)//3, maxLineGap=20)
    if lines is None or len(lines) < 4:
        return False
    h_lines = []
    v_lines = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        angle = abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)
        if angle < 30 or angle > 150:
            h_lines.append((min(x1, x2), max(x1, x2), (y1 + y2) // 2))  
        elif 60 < angle < 120:
            v_lines.append(((x1 + x2) // 2, min(y1, y2), max(y1, y2)))  
    has_top = any(y < height * 0.2 for _, _, y in h_lines)
    has_bottom = any(y > height * 0.8 for _, _, y in h_lines)
    has_left = any(x < width * 0.2 for x, _, _ in v_lines)
    has_right = any(x > width * 0.8 for x, _, _ in v_lines)
    has_frame = (len(h_lines) >= 2 and len(v_lines) >= 2) and (has_top and has_bottom and has_left and has_right)
    edge_density = np.sum(edges) / (width * height)
    edge_perimeter = np.sum(edges[0, :]) + np.sum(edges[-1, :]) + np.sum(edges[:, 0]) + np.sum(edges[:, -1])
    perimeter_density = edge_perimeter / (2 * (width + height))
    central_region = edges[height//4:3*height//4, width//4:3*width//4]
    central_density = np.sum(central_region) / central_region.size
    edge_to_center_ratio = (perimeter_density + 1e-6) / (central_density + 1e-6) 
    return has_frame or (edge_density > 0.05 and edge_to_center_ratio > 2.0)

def is_image_block(roi, gray_roi):
    height, width = gray_roi.shape
    if width < 60 or height < 60:
        return False
    gray_std = np.std(gray_roi)
    texture = cv2.Laplacian(gray_roi, cv2.CV_64F)
    texture_complexity = np.var(texture)
    edges = cv2.Canny(gray_roi, 50, 150)
    edge_density = np.sum(edges) / (width * height)
    hist = cv2.calcHist([gray_roi], [0], None, [256], [0, 256])
    hist_std = np.std(hist)
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, 40, minLineLength=width//10, maxLineGap=20)
    circles = cv2.HoughCircles(gray_roi, cv2.HOUGH_GRADIENT, 1, 20, param1=50, param2=30, minRadius=5, maxRadius=30)
    contours, _ = cv2.findContours(edges, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    arrow_like = 0
    for c in contours:
        approx = cv2.approxPolyDP(c, 0.02 * cv2.arcLength(c, True), True)
        if 5 <= len(approx) <= 7:
            arrow_like += 1
    small_objects = sum(1 for c in contours if 10 < cv2.contourArea(c) < 100)
    ret, thresh = cv2.threshold(gray_roi, 127, 255, cv2.THRESH_BINARY)
    num_labels, _, _, _ = cv2.connectedComponentsWithStats(thresh)
    condition1 = gray_std > 20 and texture_complexity > 80
    condition2 = edge_density > 0.05 and hist_std > 15
    condition3 = lines is not None and len(lines) >= 4
    condition4 = (circles is not None and len(circles[0]) >= 3) or (arrow_like >= 2)
    condition5 = num_labels > 15 and small_objects >= 5

    return (condition1 and condition2) or \
           (condition3 and condition4) or \
           (condition1 and condition4) or \
           (condition2 and condition5)

def is_text_too_dense(roi, gray_roi):
    text = pytesseract.image_to_string(gray_roi)
    lines = text.strip().split('\n')
    if len(lines) <= 1:
        return False
    avg_chars_per_line = sum(len(line) for line in lines) / len(lines)
    height, width = gray_roi.shape
    text_density = len(text) / (height * width)
    edges = cv2.Canny(gray_roi, 100, 200)
    edge_density = np.sum(edges) / (height * width)
    long_lines = sum(1 for line in lines if len(line.strip()) > 40)
    long_line_ratio = long_lines / len(lines) if lines else 0
    return (avg_chars_per_line > 40 and len(lines) > 5) or (text_density > 0.05 and edge_density > 0.2 and long_line_ratio > 0.7)

def merge_overlapping_rectangles(rectangles, overlap_threshold=0.7):
    if not rectangles:
        return []
    result = []
    rectangles.sort(key=lambda r: r[0] * 10000 + r[1])  
    current = rectangles[0]
    for rect in rectangles[1:]:
        x1, y1, w1, h1 = current
        x2, y2, w2, h2 = rect
        x_overlap = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
        y_overlap = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
        overlap_area = x_overlap * y_overlap
        area1 = w1 * h1
        area2 = w2 * h2
        intersection_area = overlap_area
        union_area = area1 + area2 - intersection_area
        overlap_ratio = intersection_area / union_area if union_area > 0 else 0
        
        if overlap_ratio > overlap_threshold:
            if area1 >= area2:
                current = (x1, y1, w1, h1)
            else:
                current = (x2, y2, w2, h2)
        else:
            x = min(x1, x2)
            y = min(y1, y2)
            w = max(x1 + w1, x2 + w2) - x
            h = max(y1 + h1, y2 + h2) - y
            current = (x, y, w, h)
        
    result.append(current)
    
    return result


def is_valid_zone(x, y, w, h, img_height, img_width):
    if w < 70 or h < 70: 
        return False
    return True

def parse_filename(image_path):
    """
    從新的檔案名稱解析資訊: Abbreviation_SubjectCode_Year_QuestionNum-Suffix.png
    例如: YPU_CS_101_4-0point_cont.png -> YPU, CS, 101, 4
    """
    filename = os.path.basename(image_path)
    name, _ = os.path.splitext(filename)
    # Regex to capture: SchoolAbbr, SubjectCode, Year, QuestionNumber
    # Assumes suffix starts after the question number and might contain non-digits
    pattern = re.compile(r"^([A-Z_]+)_([A-Z]+)_(\d+)_(\d+).*")
    match = pattern.match(name)

    if match:
        school_abbr = match.group(1)
        subject_code = match.group(2)
        year = match.group(3)
        try:
            question_number = int(match.group(4))
        except ValueError:
            question_number = None # Or handle as needed
        # We map subject_code to department for now, adjust if needed
        return school_abbr, subject_code, year, question_number
    else:
        # Return None if the filename doesn't match the expected pattern
        return None, None, None, None

def extract_text_from_image(img_array):
    """使用Tesseract OCR從圖片Numpy陣列中提取文字"""
    # Use PIL to convert numpy array (BGR from OpenCV) to RGB for Tesseract if needed,
    # or let Tesseract handle the BGR numpy array directly.
    # Tesseract typically prefers RGB.
    try:
        img_rgb = cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB)
        pil_img = Image.fromarray(img_rgb)
        text = pytesseract.image_to_string(pil_img, lang="chi_tra+eng")  # 支援中英文混合
        return text.strip()
    except Exception as e:
        print(f"[錯誤] Tesseract OCR 處理失敗: {e}")
        return "" # Return empty string on error

def save_all_results_to_json(all_data, output_file_path):
    """將所有結果的列表保存為單個JSON文件"""
    # Ensure the output directory exists
    output_dir = os.path.dirname(output_file_path)
    os.makedirs(output_dir, exist_ok=True)

    # Save the list of data dictionaries to the specified file path
    with open(output_file_path, "w", encoding="utf-8") as f:
        json.dump(all_data, f, ensure_ascii=False, indent=2)

def process_image(image_path, output_img_dir="output", show_images=True):
    """處理單個圖片：區域檢測+OCR -> 回傳數據字典"""
    # parse_filename now returns: school_abbr, subject_code, year, question_number
    school_abbr, subject_code, year, question_number = parse_filename(image_path)

    if not all([school_abbr, subject_code, year, question_number is not None]): # Check question_number validity
        print(f"[警告] 新檔名格式解析錯誤或不完整，跳過：{image_path}")
        return None

    # Ensure the base output image directory exists (This one IS needed for ROI images)
    os.makedirs(output_img_dir, exist_ok=True)

    # --- Read image using imdecode for Unicode path compatibility ---
    try:
        with open(image_path, 'rb') as f:
            img_bytes = f.read()
        img_np = np.frombuffer(img_bytes, np.uint8)
        img = cv2.imdecode(img_np, cv2.IMREAD_COLOR)
        if img is None:
            # Raise an error if decoding failed after successfully reading bytes
            raise ValueError("cv2.imdecode returned None")
    except Exception as e:
        print(f"[錯誤] 無法讀取或解碼圖片 (imdecode): {image_path} - {e}")
        # Return None instead of False
        return None
    # --- End image reading modification ---

    # Pass the loaded numpy array to the OCR function
    full_question_text = extract_text_from_image(img)

    base_name = os.path.splitext(os.path.basename(image_path))[0]
    img_height, img_width = img.shape[:2]
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    enhanced_gray = cv2.convertScaleAbs(gray, alpha=1.2, beta=10)
    edges = cv2.Canny(enhanced_gray, 30, 150)
    kernel = np.ones((3, 3), np.uint8)
    dilated = cv2.dilate(edges, kernel, iterations=2)
    contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    output_img = img.copy()
    detected_rectangles = []
    
    # 檢測初始區域
    for cnt in contours:
        x, y, w, h = cv2.boundingRect(cnt)
        if not is_valid_zone(x, y, w, h, img_height, img_width):
            continue
        # 在初始區域檢測時增加擴展範圍，條這個如果擷取附圖太小或太大0430原本45 55改成35 45
        x_expanded = max(0, x - 35)  
        y_expanded = max(0, y - 45)
        w_expanded = min(img_width - x_expanded, w + 100) 
        h_expanded = min(img_height - y_expanded, h + 110)
        roi = img[y_expanded:y_expanded+h_expanded, x_expanded:x_expanded+w_expanded]
        gray_roi = gray[y_expanded:y_expanded+h_expanded, x_expanded:x_expanded+w_expanded]
        if is_text_too_dense(roi, gray_roi):
            continue
        is_border_block = has_border(roi, gray_roi)
        is_image = is_image_block(roi, gray_roi)
        if is_border_block or is_image:
            detected_rectangles.append((x_expanded, y_expanded, w_expanded, h_expanded))
    merged_rectangles = merge_overlapping_rectangles(detected_rectangles)
    region_files = []
    for i, (x, y, w, h) in enumerate(merged_rectangles):
        roi = img[y:y+h, x:x+w]
        # Construct region filename relative to the base output_img_dir
        # region_subfolder = os.path.relpath(os.path.dirname(image_path), os.path.dirname(os.path.dirname(image_path))) # Get relative subfolder path like '101', '102' etc.
        # region_output_dir = os.path.join(output_img_dir, region_subfolder)
        # os.makedirs(region_output_dir, exist_ok=True) # Create specific subfolder for region images

        region_filename_only = f"{base_name}_region_{i+1}.png"
        # Save directly into the base output_img_dir
        output_img_path = os.path.join(output_img_dir, region_filename_only)

        # Store relative path for JSON data (now relative to output_img_dir)
        # region_files.append(os.path.join(region_subfolder, region_filename_only).replace('\\', '/')) # Use relative path in JSON
        region_files.append(region_filename_only) # Store only the filename

        # --- Write ROI using imencode for Unicode path compatibility ---
        try:
            is_success, buffer = cv2.imencode(".png", roi)
            if not is_success:
                raise ValueError("cv2.imencode failed")
            with open(output_img_path, 'wb') as f:
                f.write(buffer)
        except Exception as e:
             print(f"[錯誤] 無法寫入圖片區塊 (imencode): {output_img_path} - {e}")
             # Decide if you want to continue or return False here
        gray_roi = gray[y:y+h, x:x+w]
        is_border_block = has_border(roi, gray_roi)
        is_image = is_image_block(roi, gray_roi)
        if is_border_block:
            color = (0, 0, 255)
            region_type = "框線區塊"
        elif is_image:
            color = (255, 0, 0)
            region_type = "圖片區塊"
        else:
            color = (128, 128, 128) 
            region_type = "未知區塊"
        cv2.rectangle(output_img, (x, y), (x+w, y+h), color, 2)
        cv2.putText(output_img, f"{i+1}:{region_type}", (x, y-5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
    data = {
        "school": school_abbr,       # Using abbreviation as school identifier
        "department": subject_code, # Using subject code as department identifier
        "year": year,
        "question_number": question_number,
        "question_text": full_question_text,
        "image_file": region_files   # Relative paths to extracted region images
    }
    
    if show_images:
        display_height = 720
        display_width = int(img_width * (display_height / img_height))
        resized_output = cv2.resize(output_img, (display_width, display_height))
        print(f"▶️ {os.path.basename(image_path)} 檢測到 {len(merged_rectangles)} 個區塊")
        cv2.imshow("檢測結果", resized_output)
        key = cv2.waitKey(0)
        if key == 27:  # ESC 鍵：退出程序
            cv2.destroyAllWindows()
            # Return None to signal stop request
            return None
        elif key != ord('s'):  # 非 S 鍵：跳過保存
            print("⏭️ 跳過保存")
            # Return None to signal skip
            return None
    # Remove save_to_json call
    # save_to_json(data, output_json_path) # Removed

    print(f"✅ 處理完成：{image_path}")
    # Return the data dictionary
    return data

def process_folder(input_folder="data", output_img_folder="output", output_json_folder="output_json", show_images=True):
    """批量處理所有圖片（包含子資料夾），並將結果匯總到單個JSON文件"""
    supported_ext = [".jpg", ".jpeg", ".png"]
    processed_count = 0
    skipped_count = 0
    # List to hold data from all processed images
    all_results_data = []

    if not os.path.exists(input_folder):
        os.makedirs(input_folder)
        print(f"已創建輸入資料夾: {input_folder}")
        print(f"請將圖片放入 '{input_folder}' 資料夾（包含子資料夾）後再次運行程序")
        return

    print(f"開始遞迴處理資料夾 '{input_folder}' 中的圖片...")

    for root, _, files in os.walk(input_folder):
        image_files = [f for f in files if os.path.splitext(f)[1].lower() in supported_ext]

        if not image_files:
            continue # Skip directories with no supported images

        for filename in image_files:
            image_path = os.path.join(root, filename)

            # --- 建立對應的輸出路徑 ---
            # 計算相對於 input_folder 的路徑
            relative_dir = os.path.relpath(root, input_folder)
            # 組合 JSON 輸出路徑 - 不再需要為每個文件產生，但保留目錄結構可能有用（雖然現在沒用）
            # current_output_json_dir = os.path.join(output_json_folder, relative_dir) if relative_dir != '.' else output_json_folder
            # json_filename = f"{os.path.splitext(filename)[0]}.json"
            # output_json_path = os.path.join(current_output_json_dir, json_filename)

            # 組合圖片區塊輸出資料夾的基本路徑 (process_image內部會再處理子資料夾)
            current_output_img_dir = output_img_folder # Pass the base image output folder

            # --- 處理圖片 --- Process image and get data dictionary (or None)
            image_data = process_image(image_path, current_output_img_dir, show_images)

            if image_data is not None: # Successfully processed
                all_results_data.append(image_data)
                processed_count += 1
            elif not all(parse_filename(image_path)): # Check if skipped due to filename format
                 skipped_count += 1
            elif show_images: # Check if stopped by user (ESC key) or skipped via 's' key
                 # If process_image returned None and show_images is True, it was likely ESC or 's'
                 # Check if it was ESC specifically (user stop request)
                 # This logic might be imperfect as 's' skip also returns None
                 # A more robust way would be for process_image to return a specific value for ESC.
                 # Assuming any None return with show_images=True means user interaction caused stop/skip.
                 print("使用者要求停止或跳過處理。")
                 if show_images:
                     cv2.destroyAllWindows()
                 # Save collected results so far before exiting due to user request
                 final_output_json_path = os.path.join(output_json_folder, "all_results.json")
                 if all_results_data:
                     save_all_results_to_json(all_results_data, final_output_json_path)
                     print(f"\n已將目前收集到的 {len(all_results_data)} 筆結果保存至: {final_output_json_path}")
                 else:
                     print("\n沒有收集到任何結果可保存。")
                 print(f"處理總結：成功 {processed_count} 張，因檔名格式錯誤跳過 {skipped_count} 張。")
                 return # Stop processing immediately

    # After processing all files (or if loop finishes without user interruption)
    if show_images:
        cv2.destroyAllWindows()

    # Define the final single JSON output path
    final_output_json_path = os.path.join(output_json_folder, "all_results.json")

    # Save all collected results to the single JSON file
    if all_results_data:
        save_all_results_to_json(all_results_data, final_output_json_path)
        print(f"\n批次處理完成! 所有 {len(all_results_data)} 筆結果已保存至: {final_output_json_path}")
    else:
        print("\n批次處理完成! 沒有處理成功的圖片可保存。")

    print(f"處理總結：成功 {processed_count} 張，因檔名格式錯誤跳過 {skipped_count} 張。")

if __name__ == "__main__":

    # --- 實際處理流程 ---
    input_folder = "data"           # 放置已重新命名圖片的資料夾 (包含子資料夾)
    output_img_folder = "output"    # 處理後圖片區塊將儲存在此資料夾
    output_json_folder = "output_json"  # 輸出 JSON 將儲存在此資料夾
    for folder in [input_folder, output_img_folder, output_json_folder]:
        os.makedirs(folder, exist_ok=True)
    show_images = False  # 設為 False 可關閉圖片顯示，直接批量處理
    process_folder(input_folder, output_img_folder, output_json_folder, show_images) # Re-enabled processing