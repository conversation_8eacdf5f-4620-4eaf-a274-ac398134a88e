2025-05-29 10:47:20,312 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 10:47:20,313 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-05-29 10:47:20,314 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 10:47:25,068 - rag_processor - INFO - ✅ 向量化模型載入成功
2025-05-29 10:47:25,106 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-29 10:47:25,692 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 10:47:25,692 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 10:47:25,693 - rag_processor - INFO - 📚 開始批量處理 13 個PDF檔案
2025-05-29 10:47:25,693 - rag_processor - INFO - 📖 正在處理: 1-1.pdf
2025-05-29 10:47:25,699 - pikepdf._core - INFO - pikepdf C++ to Python logger bridge initialized
2025-05-29 10:47:25,769 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 10:47:36,979 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\1-1.pdf ...
2025-05-29 12:44:24,668 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 12:44:24,669 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 12:44:24,744 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 12:44:24,745 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 12:44:24,745 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 12:44:24,746 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 12:44:29,100 - rag_processor - INFO - ✅ 向量化模型載入成功
2025-05-29 12:44:29,112 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-29 12:44:29,240 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 12:44:29,240 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 12:44:29,427 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 12:44:29,427 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 12:44:29,427 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 12:44:29,427 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 12:44:29,428 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 12:44:29,429 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 12:44:32,870 - rag_processor - INFO - ✅ 向量化模型載入成功
2025-05-29 12:44:32,900 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 12:44:32,900 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 12:44:32,901 - rag_ai_responder - WARNING - ⚠️ 未找到現有的向量資料庫，請先建立知識庫
2025-05-29 12:44:32,901 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-29 12:44:32,901 - rag_ai_responder - INFO - 🌐 語言已切換為: English
2025-05-29 12:45:48,789 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 12:45:48,789 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 12:45:48,877 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 12:45:48,878 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 12:45:48,878 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 12:45:48,879 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 12:45:52,821 - rag_processor - INFO - ✅ 向量化模型載入成功
2025-05-29 12:45:52,833 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-29 12:45:52,984 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 12:45:52,984 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 12:45:52,985 - rag_processor - INFO - 📚 開始批量處理 13 個PDF檔案
2025-05-29 12:45:52,986 - rag_processor - INFO - 📖 正在處理: 1-1.pdf
2025-05-29 12:45:52,989 - pikepdf._core - INFO - pikepdf C++ to Python logger bridge initialized
2025-05-29 12:45:53,019 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 12:46:05,169 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\1-1.pdf ...
2025-05-29 12:48:29,453 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 12:48:53,014 - unstructured_inference - INFO - Loading the Table agent ...
2025-05-29 12:48:53,221 - unstructured_inference - INFO - Loading the table structure model ...
2025-05-29 12:48:53,642 - timm.models._builder - INFO - Loading pretrained weights from Hugging Face hub (timm/resnet18.a1_in1k)
2025-05-29 12:48:53,853 - timm.models._hub - INFO - [timm/resnet18.a1_in1k] Safe alternative available for 'pytorch_model.bin' (as 'model.safetensors'). Loading weights using safetensors.
2025-05-29 12:48:53,870 - timm.models._builder - INFO - Missing keys (fc.weight, fc.bias) discovered while loading pretrained weights. This is expected if model is being adapted.
2025-05-29 12:51:01,109 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 12:51:01,109 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 12:51:01,185 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 12:51:01,185 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 12:51:01,185 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 12:51:01,187 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 12:51:05,306 - rag_processor - INFO - ✅ 向量化模型載入成功
2025-05-29 12:51:05,318 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-29 12:51:05,441 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 12:51:05,441 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 12:51:05,441 - rag_processor - INFO - 📚 開始批量處理 13 個PDF檔案
2025-05-29 12:51:05,442 - rag_processor - INFO - 📖 正在處理: 1-1.pdf
2025-05-29 12:51:05,445 - pikepdf._core - INFO - pikepdf C++ to Python logger bridge initialized
2025-05-29 12:51:05,473 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 12:51:16,586 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\1-1.pdf ...
2025-05-29 12:53:38,045 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 12:54:00,634 - unstructured_inference - INFO - Loading the Table agent ...
2025-05-29 12:54:00,832 - unstructured_inference - INFO - Loading the table structure model ...
2025-05-29 12:54:01,261 - timm.models._builder - INFO - Loading pretrained weights from Hugging Face hub (timm/resnet18.a1_in1k)
2025-05-29 12:54:01,459 - timm.models._hub - INFO - [timm/resnet18.a1_in1k] Safe alternative available for 'pytorch_model.bin' (as 'model.safetensors'). Loading weights using safetensors.
2025-05-29 12:54:01,462 - timm.models._builder - INFO - Missing keys (fc.weight, fc.bias) discovered while loading pretrained weights. This is expected if model is being adapted.
2025-05-29 13:00:26,232 - rag_processor - INFO - ✅ 成功處理 29 個內容元素
2025-05-29 13:00:26,236 - rag_processor - INFO - ✅ 成功處理: 1-1.pdf
2025-05-29 13:00:26,246 - rag_processor - INFO - 📖 正在處理: 1.pdf
2025-05-29 13:00:26,292 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:00:34,855 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\1.pdf ...
2025-05-29 13:01:28,889 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:04:19,601 - rag_processor - INFO - ✅ 成功處理 11 個內容元素
2025-05-29 13:04:19,602 - rag_processor - INFO - ✅ 成功處理: 1.pdf
2025-05-29 13:04:19,603 - rag_processor - INFO - 📖 正在處理: 2-2.pdf
2025-05-29 13:04:19,646 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\2-2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:04:26,038 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\2-2.pdf ...
2025-05-29 13:05:43,688 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\2-2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:08:58,638 - rag_processor - INFO - ✅ 成功處理 12 個內容元素
2025-05-29 13:08:58,640 - rag_processor - INFO - ✅ 成功處理: 2-2.pdf
2025-05-29 13:08:58,648 - rag_processor - INFO - 📖 正在處理: 2.pdf
2025-05-29 13:08:58,690 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:09:09,968 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\2.pdf ...
2025-05-29 13:10:20,382 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:13:52,567 - rag_processor - INFO - ✅ 成功處理 13 個內容元素
2025-05-29 13:13:52,570 - rag_processor - INFO - ✅ 成功處理: 2.pdf
2025-05-29 13:13:52,583 - rag_processor - INFO - 📖 正在處理: 3-1.pdf
2025-05-29 13:13:52,611 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:14:02,395 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\3-1.pdf ...
2025-05-29 13:15:21,855 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:19:40,880 - rag_processor - INFO - ✅ 成功處理 15 個內容元素
2025-05-29 13:19:40,882 - rag_processor - INFO - ✅ 成功處理: 3-1.pdf
2025-05-29 13:19:40,882 - rag_processor - INFO - 📖 正在處理: 3-2.pdf
2025-05-29 13:19:40,935 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:20:12,303 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\3-2.pdf ...
2025-05-29 13:22:58,454 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:33:02,355 - rag_processor - INFO - ✅ 成功處理 28 個內容元素
2025-05-29 13:33:02,358 - rag_processor - INFO - ✅ 成功處理: 3-2.pdf
2025-05-29 13:33:02,359 - rag_processor - INFO - 📖 正在處理: 3-3.pdf
2025-05-29 13:33:02,366 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-3.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:33:07,275 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\3-3.pdf ...
2025-05-29 13:33:55,447 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-3.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:36:23,502 - rag_processor - INFO - ✅ 成功處理 8 個內容元素
2025-05-29 13:36:23,504 - rag_processor - INFO - ✅ 成功處理: 3-3.pdf
2025-05-29 13:36:23,504 - rag_processor - INFO - 📖 正在處理: 3.pdf
2025-05-29 13:36:23,558 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:36:35,741 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\3.pdf ...
2025-05-29 13:37:31,757 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:40:33,554 - rag_processor - INFO - ✅ 成功處理 11 個內容元素
2025-05-29 13:40:33,556 - rag_processor - INFO - ✅ 成功處理: 3.pdf
2025-05-29 13:40:33,556 - rag_processor - INFO - 📖 正在處理: 4-1.pdf
2025-05-29 13:40:33,582 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\4-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:40:40,228 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\4-1.pdf ...
2025-05-29 13:41:20,999 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\4-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:43:43,137 - rag_processor - INFO - ✅ 成功處理 8 個內容元素
2025-05-29 13:43:43,137 - rag_processor - INFO - ✅ 成功處理: 4-1.pdf
2025-05-29 13:43:43,138 - rag_processor - INFO - 📖 正在處理: 4.pdf
2025-05-29 13:43:43,170 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\4.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:43:48,285 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\4.pdf ...
2025-05-29 13:44:36,687 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\4.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 13:47:12,510 - rag_processor - INFO - ✅ 成功處理 10 個內容元素
2025-05-29 13:47:12,511 - rag_processor - INFO - ✅ 成功處理: 4.pdf
2025-05-29 13:47:12,512 - rag_processor - INFO - 📖 正在處理: Computer Science An Overview, 13th Edition.pdf
2025-05-29 13:47:54,671 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Computer Science An Overview, 13th Edition.pdf ...
2025-05-29 14:24:24,618 - rag_processor - INFO - ✅ 成功處理 595 個內容元素
2025-05-29 14:24:24,627 - rag_processor - INFO - ✅ 成功處理: Computer Science An Overview, 13th Edition.pdf
2025-05-29 14:24:24,627 - rag_processor - INFO - 📖 正在處理: Introduction to Computing Explorations in Language Logic  and Machines.pdf
2025-05-29 14:24:33,515 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Introduction to Computing Explorations in Language Logic  and Machines.pdf ...
2025-05-29 14:38:25,104 - rag_processor - INFO - ✅ 成功處理 221 個內容元素
2025-05-29 14:38:25,107 - rag_processor - INFO - ✅ 成功處理: Introduction to Computing Explorations in Language Logic  and Machines.pdf
2025-05-29 14:38:25,125 - rag_processor - INFO - 📖 正在處理: Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf
2025-05-29 14:39:08,154 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,226 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,305 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,367 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,430 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,491 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,539 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,600 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,683 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,740 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,804 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,861 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,921 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:08,979 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,046 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,115 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,184 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,249 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,303 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,360 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,434 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,496 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,562 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,616 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,677 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,742 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,797 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,852 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,903 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:09,963 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,027 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,082 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,145 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,225 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,300 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,369 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,428 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,484 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,807 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,862 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,917 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:39:10,960 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf ...
2025-05-29 14:59:16,468 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,529 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,587 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,629 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,674 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,721 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,788 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,849 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,895 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,941 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:16,992 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,048 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,089 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,149 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,193 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,246 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,291 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,344 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,396 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,443 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,505 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,552 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,598 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,644 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,700 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,751 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,798 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,856 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,896 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:17,942 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,212 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,263 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,314 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,365 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,416 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,482 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,532 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,576 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,628 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,697 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 14:59:18,748 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 15:39:55,162 - rag_processor - INFO - ✅ 成功處理 1040 個內容元素
2025-05-29 15:39:55,174 - rag_processor - INFO - ✅ 成功處理: Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf
2025-05-29 15:39:55,243 - rag_processor - INFO - ✅ 結構化數據已保存到: D:\GitHub\MIS_teach_all\rag_system\data\outputs\structured_content.json
2025-05-29 15:39:55,243 - rag_processor - INFO - 📊 總共提取了 2001 個內容塊
2025-05-29 15:39:55,243 - rag_processor - INFO - 📊 批量處理完成: 成功 13 個，失敗 0 個
2025-05-29 15:39:55,243 - rag_processor - INFO - 🧠 正在創建知識點 (最小長度: 100)
2025-05-29 15:39:55,719 - rag_processor - INFO - ✅ 創建了 2001 個知識點
2025-05-29 15:39:55,719 - rag_processor - INFO - 🔧 正在建立向量資料庫 (ChromaDB)
2025-05-29 15:39:56,245 - rag_processor - INFO - 🚀 使用GPU批量處理，批量大小: 64
2025-05-29 15:39:56,248 - rag_processor - INFO - 🔄 正在生成嵌入向量...
2025-05-29 15:40:00,445 - rag_processor - INFO - 🔄 正在添加到ChromaDB...
2025-05-29 15:41:30,394 - rag_processor - INFO - ✅ ChromaDB建立完成，包含 2001 個向量
2025-05-29 15:41:30,394 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 15:41:30,481 - rag_processor - INFO - ✅ 知識點已保存到: D:\GitHub\MIS_teach_all\rag_system\data\outputs\knowledge_points.json
2025-05-29 16:42:47,502 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-29 16:42:52,929 - rag_ai_responder - INFO - 🤔 收到問題: 什麼是作業系統？
2025-05-29 16:42:53,215 - rag_ai_responder - INFO - 🔍 搜索到 0 個相關結果
2025-05-29 16:42:53,216 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 0 個相關結果)
2025-05-29 16:43:17,521 - rag_ai_responder - INFO - 🤔 收到問題: What is deadlock?
2025-05-29 16:43:17,680 - rag_ai_responder - INFO - 🔍 搜索到 0 個相關結果
2025-05-29 16:43:17,680 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 0 個相關結果)
2025-05-29 16:44:51,844 - rag_ai_responder - INFO - 🤔 收到問題: 什麼是作業系統？
2025-05-29 16:44:51,997 - rag_ai_responder - INFO - 🔍 搜索到 0 個相關結果
2025-05-29 16:44:51,997 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 0 個相關結果)
2025-05-29 16:57:50,758 - rag_ai_responder - INFO - 🤔 收到問題: What is deadlock?
2025-05-29 16:57:50,912 - rag_ai_responder - INFO - 🔍 搜索到 0 個相關結果
2025-05-29 16:57:50,912 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 0 個相關結果)
2025-05-29 16:59:02,314 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 16:59:02,315 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 16:59:02,380 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 16:59:02,380 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 16:59:02,380 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 16:59:02,381 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 16:59:08,846 - rag_processor - INFO - 🚀 啟用FP16半精度模式，提升GPU處理速度
2025-05-29 16:59:08,846 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-29 16:59:08,846 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-29 16:59:08,966 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-29 16:59:09,261 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 16:59:09,262 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 16:59:09,431 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 16:59:09,431 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 16:59:09,432 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 16:59:09,432 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 16:59:09,432 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 16:59:09,434 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 16:59:12,657 - rag_processor - INFO - 🚀 啟用FP16半精度模式，提升GPU處理速度
2025-05-29 16:59:12,657 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-29 16:59:12,657 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-29 16:59:12,674 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 16:59:12,675 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 16:59:12,675 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-05-29 16:59:12,675 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-05-29 16:59:12,676 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-29 16:59:14,331 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 16:59:14,331 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 16:59:14,331 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 16:59:14,331 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 16:59:14,331 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 16:59:14,333 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 16:59:17,645 - rag_processor - INFO - 🚀 啟用FP16半精度模式，提升GPU處理速度
2025-05-29 16:59:17,645 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-29 16:59:17,645 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-29 16:59:17,662 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 16:59:17,662 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 17:01:07,031 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 17:01:07,031 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 17:01:07,113 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 17:01:07,113 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 17:01:07,114 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 17:01:07,115 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 17:01:10,912 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-29 17:01:10,912 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-29 17:01:10,922 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-29 17:01:11,065 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 17:01:11,065 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 17:01:11,065 - rag_processor - INFO - 📚 開始批量處理 13 個PDF檔案
2025-05-29 17:01:11,066 - rag_processor - INFO - 📖 正在處理: 1-1.pdf
2025-05-29 17:01:11,263 - pikepdf._core - INFO - pikepdf C++ to Python logger bridge initialized
2025-05-29 17:01:11,397 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:01:22,547 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\1-1.pdf ...
2025-05-29 17:03:39,447 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:04:01,549 - unstructured_inference - INFO - Loading the Table agent ...
2025-05-29 17:04:01,914 - unstructured_inference - INFO - Loading the table structure model ...
2025-05-29 17:04:02,660 - timm.models._builder - INFO - Loading pretrained weights from Hugging Face hub (timm/resnet18.a1_in1k)
2025-05-29 17:04:03,037 - timm.models._hub - INFO - [timm/resnet18.a1_in1k] Safe alternative available for 'pytorch_model.bin' (as 'model.safetensors'). Loading weights using safetensors.
2025-05-29 17:04:03,053 - timm.models._builder - INFO - Missing keys (fc.weight, fc.bias) discovered while loading pretrained weights. This is expected if model is being adapted.
2025-05-29 17:10:21,412 - rag_processor - INFO - ✅ 成功處理 29 個內容元素
2025-05-29 17:10:21,415 - rag_processor - INFO - ✅ 成功處理: 1-1.pdf
2025-05-29 17:10:21,415 - rag_processor - INFO - 📖 正在處理: 1.pdf
2025-05-29 17:10:21,464 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:10:30,699 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\1.pdf ...
2025-05-29 17:11:24,721 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:14:09,831 - rag_processor - INFO - ✅ 成功處理 11 個內容元素
2025-05-29 17:14:09,833 - rag_processor - INFO - ✅ 成功處理: 1.pdf
2025-05-29 17:14:09,834 - rag_processor - INFO - 📖 正在處理: 2-2.pdf
2025-05-29 17:14:09,867 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\2-2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:14:16,282 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\2-2.pdf ...
2025-05-29 17:15:34,873 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\2-2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:18:46,416 - rag_processor - INFO - ✅ 成功處理 12 個內容元素
2025-05-29 17:18:46,418 - rag_processor - INFO - ✅ 成功處理: 2-2.pdf
2025-05-29 17:18:46,419 - rag_processor - INFO - 📖 正在處理: 2.pdf
2025-05-29 17:18:46,451 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:18:58,062 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\2.pdf ...
2025-05-29 17:20:08,862 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:23:39,989 - rag_processor - INFO - ✅ 成功處理 13 個內容元素
2025-05-29 17:23:39,991 - rag_processor - INFO - ✅ 成功處理: 2.pdf
2025-05-29 17:23:39,991 - rag_processor - INFO - 📖 正在處理: 3-1.pdf
2025-05-29 17:23:40,024 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:23:49,949 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\3-1.pdf ...
2025-05-29 17:25:09,737 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:29:26,275 - rag_processor - INFO - ✅ 成功處理 15 個內容元素
2025-05-29 17:29:26,277 - rag_processor - INFO - ✅ 成功處理: 3-1.pdf
2025-05-29 17:29:26,278 - rag_processor - INFO - 📖 正在處理: 3-2.pdf
2025-05-29 17:29:26,339 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:29:59,104 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\3-2.pdf ...
2025-05-29 17:32:45,641 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-2.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:42:57,952 - rag_processor - INFO - ✅ 成功處理 28 個內容元素
2025-05-29 17:42:57,956 - rag_processor - INFO - ✅ 成功處理: 3-2.pdf
2025-05-29 17:42:57,956 - rag_processor - INFO - 📖 正在處理: 3-3.pdf
2025-05-29 17:42:57,995 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-3.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:43:03,316 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\3-3.pdf ...
2025-05-29 17:43:52,888 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3-3.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:46:25,299 - rag_processor - INFO - ✅ 成功處理 8 個內容元素
2025-05-29 17:46:25,300 - rag_processor - INFO - ✅ 成功處理: 3-3.pdf
2025-05-29 17:46:25,300 - rag_processor - INFO - 📖 正在處理: 3.pdf
2025-05-29 17:46:25,365 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:46:38,233 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\3.pdf ...
2025-05-29 17:47:34,197 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\3.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:50:38,406 - rag_processor - INFO - ✅ 成功處理 11 個內容元素
2025-05-29 17:50:38,407 - rag_processor - INFO - ✅ 成功處理: 3.pdf
2025-05-29 17:50:38,408 - rag_processor - INFO - 📖 正在處理: 4-1.pdf
2025-05-29 17:50:38,437 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\4-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:50:45,094 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\4-1.pdf ...
2025-05-29 17:51:26,249 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\4-1.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:53:44,648 - rag_processor - INFO - ✅ 成功處理 8 個內容元素
2025-05-29 17:53:44,649 - rag_processor - INFO - ✅ 成功處理: 4-1.pdf
2025-05-29 17:53:44,649 - rag_processor - INFO - 📖 正在處理: 4.pdf
2025-05-29 17:53:44,691 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\4.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:53:49,836 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\4.pdf ...
2025-05-29 17:54:37,236 - pdfminer.pdfpage - WARNING - The PDF <_io.BufferedReader name='D:\\GitHub\\MIS_teach_all\\rag_system\\data\\pdfs\\4.pdf'> contains a metadata field indicating that it should not allow text extraction. Ignoring this field and proceeding. Use the check_extractable if you want to raise an error in this case
2025-05-29 17:57:17,718 - rag_processor - INFO - ✅ 成功處理 10 個內容元素
2025-05-29 17:57:17,719 - rag_processor - INFO - ✅ 成功處理: 4.pdf
2025-05-29 17:57:17,720 - rag_processor - INFO - 📖 正在處理: Computer Science An Overview, 13th Edition.pdf
2025-05-29 17:57:59,653 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Computer Science An Overview, 13th Edition.pdf ...
2025-05-29 18:32:00,023 - rag_processor - INFO - ✅ 成功處理 595 個內容元素
2025-05-29 18:32:00,033 - rag_processor - INFO - ✅ 成功處理: Computer Science An Overview, 13th Edition.pdf
2025-05-29 18:32:00,034 - rag_processor - INFO - 📖 正在處理: Introduction to Computing Explorations in Language Logic  and Machines.pdf
2025-05-29 18:32:08,906 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Introduction to Computing Explorations in Language Logic  and Machines.pdf ...
2025-05-29 18:46:08,709 - rag_processor - INFO - ✅ 成功處理 221 個內容元素
2025-05-29 18:46:08,713 - rag_processor - INFO - ✅ 成功處理: Introduction to Computing Explorations in Language Logic  and Machines.pdf
2025-05-29 18:46:08,714 - rag_processor - INFO - 📖 正在處理: Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf
2025-05-29 18:46:43,265 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,335 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,393 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,453 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,501 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,565 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,619 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,670 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,726 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,785 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,843 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,901 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:43,959 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,022 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,079 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,140 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,198 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,256 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,310 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,365 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,423 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,478 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,539 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,594 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,654 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,716 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,776 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,836 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,891 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:44,949 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,008 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,066 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,116 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,177 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,231 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,285 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,339 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,397 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,676 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,750 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,810 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 18:46:45,836 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf ...
2025-05-29 19:06:01,574 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,627 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,667 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,712 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,748 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,790 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,833 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,874 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,916 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,957 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:01,998 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,039 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,080 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,127 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,169 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,210 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,252 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,296 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,339 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,382 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,425 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,465 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,510 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,551 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,595 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,637 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,676 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,719 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,759 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:02,802 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,066 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,105 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,145 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,185 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,228 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,269 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,310 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,355 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,397 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,440 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:06:03,479 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 19:44:52,874 - rag_processor - INFO - ✅ 成功處理 1040 個內容元素
2025-05-29 19:44:52,886 - rag_processor - INFO - ✅ 成功處理: Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf
2025-05-29 19:44:52,953 - rag_processor - INFO - ✅ 結構化數據已保存到: D:\GitHub\MIS_teach_all\rag_system\data\outputs\structured_content.json
2025-05-29 19:44:52,954 - rag_processor - INFO - 📊 總共提取了 2001 個內容塊
2025-05-29 19:44:52,954 - rag_processor - INFO - 📊 批量處理完成: 成功 13 個，失敗 0 個
2025-05-29 19:44:52,954 - rag_processor - INFO - 🧠 正在創建知識點 (最小長度: 100)
2025-05-29 19:44:53,370 - rag_processor - INFO - ✅ 創建了 2001 個知識點
2025-05-29 19:44:53,370 - rag_processor - INFO - 🔧 正在建立向量資料庫 (ChromaDB)
2025-05-29 19:44:53,748 - rag_processor - INFO - 🚀 使用GPU批量處理，批量大小: 64
2025-05-29 19:44:53,749 - rag_processor - INFO - 🔄 正在生成嵌入向量...
2025-05-29 19:45:00,783 - rag_processor - INFO - 🔄 正在添加到ChromaDB...
2025-05-29 19:46:53,840 - rag_processor - INFO - ✅ ChromaDB建立完成，包含 2001 個向量
2025-05-29 19:46:53,840 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 19:46:53,930 - rag_processor - INFO - ✅ 知識點已保存到: D:\GitHub\MIS_teach_all\rag_system\data\outputs\knowledge_points.json
2025-05-29 21:13:50,307 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 21:13:50,307 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 21:13:50,387 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 21:13:50,388 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 21:13:50,388 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 21:13:50,389 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 21:13:58,685 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-29 21:13:58,685 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-29 21:13:58,795 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-29 21:13:59,115 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 21:13:59,115 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 21:14:01,246 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 21:14:01,246 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 21:14:01,246 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 21:14:01,246 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 21:14:01,247 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 21:14:01,248 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 21:14:04,809 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-29 21:14:04,810 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-29 21:14:04,828 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 21:14:04,829 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-29 21:14:04,829 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-05-29 21:14:04,830 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-05-29 21:14:04,830 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-29 21:14:06,719 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-29 21:14:06,719 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-29 21:14:06,719 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-29 21:14:06,720 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 21:14:06,720 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-29 21:14:06,721 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-29 21:14:10,507 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-29 21:14:10,507 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-29 21:14:10,530 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-29 21:14:10,531 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-31 10:18:30,418 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-31 10:18:30,428 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-31 10:18:30,482 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-31 10:18:30,482 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:18:30,482 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-31 10:18:30,483 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:18:37,630 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-31 10:18:37,631 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-31 10:18:37,745 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-31 10:18:38,804 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-31 10:18:38,804 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-31 10:18:38,861 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-05-31 10:18:38,861 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-05-31 10:18:38,861 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-31 10:18:48,973 - rag_ai_responder - INFO - 🌐 語言已切換為: English
2025-05-31 10:19:11,715 - rag_ai_responder - INFO - 🤔 收到問題: what is Mass storage?
2025-05-31 10:19:14,079 - rag_ai_responder - INFO - 📊 資料庫包含 4002 個向量
2025-05-31 10:19:14,079 - rag_ai_responder - INFO - 🔍 搜索問題: 'what is Mass storage?' (top_k=10)
2025-05-31 10:19:14,745 - rag_ai_responder - INFO - 🔍 原始搜索結果: 10 個
2025-05-31 10:19:14,746 - rag_ai_responder - INFO -   結果 1: 相似度=0.096, 距離=0.904
2025-05-31 10:19:14,746 - rag_ai_responder - INFO -   結果 2: 相似度=0.096, 距離=0.904
2025-05-31 10:19:14,746 - rag_ai_responder - INFO -   結果 3: 相似度=0.078, 距離=0.922
2025-05-31 10:19:14,746 - rag_ai_responder - INFO - 🔍 過濾後結果: 0 個 (閾值=0.3)
2025-05-31 10:19:14,746 - rag_ai_responder - INFO - 💡 使用最相似的3個結果
2025-05-31 10:19:29,228 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 3 個相關結果)
2025-05-31 10:22:42,851 - rag_ai_responder - INFO - 🤔 收到問題: What is the difference between primary storage and secondary storage
2025-05-31 10:22:42,852 - rag_ai_responder - INFO - 📊 資料庫包含 4002 個向量
2025-05-31 10:22:42,852 - rag_ai_responder - INFO - 🔍 搜索問題: 'What is the difference between primary storage and secondary storage' (top_k=10)
2025-05-31 10:22:43,044 - rag_ai_responder - INFO - 🔍 原始搜索結果: 10 個
2025-05-31 10:22:43,044 - rag_ai_responder - INFO -   結果 1: 相似度=0.183, 距離=0.817
2025-05-31 10:22:43,044 - rag_ai_responder - INFO -   結果 2: 相似度=0.183, 距離=0.817
2025-05-31 10:22:43,044 - rag_ai_responder - INFO -   結果 3: 相似度=0.133, 距離=0.867
2025-05-31 10:22:43,044 - rag_ai_responder - INFO - 🔍 過濾後結果: 0 個 (閾值=0.3)
2025-05-31 10:22:43,044 - rag_ai_responder - INFO - 💡 使用最相似的3個結果
2025-05-31 10:22:50,368 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 3 個相關結果)
2025-05-31 10:25:07,234 - rag_ai_responder - INFO - 🤔 收到問題: What Operating Systems Do
2025-05-31 10:25:07,236 - rag_ai_responder - INFO - 📊 資料庫包含 4002 個向量
2025-05-31 10:25:07,236 - rag_ai_responder - INFO - 🔍 搜索問題: 'What Operating Systems Do' (top_k=10)
2025-05-31 10:25:07,463 - rag_ai_responder - INFO - 🔍 原始搜索結果: 10 個
2025-05-31 10:25:07,464 - rag_ai_responder - INFO -   結果 1: 相似度=0.489, 距離=0.511
2025-05-31 10:25:07,464 - rag_ai_responder - INFO -   結果 2: 相似度=0.489, 距離=0.511
2025-05-31 10:25:07,464 - rag_ai_responder - INFO -   結果 3: 相似度=0.462, 距離=0.538
2025-05-31 10:25:07,464 - rag_ai_responder - INFO - 🔍 過濾後結果: 10 個 (閾值=0.3)
2025-05-31 10:25:14,182 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 10 個相關結果)
2025-05-31 10:26:50,392 - rag_ai_responder - INFO - 🤔 收到問題: Computer-System Architecture
2025-05-31 10:26:50,393 - rag_ai_responder - INFO - 📊 資料庫包含 4002 個向量
2025-05-31 10:26:50,393 - rag_ai_responder - INFO - 🔍 搜索問題: 'Computer-System Architecture' (top_k=10)
2025-05-31 10:26:50,622 - rag_ai_responder - INFO - 🔍 原始搜索結果: 10 個
2025-05-31 10:26:50,622 - rag_ai_responder - INFO -   結果 1: 相似度=0.310, 距離=0.690
2025-05-31 10:26:50,623 - rag_ai_responder - INFO -   結果 2: 相似度=0.310, 距離=0.690
2025-05-31 10:26:50,624 - rag_ai_responder - INFO -   結果 3: 相似度=0.273, 距離=0.727
2025-05-31 10:26:50,624 - rag_ai_responder - INFO - 🔍 過濾後結果: 2 個 (閾值=0.3)
2025-05-31 10:27:00,304 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 2 個相關結果)
2025-05-31 10:45:49,118 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-31 10:45:49,119 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-31 10:45:49,174 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-31 10:45:49,174 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:45:49,174 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-31 10:45:49,175 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:45:52,996 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-31 10:45:52,997 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-31 10:45:53,007 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-31 10:45:53,157 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-31 10:45:53,157 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-31 10:45:53,158 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-05-31 10:45:53,158 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-05-31 10:45:53,158 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-31 10:46:05,677 - rag_ai_responder - INFO - 🌐 語言已切換為: English
2025-05-31 10:47:02,081 - rag_ai_responder - INFO - 🤔 收到問題: what is operating system ?
2025-05-31 10:47:02,172 - rag_ai_responder - INFO - 📊 資料庫包含 4002 個向量
2025-05-31 10:47:02,172 - rag_ai_responder - INFO - 🔍 搜索問題: 'what is operating system ?' (top_k=15)
2025-05-31 10:47:02,420 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 10:47:02,420 - rag_ai_responder - INFO -   結果 1: 相似度=0.490, 距離=0.510
2025-05-31 10:47:02,420 - rag_ai_responder - INFO -   結果 2: 相似度=0.490, 距離=0.510
2025-05-31 10:47:02,420 - rag_ai_responder - INFO -   結果 3: 相似度=0.489, 距離=0.511
2025-05-31 10:47:02,420 - rag_ai_responder - INFO -   結果 4: 相似度=0.489, 距離=0.511
2025-05-31 10:47:02,421 - rag_ai_responder - INFO -   結果 5: 相似度=0.469, 距離=0.531
2025-05-31 10:47:02,421 - rag_ai_responder - INFO - 🔍 過濾後結果: 15 個 (閾值=0.2)
2025-05-31 10:47:02,421 - rag_ai_responder - ERROR - ❌ 生成AI回答失敗: 'detailed_answer'
2025-05-31 10:47:02,421 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 15 個相關結果)
2025-05-31 10:48:52,206 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-31 10:48:52,206 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-31 10:48:52,277 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-31 10:48:52,277 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:48:52,277 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-31 10:48:52,279 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:48:56,535 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-31 10:48:56,535 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-31 10:48:56,546 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-31 10:48:56,681 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-31 10:48:56,681 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-31 10:48:56,682 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-05-31 10:48:56,682 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-05-31 10:48:56,682 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-31 10:49:02,543 - rag_ai_responder - INFO - 🌐 語言已切換為: English
2025-05-31 10:49:22,712 - rag_ai_responder - INFO - 🤔 收到問題: what is operating system?
2025-05-31 10:49:22,819 - rag_ai_responder - INFO - 📊 資料庫包含 4002 個向量
2025-05-31 10:49:22,819 - rag_ai_responder - INFO - 🔍 搜索問題: 'what is operating system?' (top_k=15)
2025-05-31 10:49:23,012 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 10:49:23,012 - rag_ai_responder - INFO -   結果 1: 相似度=0.490, 距離=0.510
2025-05-31 10:49:23,012 - rag_ai_responder - INFO -   結果 2: 相似度=0.490, 距離=0.510
2025-05-31 10:49:23,012 - rag_ai_responder - INFO -   結果 3: 相似度=0.489, 距離=0.511
2025-05-31 10:49:23,012 - rag_ai_responder - INFO -   結果 4: 相似度=0.489, 距離=0.511
2025-05-31 10:49:23,012 - rag_ai_responder - INFO -   結果 5: 相似度=0.469, 距離=0.531
2025-05-31 10:49:23,012 - rag_ai_responder - INFO - 🔍 過濾後結果: 15 個 (閾值=0.2)
2025-05-31 10:49:23,012 - rag_ai_responder - ERROR - ❌ 生成AI回答失敗: 'detailed_answer'
2025-05-31 10:49:23,013 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 15 個相關結果)
2025-05-31 10:54:15,920 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-31 10:54:15,921 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-31 10:54:15,973 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-31 10:54:15,973 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:54:15,973 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-31 10:54:15,975 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:54:19,999 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-31 10:54:19,999 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-31 10:54:20,010 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-31 10:54:20,159 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-31 10:54:20,159 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-31 10:54:20,160 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-05-31 10:54:20,160 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-05-31 10:54:20,160 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-31 10:54:28,006 - rag_ai_responder - INFO - 🌐 語言已切換為: English
2025-05-31 10:54:45,941 - rag_ai_responder - INFO - 🤔 收到問題: what is operating system?
2025-05-31 10:54:46,027 - rag_ai_responder - INFO - 📊 資料庫包含 4002 個向量
2025-05-31 10:54:46,027 - rag_ai_responder - INFO - 🔍 搜索問題: 'what is operating system?' (top_k=15)
2025-05-31 10:54:46,202 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 10:54:46,202 - rag_ai_responder - INFO -   結果 1: 相似度=0.490, 距離=0.510
2025-05-31 10:54:46,202 - rag_ai_responder - INFO -   結果 2: 相似度=0.490, 距離=0.510
2025-05-31 10:54:46,202 - rag_ai_responder - INFO -   結果 3: 相似度=0.489, 距離=0.511
2025-05-31 10:54:46,203 - rag_ai_responder - INFO -   結果 4: 相似度=0.489, 距離=0.511
2025-05-31 10:54:46,203 - rag_ai_responder - INFO -   結果 5: 相似度=0.469, 距離=0.531
2025-05-31 10:54:46,203 - rag_ai_responder - INFO - 🔍 過濾後結果: 15 個 (閾值=0.05)
2025-05-31 10:54:54,317 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 15 個相關結果)
2025-05-31 10:58:51,524 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-31 10:58:51,524 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-31 10:58:51,589 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-31 10:58:51,590 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:58:51,590 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-31 10:58:51,591 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 10:58:56,348 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-31 10:58:56,349 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-31 10:58:56,360 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-31 10:58:56,946 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-31 10:58:56,946 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-31 10:58:56,946 - rag_processor - INFO - 📚 開始批量處理 3 個PDF檔案
2025-05-31 10:58:56,947 - rag_processor - INFO - 📖 正在處理: Computer Science An Overview, 13th Edition.pdf
2025-05-31 10:58:57,154 - pikepdf._core - INFO - pikepdf C++ to Python logger bridge initialized
2025-05-31 10:59:38,606 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Computer Science An Overview, 13th Edition.pdf ...
2025-05-31 11:12:31,060 - unstructured_inference - INFO - Loading the Table agent ...
2025-05-31 11:12:31,336 - unstructured_inference - INFO - Loading the table structure model ...
2025-05-31 11:12:33,167 - timm.models._builder - INFO - Loading pretrained weights from Hugging Face hub (timm/resnet18.a1_in1k)
2025-05-31 11:12:33,381 - timm.models._hub - INFO - [timm/resnet18.a1_in1k] Safe alternative available for 'pytorch_model.bin' (as 'model.safetensors'). Loading weights using safetensors.
2025-05-31 11:12:33,501 - timm.models._builder - INFO - Missing keys (fc.weight, fc.bias) discovered while loading pretrained weights. This is expected if model is being adapted.
2025-05-31 11:34:17,439 - rag_processor - INFO - ✅ 成功處理 4519 個內容元素
2025-05-31 11:34:17,454 - rag_processor - INFO - ✅ 成功處理: Computer Science An Overview, 13th Edition.pdf
2025-05-31 11:34:17,454 - rag_processor - INFO - 📖 正在處理: Introduction to Computing Explorations in Language Logic  and Machines.pdf
2025-05-31 11:34:36,198 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Introduction to Computing Explorations in Language Logic  and Machines.pdf ...
2025-05-31 11:48:54,014 - rag_processor - INFO - ✅ 成功處理 1687 個內容元素
2025-05-31 11:48:54,020 - rag_processor - INFO - ✅ 成功處理: Introduction to Computing Explorations in Language Logic  and Machines.pdf
2025-05-31 11:48:54,021 - rag_processor - INFO - 📖 正在處理: Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf
2025-05-31 11:49:31,645 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:31,721 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:31,787 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:31,856 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:31,910 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:31,973 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,032 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,083 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,143 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,203 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,269 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,335 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,407 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,479 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,542 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,824 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,879 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,936 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:32,989 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,045 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,103 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,158 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,215 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,269 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,330 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,390 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,450 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,515 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,591 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,691 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,755 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,820 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,883 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:33,947 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:34,012 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:34,070 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:34,130 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:34,186 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:34,248 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:34,306 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:34,361 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 11:49:34,386 - unstructured_inference - INFO - Reading PDF for file: D:\GitHub\MIS_teach_all\rag_system\data\pdfs\Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf ...
2025-05-31 12:08:31,948 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,000 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,040 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,301 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,341 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,385 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,423 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,467 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,506 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,546 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,588 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,628 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,672 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,717 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,759 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,799 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,842 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,882 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,923 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:32,966 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,008 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,049 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,092 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,133 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,175 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,217 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,259 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,302 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,342 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,385 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,429 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,469 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,507 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,548 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,588 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,627 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,668 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,709 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,756 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,794 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:08:33,835 - pdfminer.pdfpage - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-31 12:46:53,597 - rag_processor - INFO - ✅ 成功處理 8222 個內容元素
2025-05-31 12:46:53,619 - rag_processor - INFO - ✅ 成功處理: Operating System Concepts (Abraham Silberschatz, Greg Gagne etc.) (Z-Library).pdf
2025-05-31 12:46:53,798 - rag_processor - INFO - ✅ 結構化數據已保存到: D:\GitHub\MIS_teach_all\rag_system\data\outputs\structured_content.json
2025-05-31 12:46:53,798 - rag_processor - INFO - 📊 總共提取了 14428 個內容塊
2025-05-31 12:46:53,798 - rag_processor - INFO - 📊 批量處理完成: 成功 3 個，失敗 0 個
2025-05-31 12:46:53,798 - rag_processor - INFO - 🧠 正在創建知識點 (最小長度: 100)
2025-05-31 12:46:54,173 - rag_processor - INFO - ✅ 創建了 14428 個知識點
2025-05-31 12:46:54,173 - rag_processor - INFO - 🔧 正在建立向量資料庫 (ChromaDB)
2025-05-31 12:46:54,719 - rag_processor - INFO - 🚀 使用GPU批量處理，批量大小: 64
2025-05-31 12:46:54,719 - rag_processor - INFO - 🔄 正在生成嵌入向量...
2025-05-31 12:47:06,066 - rag_processor - INFO - 🔄 正在添加到ChromaDB...
2025-05-31 12:57:04,173 - rag_processor - INFO - ✅ ChromaDB建立完成，包含 14428 個向量
2025-05-31 12:57:04,173 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-31 12:57:04,636 - rag_processor - INFO - ✅ 知識點已保存到: D:\GitHub\MIS_teach_all\rag_system\data\outputs\knowledge_points.json
2025-05-31 14:14:49,430 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: English)
2025-05-31 14:17:54,798 - rag_ai_responder - INFO - 🤔 收到問題: what operating system do?
2025-05-31 14:17:54,846 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-05-31 14:17:54,846 - rag_ai_responder - INFO - 🔍 搜索問題: 'what operating system do?' (top_k=15)
2025-05-31 14:17:55,041 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 14:17:55,041 - rag_ai_responder - INFO -   結果 1: 相似度=0.641, 距離=0.359
2025-05-31 14:17:55,041 - rag_ai_responder - INFO -   結果 2: 相似度=0.574, 距離=0.426
2025-05-31 14:17:55,041 - rag_ai_responder - INFO -   結果 3: 相似度=0.552, 距離=0.448
2025-05-31 14:17:55,041 - rag_ai_responder - INFO -   結果 4: 相似度=0.543, 距離=0.457
2025-05-31 14:17:55,041 - rag_ai_responder - INFO -   結果 5: 相似度=0.501, 距離=0.499
2025-05-31 14:17:55,041 - rag_ai_responder - INFO - 🔍 過濾後結果: 15 個 (閾值=0.09)
2025-05-31 14:18:05,567 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 15 個相關結果)
2025-05-31 14:20:44,765 - rag_ai_responder - INFO - 🤔 收到問題: what is DMA?
2025-05-31 14:20:44,766 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-05-31 14:20:44,766 - rag_ai_responder - INFO - 🔍 搜索問題: 'what is DMA?' (top_k=15)
2025-05-31 14:20:44,912 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 14:20:44,912 - rag_ai_responder - INFO -   結果 1: 相似度=0.166, 距離=0.834
2025-05-31 14:20:44,912 - rag_ai_responder - INFO -   結果 2: 相似度=0.164, 距離=0.836
2025-05-31 14:20:44,912 - rag_ai_responder - INFO -   結果 3: 相似度=0.115, 距離=0.885
2025-05-31 14:20:44,912 - rag_ai_responder - INFO -   結果 4: 相似度=0.057, 距離=0.943
2025-05-31 14:20:44,912 - rag_ai_responder - INFO -   結果 5: 相似度=0.024, 距離=0.976
2025-05-31 14:20:44,912 - rag_ai_responder - INFO - 🔍 過濾後結果: 3 個 (閾值=0.09)
2025-05-31 14:20:44,913 - rag_ai_responder - INFO - 💡 結果數量不足，補充到5個
2025-05-31 14:20:50,779 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 5 個相關結果)
2025-05-31 14:23:18,227 - rag_ai_responder - INFO - 🤔 收到問題: what is direct memory access ?
2025-05-31 14:23:18,229 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-05-31 14:23:18,229 - rag_ai_responder - INFO - 🔍 搜索問題: 'what is direct memory access ?' (top_k=15)
2025-05-31 14:23:18,424 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 14:23:18,424 - rag_ai_responder - INFO -   結果 1: 相似度=0.480, 距離=0.520
2025-05-31 14:23:18,425 - rag_ai_responder - INFO -   結果 2: 相似度=0.427, 距離=0.573
2025-05-31 14:23:18,425 - rag_ai_responder - INFO -   結果 3: 相似度=0.413, 距離=0.587
2025-05-31 14:23:18,425 - rag_ai_responder - INFO -   結果 4: 相似度=0.301, 距離=0.699
2025-05-31 14:23:18,425 - rag_ai_responder - INFO -   結果 5: 相似度=0.283, 距離=0.717
2025-05-31 14:23:18,425 - rag_ai_responder - INFO - 🔍 過濾後結果: 15 個 (閾值=0.09)
2025-05-31 14:23:25,271 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 15 個相關結果)
2025-05-31 14:29:41,564 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-05-31 14:29:41,564 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-05-31 14:29:41,640 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-05-31 14:29:41,641 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 14:29:41,641 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-05-31 14:29:41,642 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-31 14:29:47,260 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-05-31 14:29:47,261 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-05-31 14:29:47,309 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-05-31 14:29:47,475 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-05-31 14:29:47,475 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-05-31 14:29:47,486 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-05-31 14:29:47,486 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-05-31 14:29:47,486 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-05-31 14:29:54,067 - rag_ai_responder - INFO - 🤔 收到問題: 你是誰?
2025-05-31 14:29:54,140 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-05-31 14:29:54,141 - rag_ai_responder - INFO - 🔍 搜索問題: '你是誰?' (top_k=15)
2025-05-31 14:29:54,315 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 14:29:54,315 - rag_ai_responder - INFO -   結果 1: 相似度=0.000, 距離=1.509
2025-05-31 14:29:54,315 - rag_ai_responder - INFO -   結果 2: 相似度=0.000, 距離=1.522
2025-05-31 14:29:54,315 - rag_ai_responder - INFO -   結果 3: 相似度=0.000, 距離=1.528
2025-05-31 14:29:54,315 - rag_ai_responder - INFO -   結果 4: 相似度=0.000, 距離=1.531
2025-05-31 14:29:54,315 - rag_ai_responder - INFO -   結果 5: 相似度=0.000, 距離=1.577
2025-05-31 14:29:54,315 - rag_ai_responder - INFO - 🔍 過濾後結果: 0 個 (閾值=0.09)
2025-05-31 14:29:54,315 - rag_ai_responder - INFO - 💡 閾值過濾後無結果，使用最相似的5個結果
2025-05-31 14:30:02,309 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 5 個相關結果)
2025-05-31 14:30:21,650 - rag_ai_responder - INFO - 🤔 收到問題: 你能做什麼?
2025-05-31 14:30:21,653 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-05-31 14:30:21,653 - rag_ai_responder - INFO - 🔍 搜索問題: '你能做什麼?' (top_k=15)
2025-05-31 14:30:21,800 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 14:30:21,800 - rag_ai_responder - INFO -   結果 1: 相似度=0.000, 距離=1.436
2025-05-31 14:30:21,800 - rag_ai_responder - INFO -   結果 2: 相似度=0.000, 距離=1.500
2025-05-31 14:30:21,800 - rag_ai_responder - INFO -   結果 3: 相似度=0.000, 距離=1.518
2025-05-31 14:30:21,801 - rag_ai_responder - INFO -   結果 4: 相似度=0.000, 距離=1.538
2025-05-31 14:30:21,801 - rag_ai_responder - INFO -   結果 5: 相似度=0.000, 距離=1.539
2025-05-31 14:30:21,801 - rag_ai_responder - INFO - 🔍 過濾後結果: 0 個 (閾值=0.09)
2025-05-31 14:30:21,801 - rag_ai_responder - INFO - 💡 閾值過濾後無結果，使用最相似的5個結果
2025-05-31 14:30:26,740 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 5 個相關結果)
2025-05-31 14:30:54,905 - rag_ai_responder - INFO - 🤔 收到問題: 你的功能有什麼?
2025-05-31 14:30:54,908 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-05-31 14:30:54,908 - rag_ai_responder - INFO - 🔍 搜索問題: '你的功能有什麼?' (top_k=15)
2025-05-31 14:30:55,058 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-05-31 14:30:55,058 - rag_ai_responder - INFO -   結果 1: 相似度=0.000, 距離=1.475
2025-05-31 14:30:55,058 - rag_ai_responder - INFO -   結果 2: 相似度=0.000, 距離=1.482
2025-05-31 14:30:55,059 - rag_ai_responder - INFO -   結果 3: 相似度=0.000, 距離=1.544
2025-05-31 14:30:55,059 - rag_ai_responder - INFO -   結果 4: 相似度=0.000, 距離=1.566
2025-05-31 14:30:55,059 - rag_ai_responder - INFO -   結果 5: 相似度=0.000, 距離=1.569
2025-05-31 14:30:55,059 - rag_ai_responder - INFO - 🔍 過濾後結果: 0 個 (閾值=0.09)
2025-05-31 14:30:55,059 - rag_ai_responder - INFO - 💡 閾值過濾後無結果，使用最相似的5個結果
2025-05-31 14:31:01,541 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 5 個相關結果)
2025-06-01 14:41:02,894 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 14:41:02,895 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 14:41:02,977 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 14:41:02,978 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 14:41:02,978 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 14:41:02,979 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 14:41:09,720 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 14:41:09,720 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 14:41:09,838 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-01 14:41:10,738 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 14:41:10,738 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 14:41:10,795 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 14:41:10,795 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 14:41:10,795 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 14:41:45,786 - rag_ai_responder - INFO - 🤔 收到問題: 你是誰?你可以做什麼?你目前有哪些資料庫資料?
2025-06-01 14:41:46,588 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-06-01 14:41:46,588 - rag_ai_responder - INFO - 🔍 搜索問題: '你是誰?你可以做什麼?你目前有哪些資料庫資料?' (top_k=15)
2025-06-01 14:41:47,146 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-06-01 14:41:47,146 - rag_ai_responder - INFO -   結果 1: 相似度=0.000, 距離=1.454
2025-06-01 14:41:47,146 - rag_ai_responder - INFO -   結果 2: 相似度=0.000, 距離=1.476
2025-06-01 14:41:47,147 - rag_ai_responder - INFO -   結果 3: 相似度=0.000, 距離=1.542
2025-06-01 14:41:47,147 - rag_ai_responder - INFO -   結果 4: 相似度=0.000, 距離=1.543
2025-06-01 14:41:47,147 - rag_ai_responder - INFO -   結果 5: 相似度=0.000, 距離=1.545
2025-06-01 14:41:47,147 - rag_ai_responder - INFO - 🔍 過濾後結果: 0 個 (閾值=0.09)
2025-06-01 14:41:47,147 - rag_ai_responder - INFO - 💡 閾值過濾後無結果，使用最相似的5個結果
2025-06-01 14:42:00,891 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 5 個相關結果)
2025-06-01 14:45:38,113 - rag_ai_responder - INFO - 🤔 收到問題: 甚麼是銀行家演算法?
2025-06-01 14:45:38,127 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-06-01 14:45:38,128 - rag_ai_responder - INFO - 🔍 搜索問題: '甚麼是銀行家演算法?' (top_k=15)
2025-06-01 14:45:38,372 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-06-01 14:45:38,373 - rag_ai_responder - INFO -   結果 1: 相似度=0.000, 距離=1.304
2025-06-01 14:45:38,373 - rag_ai_responder - INFO -   結果 2: 相似度=0.000, 距離=1.369
2025-06-01 14:45:38,373 - rag_ai_responder - INFO -   結果 3: 相似度=0.000, 距離=1.406
2025-06-01 14:45:38,373 - rag_ai_responder - INFO -   結果 4: 相似度=0.000, 距離=1.421
2025-06-01 14:45:38,373 - rag_ai_responder - INFO -   結果 5: 相似度=0.000, 距離=1.427
2025-06-01 14:45:38,373 - rag_ai_responder - INFO - 🔍 過濾後結果: 0 個 (閾值=0.09)
2025-06-01 14:45:38,373 - rag_ai_responder - INFO - 💡 閾值過濾後無結果，使用最相似的5個結果
2025-06-01 14:45:45,556 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 5 個相關結果)
2025-06-01 14:51:50,494 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 14:51:50,494 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 14:51:50,567 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 14:51:50,567 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 14:51:50,568 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 14:51:50,569 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 14:51:54,287 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 14:51:54,287 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 14:51:54,299 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-01 14:51:54,470 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 14:51:54,471 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 14:51:54,472 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 14:51:54,472 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 14:51:54,472 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 14:52:02,807 - rag_ai_responder - INFO - 🌐 語言已切換為: English
2025-06-01 14:52:19,718 - rag_ai_responder - INFO - 🤔 收到問題: what is Banker's Algorithm?
2025-06-01 14:52:19,773 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-06-01 14:52:19,773 - rag_ai_responder - INFO - 🔍 搜索問題: 'what is Banker's Algorithm?' (top_k=15)
2025-06-01 14:52:19,975 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-06-01 14:52:19,976 - rag_ai_responder - INFO -   結果 1: 相似度=0.440, 距離=0.560
2025-06-01 14:52:19,976 - rag_ai_responder - INFO -   結果 2: 相似度=0.309, 距離=0.691
2025-06-01 14:52:19,976 - rag_ai_responder - INFO -   結果 3: 相似度=0.219, 距離=0.781
2025-06-01 14:52:19,976 - rag_ai_responder - INFO -   結果 4: 相似度=0.124, 距離=0.876
2025-06-01 14:52:19,976 - rag_ai_responder - INFO -   結果 5: 相似度=0.116, 距離=0.884
2025-06-01 14:52:19,977 - rag_ai_responder - INFO - 🔍 過濾後結果: 9 個 (閾值=0.09)
2025-06-01 14:52:27,598 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 9 個相關結果)
2025-06-01 15:00:14,821 - rag_ai_responder - INFO - 🤔 收到問題: python test_ai_analysis.py
2025-06-01 15:00:14,823 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-06-01 15:00:14,823 - rag_ai_responder - INFO - 🔍 搜索問題: 'python test_ai_analysis.py' (top_k=15)
2025-06-01 15:00:15,018 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-06-01 15:00:15,018 - rag_ai_responder - INFO -   結果 1: 相似度=0.095, 距離=0.905
2025-06-01 15:00:15,018 - rag_ai_responder - INFO -   結果 2: 相似度=0.000, 距離=1.066
2025-06-01 15:00:15,018 - rag_ai_responder - INFO -   結果 3: 相似度=0.000, 距離=1.067
2025-06-01 15:00:15,019 - rag_ai_responder - INFO -   結果 4: 相似度=0.000, 距離=1.106
2025-06-01 15:00:15,019 - rag_ai_responder - INFO -   結果 5: 相似度=0.000, 距離=1.115
2025-06-01 15:00:15,019 - rag_ai_responder - INFO - 🔍 過濾後結果: 1 個 (閾值=0.09)
2025-06-01 15:00:15,019 - rag_ai_responder - INFO - 💡 結果數量不足，補充到5個
2025-06-01 15:00:23,557 - rag_ai_responder - INFO - ✅ 回答生成完成 (搜索到 5 個相關結果)
2025-06-01 15:01:36,085 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:01:36,086 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:01:36,151 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:01:36,152 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:01:36,152 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:01:36,153 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:01:40,242 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:01:40,243 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:01:40,253 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-01 15:01:40,401 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:01:40,402 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:01:40,402 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 15:01:40,402 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 15:01:40,403 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 15:01:42,454 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:01:42,454 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:01:42,454 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:01:42,454 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:01:42,455 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:01:42,456 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:01:45,619 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:01:45,619 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:01:45,634 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:01:45,635 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:01:45,635 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 15:01:45,635 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 15:01:45,636 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 15:03:00,050 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:03:00,050 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:03:00,050 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:03:00,051 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:03:00,051 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:03:00,052 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:03:03,328 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:03:03,328 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:03:03,346 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:03:03,346 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:03:03,347 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 15:03:03,347 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 15:03:03,347 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 15:03:16,376 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:03:16,376 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:03:16,376 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:03:16,376 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:03:16,376 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:03:16,377 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:03:19,809 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:03:19,809 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:03:19,826 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:03:19,826 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:03:19,827 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 15:03:19,827 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 15:03:19,827 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 15:03:24,134 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-06-01 15:03:24,135 - rag_ai_responder - INFO - 🔍 搜索問題: '什麼是銀行家演算法？' (top_k=15)
2025-06-01 15:03:24,335 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-06-01 15:03:24,335 - rag_ai_responder - INFO -   結果 1: 相似度=0.000, 距離=1.294
2025-06-01 15:03:24,335 - rag_ai_responder - INFO -   結果 2: 相似度=0.000, 距離=1.391
2025-06-01 15:03:24,335 - rag_ai_responder - INFO -   結果 3: 相似度=0.000, 距離=1.423
2025-06-01 15:03:24,335 - rag_ai_responder - INFO -   結果 4: 相似度=0.000, 距離=1.430
2025-06-01 15:03:24,335 - rag_ai_responder - INFO -   結果 5: 相似度=0.000, 距離=1.449
2025-06-01 15:03:24,335 - rag_ai_responder - INFO - 🔍 過濾後結果: 0 個 (閾值=0.09)
2025-06-01 15:03:24,336 - rag_ai_responder - INFO - 💡 閾值過濾後無結果，使用最相似的5個結果
2025-06-01 15:15:58,792 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:15:58,792 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:15:58,864 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:15:58,864 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:15:58,864 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:15:58,866 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:16:03,339 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:16:03,339 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:16:03,350 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-01 15:16:03,536 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:16:03,536 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:16:03,537 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 15:16:03,537 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 15:16:03,537 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 15:16:31,518 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:16:31,518 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:16:31,570 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:16:31,570 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:16:31,571 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:16:31,572 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:16:35,304 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:16:35,305 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:16:35,315 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-01 15:16:35,464 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:16:35,464 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:16:35,465 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 15:16:35,465 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 15:16:35,465 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 15:20:15,410 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:20:15,410 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:20:15,479 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:20:15,479 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:20:15,479 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:20:15,480 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:20:19,694 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:20:19,695 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:20:19,707 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-01 15:20:19,847 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:20:19,848 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:22:42,792 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:22:42,792 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:22:42,868 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:22:42,869 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:22:42,869 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:22:42,870 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:22:47,785 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:22:47,786 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:22:47,796 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-01 15:22:47,933 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:22:47,934 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:23:46,202 - rag_processor - INFO - 🚀 使用GPU: NVIDIA GeForce RTX 4060 Ti
2025-06-01 15:23:46,202 - rag_processor - INFO - 💾 GPU記憶體: 15GB 總計, 15GB 可用
2025-06-01 15:23:46,271 - rag_processor - INFO - 🔧 GPU記憶體使用比例設定為: 0.8
2025-06-01 15:23:46,271 - rag_processor - INFO - 🔄 正在載入向量化模型: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:23:46,271 - rag_processor - INFO - 🖥️ 使用設備: cuda
2025-06-01 15:23:46,273 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-06-01 15:23:50,023 - rag_processor - INFO - 📏 設定最大序列長度: 512
2025-06-01 15:23:50,023 - rag_processor - INFO - ✅ 向量化模型載入成功 (GPU優化)
2025-06-01 15:23:50,034 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-01 15:23:50,156 - rag_processor - INFO - ✅ ChromaDB初始化成功，路徑: D:\GitHub\MIS_teach_all\rag_system\data\knowledge_db\chroma_db
2025-06-01 15:23:50,157 - rag_processor - INFO - 🚀 RAG處理器初始化完成
2025-06-01 15:23:50,157 - rag_ai_responder - INFO - ✅ 成功載入現有的向量資料庫
2025-06-01 15:23:50,158 - rag_ai_responder - INFO - 🖥️ 向量處理設備: cuda
2025-06-01 15:23:50,158 - rag_ai_responder - INFO - 🤖 AI回答系統初始化完成 (語言: 中文)
2025-06-01 15:24:27,612 - rag_ai_responder - INFO - 📊 資料庫包含 14428 個向量
2025-06-01 15:24:27,612 - rag_ai_responder - INFO - 🔍 搜索問題: 'Banker's algorithm' (top_k=15)
2025-06-01 15:24:27,792 - rag_ai_responder - INFO - 🔍 原始搜索結果: 15 個
2025-06-01 15:24:27,792 - rag_ai_responder - INFO -   結果 1: 相似度=0.457, 距離=0.543
2025-06-01 15:24:27,792 - rag_ai_responder - INFO -   結果 2: 相似度=0.307, 距離=0.693
2025-06-01 15:24:27,793 - rag_ai_responder - INFO -   結果 3: 相似度=0.259, 距離=0.741
2025-06-01 15:24:27,793 - rag_ai_responder - INFO -   結果 4: 相似度=0.245, 距離=0.755
2025-06-01 15:24:27,793 - rag_ai_responder - INFO -   結果 5: 相似度=0.220, 距離=0.780
2025-06-01 15:24:27,793 - rag_ai_responder - INFO - 🔍 過濾後結果: 11 個 (閾值=0.09)
