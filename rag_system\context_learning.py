#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於上下文的學習系統
參考 ai_modal.ipynb 的互動式學習架構
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from context_tutor import ContextTutor

class ContextLearningSystem:
    """基於上下文的學習系統"""
    
    def __init__(self):
        """初始化系統"""
        self.tutor = ContextTutor()
        self.in_conversation = False
        
    def show_welcome(self):
        """顯示歡迎訊息"""
        print("="*80)
        print("🎓 基於上下文的智能學習系統")
        print("="*80)
        print()
        print("🌟 系統特色：")
        print("  • 🎓 真正老師風格，親切自然的語氣")
        print("  • 📝 學生回答後會簡短重新說明重點")
        print("  • 💡 主動補充學生可能遺漏的概念")
        print("  • 🎯 始終記住原始問題，避免偏題")
        print("  • 🔄 循序漸進，每次回應都推進理解")
        print()
        print("💡 使用說明：")
        print("  • 直接提問開始學習")
        print("  • 在引導過程中積極回答")
        print("  • 輸入 'new' 開始新問題")
        print("  • 輸入 'status' 查看學習狀態")
        print("  • 輸入 'quit' 退出系統")
        print()
        print("🎯 設計理念：參考 ai_modal.ipynb 的架構")
        print("="*80)
    
    def handle_special_commands(self, user_input: str) -> bool:
        """處理特殊命令"""
        user_input = user_input.lower().strip()
        
        if user_input in ['quit', 'exit', '退出', '結束']:
            print("\n👋 感謝使用基於上下文的學習系統！")
            print("🎓 希望您的學習之路順利！")
            return True
        
        elif user_input in ['new', '新問題', '新主題', 'reset']:
            self.tutor.reset()
            self.in_conversation = False
            print("\n🆕 已重置，請提出新的問題：")
            return False
        
        elif user_input in ['status', '狀態', '進度']:
            status = self.tutor.get_status()
            print(f"\n{status}")
            return False
        
        elif user_input in ['help', '幫助', '說明']:
            self.show_help()
            return False
        
        return False
    
    def show_help(self):
        """顯示幫助訊息"""
        print("\n📚 系統使用說明")
        print("-" * 50)
        print("🎯 學習命令：")
        print("  • 直接提問 - 開始新的學習主題")
        print("  • 回答問題 - 在引導過程中回答老師提問")
        print("  • new/新問題 - 開始新的學習主題")
        print()
        print("🔧 系統命令：")
        print("  • status/狀態 - 查看學習狀態")
        print("  • help/幫助 - 顯示此說明")
        print("  • quit/退出 - 退出系統")
        print()
        print("💡 系統特色：")
        print("  • 始終記住您的原始問題")
        print("  • 基於完整對話上下文回應")
        print("  • 避免偏題和重複內容")
        print("  • 結合教材知識進行引導")
    
    def detect_input_type(self, user_input: str) -> str:
        """檢測輸入類型"""
        # 如果不在對話中，一定是新問題
        if not self.in_conversation:
            return "new_question"
        
        # 在對話中，檢查是否是新問題
        new_question_patterns = [
            "什麼是", "為什麼", "如何", "怎麼", "請解釋", "請說明",
            "能否", "可以", "告訴我", "我想知道", "什麼叫"
        ]
        
        if any(pattern in user_input for pattern in new_question_patterns):
            return "new_question"
        
        return "answer"
    
    def run(self):
        """運行學習系統（參考 ai_modal.ipynb 的主循環）"""
        self.show_welcome()
        
        while True:
            try:
                # 根據狀態顯示提示
                if self.in_conversation:
                    prompt = "\n💭 您的回答: "
                else:
                    prompt = "\n🤔 請提出您的問題: "
                
                user_input = input(prompt).strip()
                
                if not user_input:
                    print("💡 請輸入您的問題或回答。")
                    continue
                
                # 處理特殊命令
                if self.handle_special_commands(user_input):
                    break
                
                # 檢測輸入類型
                input_type = self.detect_input_type(user_input)
                
                if input_type == "new_question":
                    # 新問題
                    if self.in_conversation:
                        # 如果在對話中提新問題，先重置
                        self.tutor.reset()
                    
                    print("\n🎓 老師回應：")
                    response = self.tutor.start_new_question(user_input)
                    print(response)
                    
                    self.in_conversation = True
                
                else:
                    # 學生回答
                    if self.in_conversation:
                        print("\n🎓 老師回應：")
                        response = self.tutor.continue_conversation(user_input)
                        print(response)
                    else:
                        print("💡 請先提出一個問題開始學習，或輸入 'help' 查看說明。")
                
            except KeyboardInterrupt:
                print("\n\n👋 感謝使用基於上下文的學習系統！")
                break
            except Exception as e:
                print(f"\n❌ 系統錯誤: {e}")
                print("💡 請重新輸入您的問題。")

def main():
    """主函數"""
    try:
        learning_system = ContextLearningSystem()
        learning_system.run()
    except Exception as e:
        print(f"❌ 系統啟動失敗: {e}")
        print("💡 請檢查:")
        print("  1. Ollama是否運行: ollama serve")
        print("  2. 模型是否可用: ollama list")
        print("  3. 向量資料庫是否建立")

if __name__ == "__main__":
    main()
