#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試銀行家演算法教學
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from clean_tutor import CleanTutor

def test_banker_algorithm_teaching():
    """測試銀行家演算法教學流程"""
    print("🧪 測試銀行家演算法教學")
    print("="*60)
    
    tutor = CleanTutor()
    
    # 第一輪：學生問銀行家演算法
    print("🤔 學生問題: 什麼是銀行家演算法？")
    response1 = tutor.start_new_question("什麼是銀行家演算法？")
    print(f"🎓 老師回應:\n{response1}")
    
    # 檢查是否先解釋概念
    explains_concept = "銀行家演算法" in response1 and ("死鎖" in response1 or "避免" in response1)
    print(f"📊 是否先解釋概念: {'✅' if explains_concept else '❌'}")
    print("\n" + "="*40)
    
    # 第二輪：學生回答關於死鎖
    print("💭 學生回答: 死鎖指系統陷入僵局")
    response2 = tutor.continue_conversation("死鎖指系統陷入僵局")
    print(f"🎓 老師回應:\n{response2}")
    
    # 檢查是否針對死鎖回應
    addresses_deadlock = "死鎖" in response2 or "僵局" in response2
    print(f"📊 是否針對死鎖回應: {'✅' if addresses_deadlock else '❌'}")
    print("\n" + "="*40)
    
    # 第三輪：學生回答關於資源分配
    print("💭 學生回答: 利用資源分配圖確保不會有迴圈")
    response3 = tutor.continue_conversation("利用資源分配圖確保不會有迴圈")
    print(f"🎓 老師回應:\n{response3}")
    
    # 檢查是否提到安全序列
    mentions_safety = "安全" in response3 or "序列" in response3
    print(f"📊 是否引導到安全序列: {'✅' if mentions_safety else '❌'}")
    print("\n" + "="*40)
    
    # 第四輪：學生不懂安全序列
    print("💭 學生回答: 不太懂安全序列是什麼")
    response4 = tutor.continue_conversation("不太懂安全序列是什麼")
    print(f"🎓 老師回應:\n{response4}")
    
    # 檢查是否解釋安全序列
    explains_safety = "安全序列" in response4 or "安全" in response4
    print(f"📊 是否解釋安全序列: {'✅' if explains_safety else '❌'}")

def test_topic_focus():
    """測試主題專注度"""
    print("\n🧪 測試主題專注度")
    print("="*60)
    
    tutor = CleanTutor()
    tutor.start_new_question("什麼是銀行家演算法？")
    
    # 測試各種可能偏題的回答
    off_topic_answers = [
        "要看我目前可用的錢，才能判斷有多少錢可借出",
        "我覺得可以根據利益來決定",
        "那作業系統的歷史是什麼？",
        "Linux和Windows有什麼區別？"
    ]
    
    for answer in off_topic_answers:
        print(f"💭 學生回答: {answer}")
        response = tutor.continue_conversation(answer)
        print(f"🎓 老師回應: {response[:80]}...")
        
        # 檢查是否拉回主題
        stays_on_topic = "銀行家" in response or "死鎖" in response or "演算法" in response
        print(f"📊 是否保持主題: {'✅' if stays_on_topic else '❌'}")
        print()

def test_teaching_progression():
    """測試教學進展"""
    print("\n🧪 測試教學進展")
    print("="*60)
    
    tutor = CleanTutor()
    
    # 模擬完整的教學流程
    teaching_flow = [
        ("什麼是銀行家演算法？", True),
        ("死鎖指系統陷入僵局", False),
        ("利用資源分配圖", False),
        ("不懂安全序列", False),
        ("可以舉個例子嗎", False)
    ]
    
    for i, (input_text, is_new) in enumerate(teaching_flow):
        print(f"第{i+1}輪:")
        print(f"💭 {'學生問題' if is_new else '學生回答'}: {input_text}")
        
        if is_new:
            response = tutor.start_new_question(input_text)
        else:
            response = tutor.continue_conversation(input_text)
        
        print(f"🎓 老師回應: {response}")
        print(f"📊 長度: {len(response)} 字符")
        print()

def main():
    """主測試函數"""
    print("🎓 銀行家演算法教學測試")
    print("="*70)
    print("目標：確保能正確教學銀行家演算法")
    
    try:
        # 測試銀行家演算法教學
        test_banker_algorithm_teaching()
        
        # 測試主題專注度
        test_topic_focus()
        
        # 測試教學進展
        test_teaching_progression()
        
        print("\n🎉 測試完成！")
        print("\n💡 教學要點:")
        print("1. ✅ 先解釋銀行家演算法是避免死鎖的演算法")
        print("2. ✅ 從死鎖概念開始引導")
        print("3. ✅ 逐步引導到資源分配圖")
        print("4. ✅ 最後解釋安全序列概念")
        print("5. ✅ 始終保持在主題範圍內")
        
        print("\n🚀 如果教學邏輯正確，可以繼續使用系統")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
