#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試簡化版教學系統
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from clean_tutor import CleanTutor

def test_clean_system():
    """測試簡化版系統"""
    print("🧪 測試簡化版教學系統")
    print("="*60)
    
    tutor = CleanTutor()
    
    # 第一輪：新問題
    print("🤔 學生問題: 什麼是銀行家演算法？")
    response1 = tutor.start_new_question("什麼是銀行家演算法？")
    print(f"🎓 老師回應:\n{response1}")
    print("\n" + "="*40)
    
    # 第二輪：學生回答
    print("💭 學生回答: 死鎖指系統陷入僵局")
    response2 = tutor.continue_conversation("死鎖指系統陷入僵局")
    print(f"🎓 老師回應:\n{response2}")
    print("\n" + "="*40)
    
    # 第三輪：學生回答
    print("💭 學生回答: 利用資源分配圖確保不會有迴圈")
    response3 = tutor.continue_conversation("利用資源分配圖確保不會有迴圈")
    print(f"🎓 老師回應:\n{response3}")
    print("\n" + "="*40)
    
    # 第四輪：學生回答
    print("💭 學生回答: 不太懂安全序列")
    response4 = tutor.continue_conversation("不太懂安全序列")
    print(f"🎓 老師回應:\n{response4}")
    
    # 檢查系統狀態
    print("\n📊 系統狀態:")
    print(f"原始問題: {tutor.original_question}")
    print(f"上下文長度: {len(tutor.context)} 字符")
    print(f"對話輪數: {tutor.context.count('學生：')}")

def test_prompt_simplicity():
    """測試prompt簡化效果"""
    print("\n🔍 Prompt簡化效果檢查")
    print("="*60)
    
    tutor = CleanTutor()
    
    print("📝 統一的教學風格prompt:")
    print(tutor.TEACHER_STYLE)
    
    print("\n💡 簡化特點:")
    print("✅ 統一的TEACHER_STYLE")
    print("✅ 簡化的ask_ai方法")
    print("✅ 減少複雜的條件判斷")
    print("✅ 統一的回應邏輯")

def test_different_questions():
    """測試不同問題的回應"""
    print("\n🧪 測試不同問題的回應")
    print("="*60)
    
    questions = [
        "什麼是死鎖？",
        "虛擬記憶體的作用？",
        "進程和線程的區別？"
    ]
    
    for question in questions:
        print(f"\n📚 問題: {question}")
        print("-" * 30)
        
        tutor = CleanTutor()
        response = tutor.start_new_question(question)
        print(f"🎓 回應: {response[:150]}...")

def analyze_system_quality():
    """分析系統品質"""
    print("\n🔍 系統品質分析")
    print("="*60)
    
    tutor = CleanTutor()
    tutor.start_new_question("什麼是銀行家演算法？")
    response = tutor.continue_conversation("死鎖指系統陷入僵局")
    
    print("📊 品質指標:")
    
    # 檢查自然語氣
    natural_phrases = ["很好", "對的", "不錯", "讓我們", "你覺得", "想想看"]
    has_natural = any(phrase in response for phrase in natural_phrases)
    print(f"  自然語氣: {'✅' if has_natural else '❌'}")
    
    # 檢查格式化標題
    has_formatting = "**" in response or "###" in response
    print(f"  無格式化標題: {'✅' if not has_formatting else '❌'}")
    
    # 檢查針對性
    mentions_topic = "死鎖" in response or "僵局" in response
    print(f"  針對學生回答: {'✅' if mentions_topic else '❌'}")
    
    # 檢查引導問題
    has_question = "?" in response or "？" in response
    print(f"  提出引導問題: {'✅' if has_question else '❌'}")
    
    # 檢查長度
    is_appropriate = 50 < len(response) < 300
    print(f"  長度適中: {'✅' if is_appropriate else '❌'} ({len(response)} 字)")
    
    print(f"\n📝 回應內容:\n{response}")

def main():
    """主測試函數"""
    print("🎓 簡化版教學系統測試")
    print("="*70)
    print("目標：測試統一prompt設計的效果")
    
    try:
        # 測試簡化版系統
        test_clean_system()
        
        # 測試prompt簡化
        test_prompt_simplicity()
        
        # 測試不同問題
        test_different_questions()
        
        # 分析系統品質
        analyze_system_quality()
        
        print("\n🎉 測試完成！")
        print("\n💡 簡化版特色:")
        print("1. 🎯 統一的TEACHER_STYLE prompt")
        print("2. 🔄 簡化的ask_ai邏輯")
        print("3. 📝 減少複雜的條件判斷")
        print("4. 💭 保持自然的老師風格")
        print("5. 🎓 專注於教學效果")
        
        print("\n🚀 如果效果良好，可以運行: python rag_main.py")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
