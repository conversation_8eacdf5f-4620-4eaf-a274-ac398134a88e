#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合互動學習系統
蘇格拉底式引導教學，智能上下文管理
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from unified_tutor import UnifiedTutor

class UnifiedLearningSystem:
    """整合互動學習系統"""
    
    def __init__(self):
        """初始化系統"""
        self.tutor = UnifiedTutor()
        self.in_conversation = False
        self.waiting_for_response = False
        
    def show_welcome(self):
        """顯示歡迎訊息"""
        print("="*80)
        print("🎓 資管系智能學習輔導系統")
        print("="*80)
        print()
        print("🌟 教學特色：")
        print("  • 🧠 蘇格拉底式引導教學法")
        print("  • 🎯 逐步引導，確保真正理解")
        print("  • 🔄 智能上下文管理，對話更連貫")
        print("  • 📚 資管系專業知識庫支援")
        print("  • 💡 生活化舉例，抽象概念具體化")
        print()
        print("💡 使用說明：")
        print("  • 直接提問開始學習（如：什麼是銀行家演算法？）")
        print("  • 在引導過程中積極回答老師的問題")
        print("  • 輸入 'new' 或 '新問題' 開始新主題")
        print("  • 輸入 'summary' 或 '摘要' 查看學習進度")
        print("  • 輸入 'quit' 或 'exit' 退出系統")
        print()
        print("🎯 學習目標：透過引導式對話，真正理解概念而非背誦答案")
        print("="*80)
    
    def handle_special_commands(self, user_input: str) -> bool:
        """處理特殊命令"""
        user_input = user_input.lower().strip()
        
        if user_input in ['quit', 'exit', '退出', '結束']:
            print("\n👋 感謝使用資管系智能學習輔導系統！")
            print("🎓 希望您的學習之路順利！")
            return True
        
        elif user_input in ['new', '新問題', '新主題', 'reset']:
            self.tutor.reset_conversation()
            self.in_conversation = False
            self.waiting_for_response = False
            print("\n🆕 已開始新的學習主題，請提出您的問題：")
            return False
        
        elif user_input in ['summary', '摘要', '進度', 'status']:
            summary = self.tutor.get_conversation_summary()
            print(f"\n{summary}")
            return False
        
        elif user_input in ['help', '幫助', '說明']:
            self.show_help()
            return False
        
        return False
    
    def show_help(self):
        """顯示幫助訊息"""
        print("\n📚 系統使用說明")
        print("-" * 50)
        print("🎯 學習命令：")
        print("  • 直接提問 - 開始新的學習主題")
        print("  • 回答問題 - 在引導過程中回答老師提問")
        print("  • new/新問題 - 開始新的學習主題")
        print()
        print("🔧 系統命令：")
        print("  • summary/摘要 - 查看學習進度")
        print("  • help/幫助 - 顯示此說明")
        print("  • quit/退出 - 退出系統")
        print()
        print("💡 學習技巧：")
        print("  • 積極參與對話，不要只是被動接受")
        print("  • 如果不理解，直接說「不懂」或「不了解」")
        print("  • 嘗試用自己的話重新表達概念")
        print("  • 思考老師提出的引導性問題")
    
    def detect_conversation_state(self, user_input: str) -> str:
        """檢測對話狀態"""
        # 檢查是否是新問題（通常包含疑問詞）
        question_indicators = ['什麼', '為什麼', '如何', '怎麼', '哪個', '哪些', '是否', '能否', '可以']
        
        if any(indicator in user_input for indicator in question_indicators):
            return "new_question"
        
        # 檢查是否是回應（通常較短，或包含確認詞）
        if len(user_input) < 20 or any(word in user_input for word in ['是', '不是', '對', '錯', '不懂', '了解']):
            return "response"
        
        # 預設為回應
        return "response" if self.waiting_for_response else "new_question"
    
    def run(self):
        """運行整合學習系統"""
        self.show_welcome()
        
        while True:
            try:
                # 根據狀態顯示不同提示
                if self.waiting_for_response:
                    user_input = input("\n💭 您的想法或回答: ").strip()
                else:
                    user_input = input("\n🤔 請提出您的問題: ").strip()
                
                if not user_input:
                    print("💡 請輸入您的問題或回答。")
                    continue
                
                # 處理特殊命令
                if self.handle_special_commands(user_input):
                    break
                
                # 檢測對話狀態
                conversation_state = self.detect_conversation_state(user_input)
                
                if conversation_state == "new_question":
                    # 新問題
                    print("\n🎓 老師回應：")
                    response = self.tutor.handle_conversation(user_input, is_follow_up=False)
                    print(response)
                    
                    self.in_conversation = True
                    self.waiting_for_response = True
                    
                else:
                    # 對話回應
                    if self.in_conversation:
                        print("\n🎓 老師回應：")
                        response = self.tutor.handle_conversation(user_input, is_follow_up=True)
                        print(response)
                        
                        # 檢查是否需要繼續等待回應
                        if "?" in response or "想想看" in response or "你認為" in response:
                            self.waiting_for_response = True
                        else:
                            self.waiting_for_response = False
                    else:
                        print("💡 請先提出一個問題開始學習，或輸入 'help' 查看說明。")
                
            except KeyboardInterrupt:
                print("\n\n👋 感謝使用資管系智能學習輔導系統！")
                break
            except Exception as e:
                print(f"\n❌ 系統錯誤: {e}")
                print("💡 請重新輸入您的問題。")

def main():
    """主函數"""
    try:
        learning_system = UnifiedLearningSystem()
        learning_system.run()
    except Exception as e:
        print(f"❌ 系統啟動失敗: {e}")
        print("💡 請檢查:")
        print("  1. Ollama是否運行: ollama serve")
        print("  2. 模型是否可用: ollama list")
        print("  3. 向量資料庫是否建立")

if __name__ == "__main__":
    main()
