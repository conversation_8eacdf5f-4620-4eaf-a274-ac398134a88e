#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合互動學習系統
蘇格拉底式引導教學，智能上下文管理
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from unified_tutor import UnifiedTutor

class UnifiedLearningSystem:
    """整合互動學習系統"""
    
    def __init__(self):
        """初始化系統"""
        self.tutor = UnifiedTutor()
        self.in_conversation = False
        self.waiting_for_response = False
        
    def show_welcome(self):
        """顯示歡迎訊息"""
        print("="*80)
        print("🎓 資管系智能學習輔導系統")
        print("="*80)
        print()
        print("🌟 教學特色：")
        print("  • 🧠 蘇格拉底式引導教學法")
        print("  • 🎯 逐步引導，確保真正理解")
        print("  • 🔄 智能上下文管理，對話更連貫")
        print("  • 📚 資管系專業知識庫支援")
        print("  • 💡 生活化舉例，抽象概念具體化")
        print()
        print("💡 使用說明：")
        print("  • 直接提問開始學習（如：什麼是銀行家演算法？）")
        print("  • 在引導過程中積極回答老師的問題")
        print("  • 輸入 'new' 或 '新問題' 開始新主題")
        print("  • 輸入 'summary' 或 '摘要' 查看學習進度")
        print("  • 輸入 'quit' 或 'exit' 退出系統")
        print()
        print("🎯 學習目標：透過引導式對話，真正理解概念而非背誦答案")
        print("="*80)
    
    def handle_special_commands(self, user_input: str) -> bool:
        """處理特殊命令"""
        user_input = user_input.lower().strip()
        
        if user_input in ['quit', 'exit', '退出', '結束']:
            print("\n👋 感謝使用資管系智能學習輔導系統！")
            print("🎓 希望您的學習之路順利！")
            return True
        
        elif user_input in ['new', '新問題', '新主題', 'reset']:
            self.tutor.reset_conversation()
            self.in_conversation = False
            self.waiting_for_response = False
            print("\n🆕 已開始新的學習主題，請提出您的問題：")
            return False
        
        elif user_input in ['summary', '摘要', '進度', 'status']:
            summary = self.tutor.get_conversation_summary()
            print(f"\n{summary}")
            return False
        
        elif user_input in ['help', '幫助', '說明']:
            self.show_help()
            return False
        
        return False
    
    def show_help(self):
        """顯示幫助訊息"""
        print("\n📚 系統使用說明")
        print("-" * 50)
        print("🎯 學習命令：")
        print("  • 直接提問 - 開始新的學習主題")
        print("  • 回答問題 - 在引導過程中回答老師提問")
        print("  • new/新問題 - 開始新的學習主題")
        print()
        print("🔧 系統命令：")
        print("  • summary/摘要 - 查看學習進度")
        print("  • help/幫助 - 顯示此說明")
        print("  • quit/退出 - 退出系統")
        print()
        print("💡 學習技巧：")
        print("  • 積極參與對話，不要只是被動接受")
        print("  • 如果不理解，直接說「不懂」或「不了解」")
        print("  • 嘗試用自己的話重新表達概念")
        print("  • 思考老師提出的引導性問題")
    
    def detect_conversation_state(self, user_input: str) -> str:
        """檢測對話狀態"""
        # 如果正在等待回應，則一定是回應
        if self.waiting_for_response:
            return "response"

        # 如果不在對話中，則一定是新問題
        if not self.in_conversation:
            return "new_question"

        # 在對話中，檢查是否是新問題
        new_question_indicators = ['什麼是', '為什麼', '如何', '怎麼', '請解釋', '請說明']
        if any(indicator in user_input for indicator in new_question_indicators):
            return "new_question"

        # 否則視為回應
        return "response"
    
    def run(self):
        """運行整合學習系統"""
        self.show_welcome()
        
        while True:
            try:
                # 根據狀態顯示不同提示
                if self.waiting_for_response:
                    user_input = input("\n💭 請回答: ").strip()
                elif self.in_conversation:
                    user_input = input("\n🤔 請回答下一個問題: ").strip()
                else:
                    user_input = input("\n🤔 請提出您的問題: ").strip()
                
                if not user_input:
                    print("💡 請輸入您的問題或回答。")
                    continue
                
                # 處理特殊命令
                if self.handle_special_commands(user_input):
                    break
                
                # 檢測對話狀態
                conversation_state = self.detect_conversation_state(user_input)
                
                if conversation_state == "new_question":
                    # 新問題 - 重置狀態
                    self.tutor.reset_conversation()
                    self.in_conversation = True
                    self.waiting_for_response = False

                    print("\n🎓 老師回應：")
                    response = self.tutor.handle_conversation(user_input, is_follow_up=False)
                    print(response)

                    # 檢查回應是否包含問題
                    if self._response_has_question(response):
                        self.waiting_for_response = True

                else:
                    # 對話回應
                    if self.in_conversation:
                        print("\n🎓 老師回應：")
                        response = self.tutor.handle_conversation(user_input, is_follow_up=True)
                        print(response)

                        # 檢查是否需要繼續等待回應
                        if self._response_has_question(response):
                            self.waiting_for_response = True
                        else:
                            self.waiting_for_response = False
                    else:
                        print("💡 請先提出一個問題開始學習，或輸入 'help' 查看說明。")

    def _response_has_question(self, response: str) -> bool:
        """檢查回應是否包含問題"""
        question_indicators = ["?", "？", "想想看", "你認為", "請思考", "你覺得", "能否", "可以說說"]
        return any(indicator in response for indicator in question_indicators)
                
            except KeyboardInterrupt:
                print("\n\n👋 感謝使用資管系智能學習輔導系統！")
                break
            except Exception as e:
                print(f"\n❌ 系統錯誤: {e}")
                print("💡 請重新輸入您的問題。")

def main():
    """主函數"""
    try:
        learning_system = UnifiedLearningSystem()
        learning_system.run()
    except Exception as e:
        print(f"❌ 系統啟動失敗: {e}")
        print("💡 請檢查:")
        print("  1. Ollama是否運行: ollama serve")
        print("  2. 模型是否可用: ollama list")
        print("  3. 向量資料庫是否建立")

if __name__ == "__main__":
    main()
