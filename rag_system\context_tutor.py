#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於上下文的智能教師
參考 ai_modal.ipynb 的設計架構
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import requests
import json
import re
from typing import Dict, List, Any, Optional
from rag_ai_responder import AIResponder
import config

class ContextTutor:
    """基於上下文的智能教師"""
    
    def __init__(self):
        """初始化教師"""
        # 完全關閉所有日誌
        import logging
        logging.getLogger('rag_ai_responder').setLevel(logging.CRITICAL)
        logging.getLogger('rag_processor').setLevel(logging.CRITICAL)
        logging.getLogger('sentence_transformers').setLevel(logging.CRITICAL)
        logging.getLogger('chromadb').setLevel(logging.CRITICAL)
        logging.getLogger('transformers').setLevel(logging.CRITICAL)
        
        self.ai_responder = AIResponder(language='chinese')
        self.ai_base_url = config.AI_CONFIG['base_url']
        self.ai_model = config.AI_CONFIG['model']
        
        # 核心變數（參考您的設計）
        self.original_question = ""  # 主問題
        self.is_first_attempt = True  # 是否首次回應
        self.context = ""  # 對話上下文
        self.topic_knowledge = ""  # 主題知識
        
        # 蘇格拉底式教學提示詞（基於您的PROMPT）
        self.PROMPT = """
**你的目標**：
你是一位專業的資管系學習輔導 AI，幫助學生透過逐步引導方式理解考題與資管系相關知識，確保學生真正掌握概念，而不只是背誦答案。

**回應方式**：
1️⃣ **逐題解答**：
   - 你應該先用繁體中文複誦題目，確認題目方向，然後開始教學。
   - 你應該**一題一題解釋**，確保學生完全理解一題後，才進行下一題。
   - **不可一次提供所有答案**，必須等待學生回答後再決定下一步。

2️⃣ **分類考題內容**：
   - 幫我找出該題目屬於的**科目、知識點、章節、考試內容涵義**。
   - 提供**額外補充知識**（如常見錯誤、考試技巧）。

3️⃣ **引導式學習（Socratic Method）**：
   - 你的回答應該使用**逐步提問**，讓學生**自己思考出答案**，而非直接給出解答。
   - 當學生答錯時，不應直接告知答案，而應**一步步拆解問題，引導學生找到錯誤點**。
   - **舉例 + 類比**：使用生活化的舉例，幫助學生理解抽象概念。

4️⃣ **適應學生程度（動態調整）**：
   - 你需要根據學生的回答，判斷學生的**掌握度、錯誤點、學習能力**，並調整問題難度。
   - 若學生顯示出不理解，請將概念拆解為**更小步驟**，並用更淺顯的方式解釋。
   - 若學生已經理解，則可以適度提高難度，挑戰更進階題目。

**重要**：
- 始終記住原始問題，不要偏題
- 保持對話的邏輯連貫性
- 避免重複之前說過的內容
"""
        
        print("🎓 基於上下文的智能教師已啟動")
        print("💡 參考 ai_modal.ipynb 架構設計")
    
    def get_topic_knowledge(self, question: str) -> str:
        """獲取主題相關知識"""
        try:
            # 翻譯成英文搜索
            english_question = self._translate_to_english(question)
            search_results = self.ai_responder.search_knowledge(english_question)
            
            if search_results:
                # 提取前3個結果的內容
                knowledge = "\n".join([
                    result.get('content', '')[:400] 
                    for result in search_results[:3]
                ])
                return knowledge
        except Exception as e:
            pass
        return ""
    
    def _translate_to_english(self, text: str) -> str:
        """翻譯成英文"""
        try:
            prompt = f"Translate to English: {text}"
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.1, "num_predict": 50}
                },
                timeout=10
            )
            if response.status_code == 200:
                return response.json().get('response', '').strip()
        except:
            pass
        return text
    
    def ask_ai(self, student_answer: str, is_first_attempt: bool, original_question: str = "", context: str = "") -> str:
        """AI回應函式（基於您的設計）"""
        if is_first_attempt:
            # 首次回應：處理新問題
            prompt = f"""
            {self.PROMPT}
            
            **教材知識**：{self.topic_knowledge}
            **考題**：{original_question}
            **學生回答**：{student_answer}
            
            **請提供回饋與指導**：
            1. 先簡單解釋這個概念（基於教材知識）
            2. 從最基礎的相關概念開始引導提問
            3. 不要直接給出完整答案
            4. 語氣自然，像真正的老師
            
            記住：要始終圍繞原始問題「{original_question}」進行教學。
            """
        else:
            # 後續回應：基於上下文繼續引導
            prompt = f"""
            {self.PROMPT}
            
            **原始問題**：{original_question}
            **對話上下文**：{context}
            **學生最新回答**：{student_answer}

            **請繼續提供引導**：
            1. 簡短評價學生回答（對/不完全對/需要澄清）
            2. 提出一個新的引導問題，推進理解
            3. 始終圍繞原始問題「{original_question}」
            4. 回應要簡潔，不要重複之前的內容
            5. 一次只問一個問題

            重要：回應要簡潔明確，避免冗長解釋和重複內容。
            """
        
        return self._call_ai(prompt)
    
    def _call_ai(self, prompt: str) -> str:
        """調用AI"""
        try:
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.3,
                        "num_predict": 250,
                        "top_p": 0.9,
                        "stop": ["學生：", "💭", "🤔"]
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                ai_response = response.json().get('response', '').strip()
                if ai_response and len(ai_response) > 10:
                    return ai_response
                else:
                    return "讓我們繼續探討這個概念。"
            else:
                return "抱歉，讓我重新組織一下思路。"
        except Exception as e:
            return "請稍等，讓我重新思考一下。"
    
    def start_new_question(self, question: str) -> str:
        """開始新問題"""
        # 重置狀態
        self.original_question = question
        self.is_first_attempt = True
        self.context = ""
        
        # 獲取主題知識
        self.topic_knowledge = self.get_topic_knowledge(question)
        
        # 生成首次回應
        response = self.ask_ai(question, True, question, "")
        
        # 更新狀態
        self.is_first_attempt = False
        self.context = f"學生問題：{question}\n老師回應：{response}"
        
        return response
    
    def continue_conversation(self, student_answer: str) -> str:
        """繼續對話"""
        if not self.original_question:
            return "請先提出一個問題開始學習。"
        
        # 生成回應
        response = self.ask_ai(student_answer, False, self.original_question, self.context)
        
        # 更新上下文
        self.context += f"\n學生回答：{student_answer}\n老師回應：{response}"
        
        # 保持上下文在合理長度
        if len(self.context) > 2000:
            # 保留最近的對話
            lines = self.context.split('\n')
            self.context = '\n'.join(lines[-10:])
        
        return response
    
    def get_status(self) -> str:
        """獲取當前狀態"""
        return f"""
📊 學習狀態
原始問題: {self.original_question or '無'}
對話輪數: {len(self.context.split('學生回答：')) - 1}
上下文長度: {len(self.context)} 字符
"""
    
    def reset(self):
        """重置對話"""
        self.original_question = ""
        self.is_first_attempt = True
        self.context = ""
        self.topic_knowledge = ""
