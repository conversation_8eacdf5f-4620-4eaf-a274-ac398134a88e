#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能對話教學系統
實現自適應教學和理解程度評估
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import requests
import json
import re
from typing import Dict, List, Any
from rag_ai_responder import AIResponder
import config

class IntelligentTutor:
    """智能對話教學系統"""
    
    def __init__(self):
        """初始化智能教師"""
        self.ai_responder = AIResponder(language='chinese')
        self.ai_base_url = config.AI_CONFIG['base_url']
        self.ai_model = config.AI_CONFIG['model']
        
        # 對話狀態
        self.current_topic = None
        self.teaching_mode = None  # 'guided' or 'direct'
        self.student_understanding = 0  # 0-100 理解程度
        self.conversation_history = []
        self.current_question = None
        
        print("🎓 智能教學系統已啟動")
        print("💡 我會根據您的問題智能選擇教學方式")
        print("📚 支援中文問答，自動搜索英文資料庫")
        print("🔄 在引導過程中會評估您的理解程度")
    
    def translate_to_english(self, chinese_text: str) -> str:
        """將中文問題翻譯成英文用於搜索"""
        try:
            prompt = f"""請將以下中文問題翻譯成英文，保持技術術語的準確性：

中文問題：{chinese_text}

只回答英文翻譯，不要其他內容。"""

            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.1, "num_predict": 100}
                },
                timeout=20
            )
            
            if response.status_code == 200:
                english_question = response.json().get('response', '').strip()
                return english_question if english_question else chinese_text
            else:
                return chinese_text
                
        except Exception as e:
            print(f"⚠️ 翻譯失敗，使用原文搜索: {e}")
            return chinese_text
    
    def analyze_question_intelligence(self, question: str) -> Dict[str, Any]:
        """智能分析問題（內部使用，不顯示過程）"""
        try:
            prompt = f"""分析以下學生問題，判斷教學策略。回答JSON格式：

學生問題：{question}

{{
    "is_basic_definition": true/false,
    "complexity_level": 1-5,
    "needs_guidance": true/false,
    "teaching_approach": "direct"或"guided",
    "key_concepts": ["概念1", "概念2"],
    "estimated_difficulty": "簡單"/"中等"/"困難"
}}

只回答JSON，不要其他文字。"""

            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.1, "num_predict": 300}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                ai_response = response.json().get('response', '')
                json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
                if json_match:
                    analysis = json.loads(json_match.group())
                    return analysis
            
            # 備用分析
            return {
                "is_basic_definition": "什麼是" in question or "定義" in question,
                "complexity_level": 3,
                "needs_guidance": len(question) > 15,
                "teaching_approach": "guided" if len(question) > 15 else "direct",
                "key_concepts": [],
                "estimated_difficulty": "中等"
            }
            
        except Exception as e:
            print(f"⚠️ 問題分析失敗: {e}")
            return {
                "is_basic_definition": False,
                "complexity_level": 3,
                "needs_guidance": True,
                "teaching_approach": "guided",
                "key_concepts": [],
                "estimated_difficulty": "中等"
            }
    
    def search_knowledge_bilingual(self, chinese_question: str) -> List[Dict]:
        """雙語知識搜索：中文問題→英文搜索→中文回答"""
        # 翻譯成英文搜索
        english_question = self.translate_to_english(chinese_question)
        print(f"🔍 搜索關鍵詞: {english_question}")
        
        # 使用英文搜索向量資料庫
        search_results = self.ai_responder.search_knowledge(english_question)
        
        return search_results
    
    def generate_direct_answer(self, question: str, search_results: List[Dict]) -> str:
        """生成直接回答"""
        context = ""
        if search_results:
            context = "\n".join([result.get('content', '')[:500] for result in search_results[:3]])
        
        prompt = f"""基於以下教材內容，用繁體中文簡潔明確地回答學生問題：

教材內容：
{context}

學生問題：{question}

請提供：
1. 清楚的定義或解釋
2. 2-3個核心要點
3. 一個簡單例子（如果適用）

回答要直接、準確，不需要過度解釋。"""

        try:
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.3, "num_predict": 400}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json().get('response', '').strip()
            else:
                return "抱歉，無法生成回答。"
                
        except Exception as e:
            return f"回答生成失敗: {e}"
    
    def generate_guided_response(self, question: str, search_results: List[Dict]) -> str:
        """生成引導式回答"""
        context = ""
        if search_results:
            context = "\n".join([result.get('content', '')[:500] for result in search_results[:3]])
        
        prompt = f"""作為專業教師，用引導式教學回答學生問題。要求：

教材內容：
{context}

學生問題：{question}

請用引導式教學，結構如下：

🎯 **核心概念簡介**
[簡潔介紹概念，不超過2句話]

🔍 **關鍵思考**
[提出1-2個引導性問題，讓學生思考]

💡 **實例說明**
[用簡單例子幫助理解]

🤔 **測試理解**
[提出一個具體問題測試學生理解]

要求：
- 簡潔明確，避免冗長說明
- 重點在引導思考，不是直接給答案
- 最後必須有一個具體問題讓學生回答"""

        try:
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.4, "num_predict": 600}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json().get('response', '').strip()
            else:
                return "抱歉，無法生成引導式回答。"
                
        except Exception as e:
            return f"引導式回答生成失敗: {e}"
    
    def evaluate_understanding(self, student_response: str) -> Dict[str, Any]:
        """評估學生理解程度並提供詳細反饋"""
        try:
            prompt = f"""作為專業教師，評估學生對當前主題的理解並提供反饋：

當前主題：{self.current_topic}
學生回應：{student_response}

請評估並回答JSON格式：
{{
    "understanding_score": 85,
    "is_correct": true/false,
    "feedback": "對學生回答的具體評價和解釋",
    "next_guidance": "基於學生回答的下一步引導",
    "needs_more_help": true/false
}}

重點：
1. 判斷學生回答是否正確
2. 給出具體的反饋和解釋
3. 提供針對性的下一步引導

只回答JSON。"""

            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.2, "num_predict": 400}
                },
                timeout=30
            )

            if response.status_code == 200:
                ai_response = response.json().get('response', '')
                json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
                if json_match:
                    evaluation = json.loads(json_match.group())
                    return evaluation

            # 備用評估
            return {
                "understanding_score": 50,
                "is_correct": False,
                "feedback": "讓我們繼續探討這個概念",
                "next_guidance": "請再詳細說明您的想法",
                "needs_more_help": True
            }

        except Exception as e:
            print(f"⚠️ 理解程度評估失敗: {e}")
            return {
                "understanding_score": 50,
                "is_correct": False,
                "feedback": "讓我們繼續探討這個概念",
                "next_guidance": "請再詳細說明您的想法",
                "needs_more_help": True
            }
    
    def handle_question(self, question: str) -> str:
        """處理學生問題的主要方法"""
        print(f"\n🤔 思考中...")
        
        # 1. 智能分析問題（內部處理，不顯示）
        analysis = self.analyze_question_intelligence(question)
        
        # 2. 設定當前主題和教學模式
        self.current_topic = question
        self.teaching_mode = analysis['teaching_approach']
        
        # 3. 雙語搜索知識
        search_results = self.search_knowledge_bilingual(question)
        
        # 4. 根據分析結果選擇教學方式
        if analysis['teaching_approach'] == 'direct':
            print("📝 我來直接為您解釋這個概念：\n")
            response = self.generate_direct_answer(question, search_results)
        else:
            print("🎯 讓我用引導的方式幫您理解：\n")
            response = self.generate_guided_response(question, search_results)
        
        # 5. 記錄對話歷史
        self.conversation_history.append({
            "question": question,
            "response": response,
            "teaching_mode": self.teaching_mode
        })
        
        return response
    
    def continue_guidance(self, student_response: str) -> str:
        """在引導模式中繼續對話"""
        if self.teaching_mode != 'guided':
            return "請先問一個問題開始學習。"

        # 評估理解程度並獲取詳細反饋
        evaluation = self.evaluate_understanding(student_response)
        self.student_understanding = evaluation.get('understanding_score', 50)

        # 構建回應
        response_parts = []

        # 1. 對學生回答的評價
        if evaluation.get('is_correct'):
            response_parts.append(f"✅ **很好！** {evaluation.get('feedback', '您的理解是正確的。')}")
        else:
            response_parts.append(f"🤔 **讓我們再想想：** {evaluation.get('feedback', '這個想法需要再調整一下。')}")

        # 2. 下一步引導
        next_guidance = evaluation.get('next_guidance', '')
        if next_guidance:
            response_parts.append(f"\n💡 **接下來：**\n{next_guidance}")

        # 3. 根據理解程度決定後續行動
        if self.student_understanding >= 80:
            response_parts.append("""

🎉 **太棒了！您已經掌握了這個概念！**

您可以選擇：
• 輸入 'next' 或 '下一題' - 學習新概念
• 輸入 'deeper' 或 '深入' - 深入相關概念
• 直接問新問題 - 開始新主題""")
        elif evaluation.get('needs_more_help'):
            response_parts.append("\n🔄 **讓我們繼續探討...**")

        return "\n".join(response_parts)
    
    def _generate_follow_up_guidance(self, level: str) -> str:
        """生成後續引導"""
        try:
            if level == "good":
                prompt_type = "學生理解不錯，需要一些補充說明"
            else:
                prompt_type = "學生理解有困難，需要更簡單的解釋"
            
            prompt = f"""針對當前主題提供後續引導：

主題：{self.current_topic}
情況：{prompt_type}

請提供簡短的後續引導（不超過200字），幫助學生更好理解。"""

            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.3, "num_predict": 300}
                },
                timeout=20
            )
            
            if response.status_code == 200:
                return response.json().get('response', '').strip()
            else:
                return "讓我們繼續深入了解這個概念..."
                
        except Exception as e:
            return "讓我們繼續學習..."
