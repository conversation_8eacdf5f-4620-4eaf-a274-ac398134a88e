#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG智能教學系統 - 安裝和設定腳本
自動安裝依賴套件並進行初始設定
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """顯示安裝橫幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🎓 RAG智能教學系統                          ║
║                     安裝和設定程式                             ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_python_version():
    """檢查Python版本"""
    print("🐍 檢查Python版本...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本過低: {version.major}.{version.minor}")
        print("💡 請安裝Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_system_info():
    """檢查系統資訊"""
    print("\n💻 系統資訊:")
    print(f"  • 作業系統: {platform.system()} {platform.release()}")
    print(f"  • 處理器: {platform.processor()}")
    print(f"  • Python路徑: {sys.executable}")
    print(f"  • 工作目錄: {os.getcwd()}")

def install_dependencies():
    """安裝依賴套件"""
    print("\n📦 安裝依賴套件...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt 檔案不存在")
        return False
    
    try:
        # 升級pip
        print("🔄 升級pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip升級完成")
        
        # 安裝依賴套件
        print("🔄 安裝依賴套件...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依賴套件安裝完成")
            return True
        else:
            print(f"❌ 依賴套件安裝失敗:")
            print(result.stderr)
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 安裝過程發生錯誤: {e}")
        return False

def setup_directories():
    """設定目錄結構"""
    print("\n📁 設定目錄結構...")
    
    base_dir = Path(__file__).parent
    directories = [
        base_dir / "data",
        base_dir / "data" / "pdfs",
        base_dir / "data" / "knowledge_db", 
        base_dir / "data" / "outputs"
    ]
    
    for directory in directories:
        try:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ 目錄已創建: {directory.name}")
        except Exception as e:
            print(f"❌ 創建目錄失敗 {directory}: {e}")
            return False
    
    return True

def check_ollama():
    """檢查Ollama安裝"""
    print("\n🤖 檢查Ollama安裝...")
    
    try:
        # 檢查ollama命令是否可用
        result = subprocess.run(["ollama", "--version"], 
                               capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Ollama已安裝")
            print(f"  版本: {result.stdout.strip()}")
            
            # 檢查llama3.1模型
            print("🔍 檢查llama3.1模型...")
            models_result = subprocess.run(["ollama", "list"], 
                                         capture_output=True, text=True, timeout=10)
            
            if "llama3.1" in models_result.stdout:
                print("✅ llama3.1模型已安裝")
                return True
            else:
                print("⚠️ llama3.1模型未安裝")
                print("💡 請運行: ollama pull llama3.1")
                return False
        else:
            print("❌ Ollama未正確安裝")
            return False
            
    except FileNotFoundError:
        print("❌ Ollama未安裝")
        print("💡 請先安裝Ollama: https://ollama.ai/")
        return False
    except subprocess.TimeoutExpired:
        print("❌ Ollama檢查超時")
        return False
    except Exception as e:
        print(f"❌ 檢查Ollama時發生錯誤: {e}")
        return False

def create_sample_files():
    """創建示例檔案"""
    print("\n📄 創建示例檔案...")
    
    base_dir = Path(__file__).parent
    
    # 創建示例PDF說明檔案
    pdf_readme = base_dir / "data" / "pdfs" / "README.txt"
    try:
        with open(pdf_readme, 'w', encoding='utf-8') as f:
            f.write("""📚 PDF教材目錄說明

這個目錄用於存放PDF教材檔案。

使用方法:
1. 將您的PDF教材檔案複製到此目錄
2. 運行 'python rag_main.py' 
3. 選擇 "PDF教材處理" 功能
4. 系統會自動處理所有PDF檔案並建立知識庫

支援的檔案格式:
• PDF檔案 (.pdf)
• 支援中英文混合內容
• 建議檔案大小不超過100MB

注意事項:
• 檔案名稱請使用英文或數字
• 避免使用特殊字符
• 確保PDF檔案可以正常開啟
""")
        print("✅ PDF目錄說明檔案已創建")
    except Exception as e:
        print(f"❌ 創建PDF說明檔案失敗: {e}")
    
    # 創建快速啟動腳本
    if platform.system() == "Windows":
        start_script = base_dir / "start.bat"
        script_content = """@echo off
echo 🎓 啟動RAG智能教學系統...
python rag_main.py
pause
"""
    else:
        start_script = base_dir / "start.sh"
        script_content = """#!/bin/bash
echo "🎓 啟動RAG智能教學系統..."
python3 rag_main.py
"""
    
    try:
        with open(start_script, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        if platform.system() != "Windows":
            os.chmod(start_script, 0o755)
        
        print(f"✅ 快速啟動腳本已創建: {start_script.name}")
    except Exception as e:
        print(f"❌ 創建啟動腳本失敗: {e}")

def run_system_test():
    """運行系統測試"""
    print("\n🧪 運行系統測試...")
    
    test_script = Path(__file__).parent / "test_system.py"
    
    if not test_script.exists():
        print("❌ 測試腳本不存在")
        return False
    
    try:
        result = subprocess.run([sys.executable, str(test_script)], 
                               capture_output=True, text=True, timeout=60)
        
        print("測試輸出:")
        print(result.stdout)
        
        if result.stderr:
            print("錯誤輸出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 系統測試超時")
        return False
    except Exception as e:
        print(f"❌ 運行系統測試失敗: {e}")
        return False

def show_next_steps():
    """顯示後續步驟"""
    print("\n🎯 後續步驟:")
    print("="*50)
    
    steps = [
        "1. 將PDF教材檔案放入 data/pdfs/ 目錄",
        "2. 確保Ollama服務正在運行",
        "3. 運行 'python rag_main.py' 啟動系統",
        "4. 或者運行快速啟動腳本",
        "5. 首次使用請先處理PDF建立知識庫",
        "6. 然後就可以開始智能問答了！"
    ]
    
    for step in steps:
        print(f"  {step}")
    
    print(f"\n💡 有用的命令:")
    print(f"  • 系統測試: python test_system.py")
    print(f"  • 使用示例: python example_usage.py")
    print(f"  • 主程式: python rag_main.py")

def main():
    """主安裝函數"""
    print_banner()
    
    # 檢查系統
    if not check_python_version():
        return False
    
    check_system_info()
    
    # 安裝步驟
    steps = [
        ("安裝依賴套件", install_dependencies),
        ("設定目錄結構", setup_directories),
        ("檢查Ollama", check_ollama),
        ("創建示例檔案", create_sample_files)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
            else:
                print(f"⚠️ {step_name} 未完全成功")
        except Exception as e:
            print(f"❌ {step_name} 發生錯誤: {e}")
    
    # 顯示安裝結果
    print(f"\n📊 安裝摘要: {success_count}/{len(steps)} 個步驟成功")
    
    if success_count >= 3:  # 至少3個步驟成功
        print("🎉 安裝基本完成！")
        
        # 可選：運行系統測試
        test_choice = input("\n是否運行系統測試？(y/n): ").strip().lower()
        if test_choice in ['y', 'yes', '是']:
            run_system_test()
        
        show_next_steps()
        return True
    else:
        print("❌ 安裝未完成，請檢查錯誤訊息")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 安裝已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安裝過程發生未預期錯誤: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
