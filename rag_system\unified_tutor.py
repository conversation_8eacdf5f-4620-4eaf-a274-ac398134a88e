#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合智能教學系統
結合問答和對話教學，使用蘇格拉底式引導教學法
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import requests
import json
import re
from typing import Dict, List, Any, Optional
from rag_ai_responder import AIResponder
import config

class UnifiedTutor:
    """整合智能教師 - 蘇格拉底式引導教學"""
    
    def __init__(self):
        """初始化整合智能教師"""
        # 初始化AI回答系統，關閉詳細日誌
        self.ai_responder = AIResponder(language='chinese')
        # 設置為靜默模式，減少日誌輸出
        import logging
        logging.getLogger('rag_ai_responder').setLevel(logging.WARNING)
        self.ai_base_url = config.AI_CONFIG['base_url']
        self.ai_model = config.AI_CONFIG['model']
        
        # 對話狀態管理
        self.conversation_context = []  # 對話上下文
        self.current_topic = None       # 當前主題
        self.student_understanding = 0  # 理解程度 0-100
        self.last_search_results = []   # 上次搜索結果，避免重複搜索
        self.topic_knowledge_base = {}  # 主題知識庫緩存
        
        # 蘇格拉底式教學提示詞
        self.teaching_prompt = """
**你的目標**：
你是一位專業的資管系學習輔導 AI，幫助學生透過逐步引導方式理解考題與資管系相關知識，確保學生真正掌握概念，而不只是背誦答案。

**回應方式**：
1️⃣ **逐題解答**：
   - 你應該先用繁體中文複誦題目，確認題目方向，然後開始教學。
   - 你應該**一題一題解釋**，確保學生完全理解一題後，才進行下一題。
   - **不可一次提供所有答案**，必須等待學生回答後再決定下一步。

2️⃣ **分類考題內容**：
   - 幫我找出該題目屬於的**科目、知識點、章節、考試內容涵義**。
   - 提供**額外補充知識**（如常見錯誤、考試技巧）。

3️⃣ **引導式學習（Socratic Method）**：
   - 你的回答應該使用**逐步提問**，讓學生**自己思考出答案**，而非直接給出解答。
   - 當學生答錯時，不應直接告知答案，而應**一步步拆解問題，引導學生找到錯誤點**。
   - **舉例 + 類比**：使用生活化的舉例，幫助學生理解抽象概念。

4️⃣ **適應學生程度（動態調整）**：
   - 你需要根據學生的回答，判斷學生的**掌握度、錯誤點、學習能力**，並調整問題難度。
   - 若學生顯示出不理解，請將概念拆解為**更小步驟**，並用更淺顯的方式解釋。
   - 若學生已經理解，則可以適度提高難度，挑戰更進階題目。

**重要原則**：
- 保持對話的連續性和上下文
- 專注於引導而非直接給答案
- 使用生活化例子幫助理解
- 不要自問自答，等待學生回應
- 一次只問一個問題，不要連續提問
- 不要顯示內部思考過程或評估標題
- 直接進行教學對話，不要寫出「評估學生理解程度」等標題
"""
        
        print("🎓 整合智能教學系統已啟動")
        print("💡 使用蘇格拉底式引導教學法")
        print("🔄 智能上下文管理，減少不必要搜索")
    
    def should_search_knowledge(self, question: str) -> bool:
        """判斷是否需要搜索知識庫"""
        # 如果是新主題或者上下文中沒有相關資訊，才搜索
        if not self.current_topic:
            return True
        
        # 如果問題與當前主題相關，使用緩存的知識
        if self.current_topic.lower() in question.lower():
            return False
        
        # 如果是簡單的確認或澄清問題，不需要搜索
        simple_responses = ['是', '不是', '對', '錯', '不懂', '不了解', '還是不太了解', '可以', '不可以']
        if any(resp in question for resp in simple_responses):
            return False
        
        return True
    
    def get_knowledge_context(self, question: str) -> str:
        """獲取知識上下文"""
        if self.should_search_knowledge(question):
            # 翻譯成英文搜索（靜默執行）
            english_question = self.translate_to_english(question)
            search_results = self.ai_responder.search_knowledge(english_question)

            # 緩存搜索結果
            self.last_search_results = search_results
            self.current_topic = question

            # 提取知識內容
            if search_results:
                context = "\n".join([result.get('content', '')[:500] for result in search_results[:3]])
                self.topic_knowledge_base[self.current_topic] = context
                return context
        else:
            # 使用緩存的知識（靜默執行）
            return self.topic_knowledge_base.get(self.current_topic, "")

        return ""

    def _get_last_question(self) -> str:
        """獲取最後一個問題"""
        if self.conversation_context:
            last_response = self.conversation_context[-1].get('response', '')
            # 提取問題部分
            if '?' in last_response or '？' in last_response:
                lines = last_response.split('\n')
                for line in lines:
                    if '?' in line or '？' in line:
                        return line.strip()
        return "上一個問題"

    def translate_to_english(self, chinese_text: str) -> str:
        """將中文問題翻譯成英文用於搜索"""
        try:
            prompt = f"請將以下中文問題翻譯成英文，保持技術術語準確：{chinese_text}\n只回答英文翻譯："
            
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.1, "num_predict": 100}
                },
                timeout=15
            )
            
            if response.status_code == 200:
                return response.json().get('response', '').strip()
            return chinese_text
        except:
            return chinese_text
    
    def generate_socratic_response(self, question: str, student_response: str = None) -> str:
        """生成蘇格拉底式教學回答"""
        # 獲取知識上下文
        knowledge_context = self.get_knowledge_context(question)
        
        # 構建對話上下文
        conversation_history = "\n".join([
            f"學生: {item['question']}\n老師: {item['response']}" 
            for item in self.conversation_context[-3:]  # 只保留最近3輪對話
        ])
        
        # 構建完整prompt
        if student_response:
            # 這是對學生回答的回應
            full_prompt = f"""你是一位專業的資管系學習輔導老師。

**教材知識**：
{knowledge_context}

**對話歷史**：
{conversation_history}

**當前情況**：
你剛才問的問題：{self._get_last_question()}
學生的回答：{student_response}

請自然地回應學生的答案：
1. 簡短評價學生的回答（對/不完全對/需要補充）
2. 給予鼓勵或建設性建議
3. 如果需要繼續，提出下一個引導問題
4. 用自然對話的方式，不要寫標題或分析過程
5. 一次只問一個問題

回答要簡潔自然，像真正的老師在對話。"""
        else:
            # 這是對新問題的初始回應
            full_prompt = f"""你是一位專業的資管系學習輔導老師，使用蘇格拉底式引導教學法。

**教材知識**：
{knowledge_context}

**學生問題**：{question}

請開始教學：
1. 簡單確認學生的問題
2. 簡潔說明這個問題屬於哪個科目領域
3. 提出一個基礎的引導問題，讓學生思考
4. 用自然對話方式，不要寫標題或格式化內容
5. 一次只問一個問題

不要直接給答案，要引導學生自己思考。回答要簡潔自然。"""
        
        try:
            response = requests.post(
                f"{self.ai_base_url}/api/generate",
                json={
                    "model": self.ai_model,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.4,
                        "num_predict": 600
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                ai_response = response.json().get('response', '').strip()
                
                # 記錄對話（避免重複記錄）
                if student_response:
                    # 更新最後一條記錄的學生回應
                    if self.conversation_context:
                        self.conversation_context[-1]["student_response"] = student_response
                        self.conversation_context[-1]["teacher_follow_up"] = ai_response
                else:
                    # 新問題，添加新記錄
                    self.conversation_context.append({
                        "question": question,
                        "student_response": None,
                        "response": ai_response
                    })

                # 保持對話歷史在合理範圍內
                if len(self.conversation_context) > 8:
                    self.conversation_context = self.conversation_context[-6:]
                
                return ai_response
            else:
                return "抱歉，我現在無法回應。請重新提問。"
                
        except Exception as e:
            return f"系統暫時無法回應，請稍後再試。錯誤：{e}"
    
    def handle_conversation(self, user_input: str, is_follow_up: bool = False) -> str:
        """處理對話的主要方法"""
        if is_follow_up and self.conversation_context:
            # 這是對之前問題的後續回應
            last_question = self.conversation_context[-1]["question"]
            return self.generate_socratic_response(last_question, user_input)
        else:
            # 這是新問題
            return self.generate_socratic_response(user_input)
    
    def reset_conversation(self):
        """重置對話狀態"""
        self.conversation_context = []
        self.current_topic = None
        self.student_understanding = 0
        self.last_search_results = []
        # 靜默重置，不顯示提示
    
    def get_conversation_summary(self) -> str:
        """獲取對話摘要"""
        if not self.conversation_context:
            return "尚未開始對話"
        
        return f"""
📊 對話摘要
當前主題: {self.current_topic or '無'}
對話輪數: {len(self.conversation_context)}
理解程度: {self.student_understanding}%
最近問題: {self.conversation_context[-1]['question'] if self.conversation_context else '無'}
"""
