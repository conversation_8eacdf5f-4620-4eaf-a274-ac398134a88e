/* 背景漸層 */
.bg-light {
  background: linear-gradient(135deg, #E8F1F8, #B3C4E0); /* 淡藍色漸層背景 */
}

/* 主要顏色設定 */
.custom-2 {
  background-color: #1A2A4A !important; /* 深藍色，資管系主色調 */
  border: none;
  color: white;
}

.custom-3 {
  background-color: #3A57A1 !important; /* 較淺藍色 */
  border: none;
  color: white;
}

/* 太極式推拉容器 */
.taichi-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(50, 50, 93, .1), 0 5px 15px rgba(0, 0, 0, .07);
  margin: 2rem 0;
}

/* 推拉滑塊 */
.taichi-slider {
  display: flex;
  width: 200%;
  transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

/* 各個面板 */
.taichi-panel {
  width: 50%;
  position: relative;
  padding: 20px;
}

/* 登入面板 */
.login-panel {
  background-color: #ffffff;
}

/* 註冊面板 */
.register-panel {
  background-color: #f8f9fa;
}

/* 切換按鈕容器 */
.taichi-toggle-btn {
  text-align: center;
  margin-top: 1.5rem;
}

/* 卡片樣式 */
.custom-card {
  border-radius: 15px;
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 表單元素樣式 */
.form-control {
  border: 2px solid #e9ecef;
  padding: 10px 16px;
  transition: border 0.3s ease;
}

.form-control:focus {
  border-color: #3A57A1;
  box-shadow: 0 0 0 0.2rem rgba(58, 87, 161, 0.25);
}

/* 按鈕樣式 */
button {
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, .1), 0 3px 6px rgba(0, 0, 0, .08);
}

/* 連結按鈕 */
button[color="link"] {
  color: #4C73A1;
}

button[color="link"]:hover {
  color: #3A57A1;
  text-decoration: underline;
  transform: none;
  box-shadow: none;
}

/* 文字顏色 */
h1.text-primary {
  color: #3A57A1 !important;
}

/* 響應式調整 */
@media (max-width: 768px) {
  .taichi-panel {
    padding: 15px;
  }
}

/* 輸入框圓角增強 */
[cInputGroupText] {
  border-radius: 50px 0 0 50px;
  background-color: #f8f9fa;
}

/* 防止在最小寬度時排版崩潰 */
@media (max-width: 576px) {
  .taichi-slider {
    flex-direction: column;
    width: 100%;
    height: 200%;
  }
  
  .taichi-panel {
    width: 100%;
    height: 50%;
  }
}