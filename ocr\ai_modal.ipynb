{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import google.generativeai as genai\n", "genai.configure(api_key=\"AIzaSyCwwVlv5VeCkyI1RL9mKvWSZHUKn6WlpIU\")\n", "# ✅ 設定 Prompt（提示詞）\n", "PROMPT = \"\"\"\n", " **你的目標**：\n", "你是一位專業的資管系學習輔導 AI，幫助學生透過逐步引導方式理解考題與資管系相關知識，確保學生真正掌握概念，而不只是背誦答案。\n", "\n", " **回應方式**：\n", "1️ **逐題解答**：\n", "   - 你應該先用繁體中文複誦題目，確認題目方向，然後開始教學。\n", "   - 你應該**一題一題解釋**，確保學生完全理解一題後，才進行下一題。\n", "   - **不可一次提供所有答案**，必須等待學生回答後再決定下一步。\n", "\n", "2️ **分類考題內容**：\n", "   - 幫我找出該題目屬於的**科目、知識點、章節、考試內容涵義**。\n", "   - 提供**額外補充知識**（如常見錯誤、考試技巧）。\n", "\n", "3️ **引導式學習（Socratic Method）**：\n", "   - 你的回答應該使用**逐步提問**，讓學生**自己思考出答案**，而非直接給出解答。\n", "   - 當學生答錯時，不應直接告知答案，而應**一步步拆解問題，引導學生找到錯誤點**。\n", "   - **舉例 + 類比**：使用生活化的舉例，幫助學生理解抽象概念。\n", "\n", "4️ **適應學生程度（動態調整）**：\n", "   - 你需要根據學生的回答，判斷學生的**掌握度、錯誤點、學習能力**，並調整問題難度。\n", "   - 若學生顯示出不理解，請將概念拆解為**更小步驟**，並用更淺顯的方式解釋。\n", "   - 若學生已經理解，則可以適度提高難度，挑戰更進階題目。\n", "\"\"\"\n", "\n", "# ✅ 定義 AI 回應函式\n", "def ask_ai(student_answer, is_first_attempt, original_question=\"\", context=\"\"):\n", "    if is_first_attempt:\n", "        prompt = f\"\"\"\n", "            {PROMPT}\n", "            **考題**：{original_question}\n", "            **學生回答**：{student_answer}\n", "            **請提供回饋與指導**：\n", "            1.指出答案是否正確，並簡單解釋原因  \n", "            2.若答案錯誤，給予**適當的提示**，但不直接給出答案  \n", "            3.讓學生思考，而不是直接提供最終答案  \n", "            \"\"\"\n", "    else:\n", "        prompt = f\"\"\"\n", "            {PROMPT}\n", "             **對話上下文**：{context}\n", "             **學生最新回答**：{student_answer}\n", "\n", "            **請繼續提供引導**：\n", "            1.回應學生的理解程度，並鼓勵進一步思考  \n", "            2.若學生仍然錯誤，提供更多提示或舉例，但避免直接給答案  \n", "            3.讓對話保持自然，讓學生自己推理出正確答案  \n", "          \"\"\"\n", "    model = genai.GenerativeModel(\"Gemini-1.5-flash\")\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 互動式學習系統\n", "original_question = input(\"請輸入考題：(什麼是關聯式資料庫?)\")\n", "is_first_attempt = True\n", "\n", "while True:\n", "    student_answer = input(\"\\n💬 你的回答：\")\n", "    response = ask_ai(student_answer, is_first_attempt, original_question)\n", "\n", "    print(\"\\n🔹 AI 回應：\")\n", "    print(response)\n", "\n", "    # 設定之後的回合不再重複題目說明\n", "    is_first_attempt = False\n", "\n", "    # 確認學生是否要繼續這題\n", "    continue_input = input(\"你是否想繼續這題？(y/n): \").strip().lower()\n", "    if continue_input == 'n':\n", "        break"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 題目已存入向量資料庫！\n"]}], "source": ["import faiss\n", "import numpy as np\n", "import json\n", "\n", "# **🔹 題庫 JSON**\n", "qa_data = [\n", "    {\"id\": 1, \"question\": \"2+2 等於多少？\", \"answer\": \"4\", \"subject\": \"數學\", \"year\": 2024, \"school\": \"臺灣大學\"},\n", "    {\"id\": 2, \"question\": \"RAG 代表什麼？\", \"answer\": \"檢索增強生成\", \"subject\": \"資訊科學\", \"year\": 2023, \"school\": \"清華大學\"},\n", "    {\"id\": 3, \"question\": \"向量資料庫是做什麼的？\", \"answer\": \"儲存向量資料，進行相似度比對\", \"subject\": \"人工智慧\", \"year\": 2024, \"school\": \"交通大學\"},\n", "]\n", "\n", "# **🔹 把題目轉換成向量（這裡用隨機向量模擬，實際應用時可用 AI Embedding）**\n", "def text_to_vector(text):\n", "    return np.random.rand(128).astype(\"float32\")  # 128 維向量\n", "\n", "# **🔹 建立 FAISS 向量資料庫**\n", "dimension = 128\n", "index = faiss.IndexFlatL2(dimension)  # L2 距離索引\n", "question_vectors = np.array([text_to_vector(q[\"question\"]) for q in qa_data])\n", "index.add(question_vectors)  # 存入向量\n", "\n", "print(\"✅ 題目已存入向量資料庫！\")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❓ 問題：RAG 代表什麼？\n", "❌ 錯了！正確答案是：檢索增強生成\n"]}], "source": ["# **🔹 讓用戶選擇要考哪一年的哪一科**\n", "selected_year = int(input(\"👉 請輸入考題年份（如 2024）：\"))\n", "selected_subject = input(\"👉 請輸入考試科目（如 數學）：\")\n", "\n", "# **篩選符合條件的題目**\n", "filtered_questions = [q for q in qa_data if q[\"year\"] == selected_year and q[\"subject\"] == selected_subject]\n", "\n", "if not filtered_questions:\n", "    print(\"❌ 沒有符合條件的考題！\")\n", "else:\n", "    # **隨機選擇一道題目**\n", "    selected_question = np.random.choice(filtered_questions)\n", "    print(f\"❓ 問題：{selected_question['question']}\")\n", "    user_answer = input(\"👉 你的答案：\")\n", "\n", "    # **比對答案**\n", "    if user_answer.strip() == selected_question[\"answer\"]:\n", "        print(\"✅ 答對了！\")\n", "    else:\n", "        print(f\"❌ 錯了！正確答案是：{selected_question['answer']}\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    \"school\": \"國立交通大學\",\n", "    \"department\": \"土木工程系碩士班己組\",\n", "    \"year\": \"102\",\n", "    \"question_number\": \"1\",\n", "    \"question_text\": \"請定義「擴增實境」(Augmented Reality)與「虛擬實境」(Virtual Reality),舉出二者的應用實例，並說明二者之間的主要差異為何。(10%)\",\n", "    \"options\": [],\n", "    \"type\": \"short-answer\",\n", "    \"image_file\": [],\n", "    \"image_regions\": []\n", "  },\n", "  {\n", "    \"school\": \"國立交通大學\",\n", "    \"department\": \"土木工程系碩士班己組\",\n", "    \"year\": \"102\",\n", "    \"question_number\": \"2\",\n", "    \"question_text\": \"請針對某大電子工廠(員工數約1萬人)的門禁與工時管制系統繪製並說明其系統架構。系統包括:(a)員工識別證RFID;(b)指紋辨識;(c)大門開啟;(d)每位員工容許進出時段比對;(e)進出時間記錄;(f)系統故障時人工通報與大門開啟。(15%)\",\n", "    \"options\": [],\n", "    \"type\": \"short-answer\",\n", "    \"image_file\": [],\n", "    \"image_regions\": []\n", "  },\n", "  {\n", "    \"school\": \"國立交通大學\",\n", "    \"department\": \"土木工程系碩士班己組\",\n", "    \"year\": \"102\",\n", "    \"question_number\": \"3\",\n", "    \"question_text\": \"財政部的綜合所得稅資料庫中有國人(Name)各年度的收入淨額(Income)和繳交所得稅(Tax)的資料。針對高(>1,500,000)、中(300,000~1,500,000)、低(<300,000)所得的區隔,撰寫一段程式統計高、中、低各所得區塊的「人數」、「平均所得」;並計算全國最高所得的前10%與最低所得的後10%的平均所得比例。不限程式語言種類亦可用pseudo code;必要之,請附上程式碼的執行結果。(15%)\",\n", "    \"options\": [],\n", "    \"type\": \"short-answer\",\n", "    \"image_file\": [],\n", "    \"image_regions\": []\n", "  }\n", "]\n"]}], "source": ["import json\n", "import re\n", "\n", "def extract_json_from_response(text: str):\n", "    try:\n", "        # 嘗試直接解析整段內容為 JSON\n", "        return json.loads(text)\n", "    except json.JSONDecodeError:\n", "        pass  # 失敗再嘗試從中提取\n", "\n", "    # 正則方式找 JSON 陣列\n", "    match = re.search(r'\\[\\s*{.*}\\s*]', text, re.DOTALL)\n", "    if match:\n", "        json_str = match.group(0)\n", "        try:\n", "            return json.loads(json_str)\n", "        except Exception as e:\n", "            print(f\"❌ 無法解析正則擷取的 JSON：{e}\")\n", "            return []\n", "\n", "    print(\"⚠️ 無法在文字中找到有效 JSON 陣列\")\n", "    return []\n", "example_text = \"\"\"[\n", "  {\n", "    \"school\": \"國立交通大學\",\n", "    \"department\": \"土木工程系碩士班己組\",\n", "    \"year\": \"102\",\n", "    \"question_number\": \"1\",\n", "    \"question_text\": \"請定義「擴增實境」(Augmented Reality)與「虛擬實境」(Virtual Reality),舉出二者的應用實例，並說明二者之間的主要差異為何。(10%)\",\n", "    \"options\": [],\n", "    \"type\": \"short-answer\",\n", "    \"image_file\": [],\n", "    \"image_regions\": []\n", "  },\n", "  {\n", "    \"school\": \"國立交通大學\",\n", "    \"department\": \"土木工程系碩士班己組\",\n", "    \"year\": \"102\",\n", "    \"question_number\": \"2\",\n", "    \"question_text\": \"請針對某大電子工廠(員工數約1萬人)的門禁與工時管制系統繪製並說明其系統架構。系統包括:(a)員工識別證RFID;(b)指紋辨識;(c)大門開啟;(d)每位員工容許進出時段比對;(e)進出時間記錄;(f)系統故障時人工通報與大門開啟。(15%)\",      \n", "    \"options\": [],\n", "    \"type\": \"short-answer\",\n", "    \"image_file\": [],\n", "    \"image_regions\": []\n", "  },\n", "  {\n", "    \"school\": \"國立交通大學\",\n", "    \"department\": \"土木工程系碩士班己組\",\n", "    \"year\": \"102\",\n", "    \"question_number\": \"3\",\n", "    \"question_text\": \"財政部的綜合所得稅資料庫中有國人(Name)各年度的收入淨額(Income)和繳交所得稅(Tax)的資料。針對高(>1,500,000)、中(300,000~1,500,000)、低(<300,000)所得的區隔,撰寫一段程式統計高、中、低各所得區塊的「人數」、「平均所得」;並計算全國最高所得的前10%與最低所得的後10%的平均所得比例。不限程式語言種類亦可用pseudo code;必要之,請附上程式碼的執行結果。(15%)\",\n", "    \"options\": [],\n", "    \"type\": \"short-answer\", \n", "    \"image_file\": [],\n", "    \"image_regions\": []\n", "  }\n", "]\n", "\"\"\"\n", "\n", "# 測試提取 JSON\n", "extracted_json = extract_json_from_response(example_text)\n", "print(json.dumps(extracted_json, indent=2, ensure_ascii=False))\n"]}], "metadata": {"kernelspec": {"display_name": "transformers", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}