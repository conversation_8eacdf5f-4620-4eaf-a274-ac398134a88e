import torch
from ultralytics import YOLO
from multiprocessing import freeze_support

def main():
    # 使用絕對路徑載入模型
    model = YOLO('yolo11n.pt')

    # 訓練模型
    results = model.train(
        data='test_dataset6/data.yaml',  # 資料設定檔路徑
        epochs=100,  
        batch=16,
        device='0',  # 使用 GPU
        project='runs',
        name='test_dataset6'
    )

    # 可以添加其他訓練後的程式碼，例如驗證或導出模型

if __name__ == '__main__':
    freeze_support()
    main()