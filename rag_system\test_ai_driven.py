#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試完全AI驅動的教學系統
驗證沒有預定義陣列，完全由AI判斷
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from smart_tutor import SmartTutor

def test_ai_analysis():
    """測試AI分析功能"""
    print("🧪 測試AI驅動的學生回答分析")
    print("="*60)
    
    tutor = SmartTutor()
    tutor.current_topic = "什麼是銀行家演算法？"
    
    test_responses = [
        "死鎖指系統陷入僵局",
        "資源競爭",
        "我不知道",
        "銀行家演算法是用來防止死鎖的",
        "它像銀行放貸一樣會評估風險",
        "不太懂",
        "可以避免系統卡住"
    ]
    
    for i, response in enumerate(test_responses, 1):
        print(f"\n🔍 測試 {i}: 學生回答「{response}」")
        print("-" * 40)
        
        try:
            analysis = tutor.analyze_student_response(response)
            
            print("📊 AI分析結果:")
            print(f"  理解程度: {analysis.get('understanding_level')}/5")
            print(f"  回答分析: {analysis.get('response_analysis')}")
            print(f"  學生優點: {analysis.get('student_strengths')}")
            print(f"  需要改進: {analysis.get('areas_to_improve')}")
            print(f"  下一步重點: {analysis.get('next_teaching_focus')}")
            print(f"  教學方式: {analysis.get('best_teaching_approach')}")
            
        except Exception as e:
            print(f"❌ 分析失敗: {e}")

def test_conversation_flow():
    """測試對話流程"""
    print("\n🗣️ 測試AI驅動的對話流程")
    print("="*60)
    
    tutor = SmartTutor()
    
    # 模擬完整對話
    conversation = [
        ("什麼是銀行家演算法？", True),  # 新問題
        ("死鎖指系統陷入僵局", False),    # 學生回答
        ("資源競爭", False),           # 學生回答
        ("我不知道", False),           # 學生回答
        ("可以避免死鎖", False)        # 學生回答
    ]
    
    for i, (input_text, is_new) in enumerate(conversation, 1):
        print(f"\n🔄 對話輪次 {i}")
        print(f"{'🤔 學生問題' if is_new else '💭 學生回答'}: {input_text}")
        
        try:
            response = tutor.generate_smart_response(input_text, is_new_question=is_new)
            print(f"🎓 老師回應: {response}")
            
            # 記錄對話
            tutor.record_conversation(input_text, response)
            
        except Exception as e:
            print(f"❌ 回應生成失敗: {e}")

def test_no_predefined_arrays():
    """驗證沒有使用預定義陣列"""
    print("\n🔍 驗證系統沒有使用預定義陣列")
    print("="*60)
    
    tutor = SmartTutor()
    
    # 檢查是否還有預定義的分類
    if hasattr(tutor, 'question_types'):
        print("❌ 發現預定義的 question_types")
        print(f"內容: {tutor.question_types}")
    else:
        print("✅ 沒有發現 question_types 預定義陣列")
    
    # 檢查其他可能的預定義陣列
    predefined_attrs = [
        'question_types', 'teaching_strategies', 'response_templates',
        'concept_categories', 'difficulty_levels'
    ]
    
    found_predefined = []
    for attr in predefined_attrs:
        if hasattr(tutor, attr):
            found_predefined.append(attr)
    
    if found_predefined:
        print(f"❌ 發現預定義陣列: {found_predefined}")
    else:
        print("✅ 確認沒有預定義陣列，完全AI驅動")

def main():
    """主測試函數"""
    print("🎯 AI驅動教學系統測試")
    print("="*70)
    print("目標：驗證系統完全由AI判斷，沒有預定義分類")
    
    try:
        # 驗證沒有預定義陣列
        test_no_predefined_arrays()
        
        # 測試AI分析
        test_ai_analysis()
        
        # 測試對話流程
        test_conversation_flow()
        
        print("\n🎉 測試完成！")
        print("\n💡 系統特色:")
        print("1. 🧠 完全AI驅動 - 沒有預定義分類陣列")
        print("2. 🎯 智能分析 - AI自主判斷學生理解程度")
        print("3. 🔄 動態調整 - 根據AI分析調整教學策略")
        print("4. 💭 自然對話 - AI生成自然的教學回應")
        
        print("\n🚀 現在可以運行: python rag_main.py")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
