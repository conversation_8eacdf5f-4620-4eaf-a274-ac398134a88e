#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試基於上下文的教學系統
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from context_tutor import ContextTutor

def test_banker_algorithm():
    """測試銀行家演算法對話"""
    print("🧪 測試銀行家演算法對話流程")
    print("="*60)
    
    tutor = ContextTutor()
    
    # 開始新問題
    print("🤔 學生問題: 什麼是銀行家演算法？")
    response1 = tutor.start_new_question("什麼是銀行家演算法？")
    print(f"🎓 老師回應: {response1}")
    print(f"📊 原始問題: {tutor.original_question}")
    
    # 學生回答1
    print("\n💭 學生回答: 死鎖指系統陷入僵局")
    response2 = tutor.continue_conversation("死鎖指系統陷入僵局")
    print(f"🎓 老師回應: {response2}")
    
    # 學生回答2
    print("\n💭 學生回答: 利用資源分配圖確保不會有迴圈情況發生")
    response3 = tutor.continue_conversation("利用資源分配圖確保不會有迴圈情況發生")
    print(f"🎓 老師回應: {response3}")
    
    # 學生回答3
    print("\n💭 學生回答: 先給p1執行讓p2等待，等到p1釋放資源執行完後才換p2")
    response4 = tutor.continue_conversation("先給p1執行讓p2等待，等到p1釋放資源執行完後才換p2")
    print(f"🎓 老師回應: {response4}")
    
    print(f"\n📊 最終狀態:")
    print(f"原始問題: {tutor.original_question}")
    print(f"上下文長度: {len(tutor.context)} 字符")

def test_context_management():
    """測試上下文管理"""
    print("\n🧪 測試上下文管理")
    print("="*60)
    
    tutor = ContextTutor()
    
    # 測試狀態查詢
    print("🔍 測試 status 命令:")
    status = tutor.get_status()
    print(status)
    
    # 開始問題
    tutor.start_new_question("什麼是死鎖？")
    print(f"開始問題後 - 原始問題: {tutor.original_question}")
    
    # 重置測試
    tutor.reset()
    print(f"重置後 - 原始問題: {tutor.original_question}")

def main():
    """主測試"""
    try:
        # 測試銀行家演算法對話
        test_banker_algorithm()
        
        # 測試上下文管理
        test_context_management()
        
        print("\n✅ 測試完成！")
        print("\n💡 檢查要點:")
        print("1. 是否始終記住原始問題")
        print("2. 回應是否簡潔不重複")
        print("3. 是否沒有截斷問題")
        print("4. 日誌是否完全關閉")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
